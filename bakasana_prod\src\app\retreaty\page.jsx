import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

import {
  HeroTitle,
  SectionTitle,
  CardTitle,
  BodyText,
} from '@/components/ui/UnifiedTypography';

import AdvancedSEO from '@/components/SEO/AdvancedSEO';

import { generateRetreatStructuredData } from '@/lib/yogaStructuredData';
import { getAllDestinations, destinations } from '@/data/programData';

import { metadata } from './metadata';

export { metadata };
export default async function RetreatyPage() {
  const pageMetadata = {
    title:
      'Retreaty Jogi 2025 - Bali & Sri Lanka | Transformacyjne Podróże BAKASANA',
    description:
      'Odkryj najlepsze retreaty jogi z certyfikowaną instruktorką Julią Jakubowicz. Bali (Ubud, Canggu, Gili Air) i Sri Lanka (Sigiriya, Południe). Małe grupy, luksusowe hotele, transformacyjne doświadczenia.',
    keywords: [
      'retreaty jogi 2025',
      'retreat jogi bali',
      'retreat jogi sri lanka',
      'julia j<PERSON><PERSON> joga',
      'transformacyjne podróże',
      'ubud yoga retreat',
      'canggu retreat',
      'gili air joga',
      'sigiriya yoga',
      'małe grupy retreat',
      'luksusowe retreaty',
      'duchowe wakacje azja',
    ],
    structuredData: {
      '@context': 'https://schema.org',
      '@type': 'TravelAgency',
      name: 'BAKASANA Retreaty Jogi',
      description:
        'Retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz',
      offers: {
        '@type': 'Offer',
        category: 'Yoga Retreats',
        priceRange: '2900-4200 PLN',
      },
    },
    canonicalUrl: 'https://bakasana-travel.blog/retreaty',
    imageUrl:
      'https://bakasana-travel.blog/images/og/retreaty-overview-2025.jpg',
  };

  const allDestinations = getAllDestinations();

  // Organize destinations by country
  const baliDestinations = allDestinations.filter(
    dest => dest.country === 'Bali'
  );
  const sriLankaDestinations = allDestinations.filter(
    dest => dest.country === 'Sri Lanka'
  );

  const coreValues = [
    {
      title: 'Autentyczność',
      description:
        'Głęboka połączenie z tradycyjną praktyką jogi i lokalną kulturą',
    },
    {
      title: 'Jakość',
      description:
        'Starannie wybrane lokalizacje i najwyższe standardy komfortu',
    },
    {
      title: 'Transformacja',
      description: 'Holistyczne podejście do rozwoju osobistego i duchowego',
    },
    {
      title: 'Wspólnota',
      description: 'Intymne grupy sprzyjające głębokim połączeniom',
    },
  ];

  const upcomingRetreats = [
    {
      destination: 'Bali - Ubud',
      dates: '15-25 marca 2025',
      price: '3200 PLN',
      spots: '4 wolne miejsca',
      highlight: true,
    },
    {
      destination: 'Sri Lanka - Południe',
      dates: '10-20 maja 2025',
      price: '2900 PLN',
      spots: '6 wolnych miejsc',
      highlight: false,
    },
    {
      destination: 'Bali - Gili Air',
      dates: '15-25 czerwca 2025',
      price: '3400 PLN',
      spots: 'Wkrótce otwarte',
      highlight: false,
    },
  ];

  return (
    <main className='bg-sanctuary min-h-screen'>
      {/* Advanced SEO Component */}
      <AdvancedSEO
        title={pageMetadata.title}
        description={pageMetadata.description}
        keywords={pageMetadata.keywords}
        structuredData={[pageMetadata.structuredData]}
        canonicalUrl={pageMetadata.canonicalUrl}
        imageUrl={pageMetadata.imageUrl}
      />

      {/* HERO SECTION - Old Money Elegance */}
      <section className='py-section md:py-section-lg px-hero-padding lg:px-hero-padding'>
        <div className='max-w-4xl mx-auto text-center'>
          <div className='w-16 h-px bg-charcoal-gold/40 mx-auto mb-lg md:mb-xl animate-fade-in'></div>

          <h1
            className='font-primary text-4xl md:text-6xl lg:text-7xl font-light text-charcoal mb-md md:mb-lg leading-tight tracking-wide /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */' /* TODO: Replace with HeroTitle */
          >
            Retreaty
          </h1>

          <p className='text-base md:text-lg text-charcoal/70 font-secondary mb-xl md:mb-2xl max-w-2xl mx-auto leading-relaxed italic'>
            Duchowe podróże na Bali i Sri Lanka
          </p>

          <div className='text-xs text-charcoal/50 font-secondary tracking-widest uppercase'>
            Transformacyjne doświadczenia z certyfikowaną instruktorką jogi
          </div>

          <div className='w-16 h-px bg-charcoal-gold/40 mx-auto mt-lg md:mt-xl animate-fade-in'></div>
        </div>
      </section>

      {/* CORE VALUES - Minimalist Elegance */}
      <section className='py-section px-hero-padding lg:px-hero-padding'>
        <div className='max-w-6xl mx-auto'>
          <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-2xl lg:gap-20'>
            {coreValues.map((value, index) => (
              <div
                key={index}
                className='text-center group opacity-0 animate-fade-in'
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className='mb-lg'>
                  <div className='w-16 h-16 mx-auto border border-charcoal-gold/20 flex items-center justify-center group-hover:border-charcoal-gold/40 transition-all duration-500'>
                    <div className='w-3 h-3 bg-charcoal-gold/60 group-hover:bg-charcoal-gold/80 group-hover:scale-125 transition-all duration-500'></div>
                  </div>
                </div>
                <h3 className='font-primary text-xl font-light text-charcoal mb-md tracking-wide /* TODO: Replace with CardTitle */'>
                  {value.title}
                </h3>
                <p className='text-sm text-charcoal/60 font-secondary leading-relaxed max-w-48 mx-auto'>
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* DESTINATIONS - Sophisticated Grid */}
      <section className='py-section px-hero-padding lg:px-hero-padding'>
        <div className='max-w-7xl mx-auto'>
          <div className='text-center mb-20'>
            <h2
              className='font-primary text-4xl md:text-5xl font-light text-charcoal mb-lg /* TODO: Replace with HeroTitle */' /* TODO: Replace with SectionTitle */
            >
              Nasze Destinacje
            </h2>
            <div className='w-16 h-px bg-charcoal-gold/40 mx-auto mb-lg'></div>
            <p className='text-lg text-charcoal/70 font-secondary max-w-2xl mx-auto leading-relaxed'>
              Starannie wybrane lokalizacje oferujące autentyczne połączenie
              praktyki jogi z odkrywaniem najpiękniejszych zakątków duchowej
              Azji
            </p>
          </div>

          {/* Bali Section */}
          <div className='mb-20'>
            <div className='flex items-center justify-center mb-xl'>
              <div className='flex-1 h-px bg-charcoal-gold/20'></div>
              <SectionTitle level={3}>Bali</SectionTitle>
              <div className='flex-1 h-px bg-charcoal-gold/20'></div>
            </div>

            <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-xl'>
              {baliDestinations.map((destination, index) => (
                <Link
                  key={destination.id}
                  href={`/program?destination=${destination.id}`}
                  className='group block opacity-0 animate-fade-in'
                  style={{ animationDelay: `${index * 0.15}s` }}
                >
                  <div className='relative h-96 overflow-hidden mb-lg'>
                    <Image
                      src={destination.image}
                      alt={`Retreat jogi ${destination.name}`}
                      fill
                      className='object-cover object-center transition-transform duration-700 group-hover:scale-105 grayscale-[0.1] group-hover:grayscale-0'
                      sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
                    />
                    <div className='absolute inset-0 bg-gradient-to-t from-charcoal/20 via-transparent to-transparent group-hover:from-charcoal/40 transition-all duration-500' />
                  </div>

                  <div className='text-center'>
                    <h4 className='font-primary text-xl font-light text-charcoal mb-3 group-hover:text-charcoal-gold transition-colors duration-300'>
                      {destination.name}
                    </h4>
                    <p className='text-sm text-charcoal/60 font-secondary mb-sm tracking-wide'>
                      {destination.duration}
                    </p>
                    <div className='text-charcoal-gold font-primary font-light text-lg tracking-wide'>
                      {destination.price || 'Od 3200 PLN'}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Sri Lanka Section */}
          <div>
            <div className='flex items-center justify-center mb-xl'>
              <div className='flex-1 h-px bg-charcoal-gold/20'></div>
              <SectionTitle level={3}>Sri Lanka</SectionTitle>
              <div className='flex-1 h-px bg-charcoal-gold/20'></div>
            </div>

            <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-xl'>
              {sriLankaDestinations.map((destination, index) => (
                <Link
                  key={destination.id}
                  href={`/program?destination=${destination.id}`}
                  className='group block opacity-0 animate-fade-in'
                  style={{
                    animationDelay: `${(index + baliDestinations.length) * 0.15}s`,
                  }}
                >
                  <div className='relative h-96 overflow-hidden mb-lg'>
                    <Image
                      src={destination.image}
                      alt={`Retreat jogi ${destination.name}`}
                      fill
                      className='object-cover object-center transition-transform duration-700 group-hover:scale-105 grayscale-[0.1] group-hover:grayscale-0'
                      sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
                    />
                    <div className='absolute inset-0 bg-gradient-to-t from-charcoal/20 via-transparent to-transparent group-hover:from-charcoal/40 transition-all duration-500' />
                  </div>

                  <div className='text-center'>
                    <h4 className='font-primary text-xl font-light text-charcoal mb-3 group-hover:text-charcoal-gold transition-colors duration-300'>
                      {destination.name}
                    </h4>
                    <p className='text-sm text-charcoal/60 font-secondary mb-sm tracking-wide'>
                      {destination.duration}
                    </p>
                    <div className='text-charcoal-gold font-primary font-light text-lg tracking-wide'>
                      {destination.price || 'Od 2900 PLN'}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* UPCOMING RETREATS - Elegant Showcase */}
      <section className='py-section px-hero-padding lg:px-hero-padding bg-sanctuary/30'>
        <div className='max-w-5xl mx-auto'>
          <div className='text-center mb-2xl'>
            <h2
              className='font-primary text-4xl md:text-5xl font-light text-charcoal mb-md' /* TODO: Replace with SectionTitle */
            >
              Nadchodzące Retreaty
            </h2>
            <div className='w-16 h-px bg-charcoal-gold/40 mx-auto mb-lg'></div>
            <p className='text-lg text-charcoal/70 font-secondary max-w-2xl mx-auto leading-relaxed'>
              Zarezerwuj swoje miejsce w jednym z najbliższych wyjazdów
            </p>
          </div>

          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-lg'>
            {upcomingRetreats.map((retreat, index) => (
              <div
                key={index}
                className={`p-8 text-center transition-all duration-500 hover:transform hover:-translate-y-2 ${
                  retreat.highlight
                    ? 'bg-sanctuary border-l-2 border-charcoal-gold'
                    : 'bg-sanctuary/50'
                }`}
              >
                <div className='space-y-sm'>
                  <h3 className='font-primary text-xl font-light text-charcoal'>
                    {retreat.destination}
                  </h3>

                  <div className='space-y-2 text-sm text-charcoal/60 font-secondary'>
                    <div>{retreat.dates}</div>
                    <div className='text-charcoal-gold font-primary'>
                      Od {retreat.price}
                    </div>
                    <div
                      className={
                        retreat.spots.includes('Wkrótce')
                          ? 'text-charcoal/40'
                          : 'text-sage-green'
                      }
                    >
                      {retreat.spots}
                    </div>
                  </div>

                  <div className='pt-4'>
                    <Link
                      href='/rezerwacja'
                      className='inline-block border border-charcoal-gold/40 text-charcoal-gold px-hero-padding py-3 text-sm font-secondary tracking-wide transition-all duration-300 hover:bg-charcoal-gold hover:text-sanctuary'
                    >
                      {retreat.spots.includes('Wkrótce')
                        ? 'Lista Oczekujących'
                        : 'Rezerwuj Miejsce'}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* TESTIMONIALS - Refined Elegance */}
      <section className='py-section-lg px-hero-padding lg:px-hero-padding'>
        <div className='max-w-4xl mx-auto text-center'>
          <div className='w-24 h-px bg-charcoal-gold/40 mx-auto mb-2xl'></div>

          <blockquote className='font-primary text-2xl md:text-3xl lg:text-4xl font-light text-charcoal leading-relaxed mb-xl italic /* TODO: Replace with SectionTitle */ /* TODO: Replace with SectionTitle */'>
            "Każda podróż zaczyna się od jednego kroku, każda transformacja od
            jednej decyzji..."
          </blockquote>

          <div className='w-24 h-px bg-charcoal-gold/40 mx-auto'></div>
        </div>
      </section>

      {/* CALL TO ACTION - Sophisticated Elegance */}
      <section className='py-section-lg px-hero-padding lg:px-hero-padding bg-charcoal text-sanctuary'>
        <div className='max-w-4xl mx-auto text-center'>
          <h2
            className='font-primary text-4xl md:text-5xl font-light mb-xl' /* TODO: Replace with SectionTitle */
          >
            Gotowa na
            <span className='text-charcoal-gold block mt-2'>
              Transformację?
            </span>
          </h2>

          <p className='text-lg text-sanctuary/80 font-secondary mb-2xl leading-relaxed max-w-2xl mx-auto'>
            Rozpocznij swoją podróż ku głębszemu zrozumieniu siebie i odnajdź
            wewnętrzny spokój w przepięknych zakątkach duchowej Azji
          </p>

          <div className='flex flex-col sm:flex-row gap-lg justify-center items-center'>
            <Link
              href='/rezerwacja'
              className='border border-charcoal-gold text-charcoal-gold px-16 py-4 text-sm font-secondary tracking-widest transition-all duration-500 hover:bg-charcoal-gold hover:text-charcoal hover:transform hover:-translate-y-1'
            >
              ZAREZERWUJ KONSULTACJĘ
            </Link>
            <Link
              href='/kontakt'
              className='border border-sanctuary/30 text-sanctuary/80 px-16 py-4 text-sm font-secondary tracking-widest transition-all duration-500 hover:bg-sanctuary/10 hover:text-sanctuary hover:transform hover:-translate-y-1'
            >
              POROZMAWIAJMY
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
