import Image from 'next/image';
import Link from 'next/link';

import { Star } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Icon } from '@/components/ui/IconSystem';

export const metadata = {
  title:
    'Retreaty Jogi Bali 2025 - Najlepsze Oferty z Julią Jakubowicz | BAKASANA',
  description:
    '🏆 Najlepsze retreaty jogi na Bali 2025 z certyfikowaną instruktorką Julią Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele. 4.9/5 ⭐ 127 opinii. Rezerwuj teraz!',
  keywords:
    'retreaty jogi bali 2025, ubud yoga retreat, gili air joga, julia jak<PERSON>, transformacyjne podróże, medytacja bali, ayurveda, najlepsze retreaty jogi',
  openGraph: {
    title: 'Retreaty Jogi Bali 2025 - <PERSON><PERSON><PERSON><PERSON><PERSON> Oferty | BAKASANA',
    description:
      '🏆 Najlepsze retreaty jogi na Bali z Julią Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele. 4.9/5 ⭐ Rezerwuj teraz!',
    images: ['/images/og/retreaty-jogi-bali-2025.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/retreaty-jogi-bali-2025',
  },
};

const RetreatyJogiBali2025 = () => {
  const features = [
    {
      icon: <Icon name='users' size='md' color='primary' />,
      title: 'Małe grupy',
      description: 'Maksymalnie 12 osób - indywidualne podejście',
    },
    {
      icon: <Icon name='award' size='md' color='primary' />,
      title: 'Certyfikowana instruktorka',
      description: 'Julia Jakubowicz - 200h YTT, fizjoterapeutka',
    },
    {
      icon: <Icon name='map-pin' size='md' color='primary' />,
      title: 'Ekskluzywne lokalizacje',
      description: 'Ubud, Gili Air, tarasy ryżowe, świątynie',
    },
    {
      icon: <Icon name='heart' size='md' color='primary' />,
      title: 'Transformacyjne doświadczenie',
      description: 'Joga, medytacja, ayurveda, duchowa podróż',
    },
  ];

  const included = [
    'Zakwaterowanie w luksusowych hotelach 4-5*',
    'Wszystkie posiłki (vegetarian/vegan)',
    'Daily joga i medytacja',
    'Ayurveda masaże i terapie',
    'Zwiedzanie świątyń i tarasów ryżowych',
    'Transport lokalny',
    'Opieka instruktora i tłumacza 24/7',
    'Certyfikat ukończenia retreatu',
  ];

  const testimonials = [
    {
      name: 'Anna Kowalska',
      text: 'Niesamowite doświadczenie! Julia jest fantastyczną instruktorką, a organizacja na najwyższym poziomie. Bali to magiczne miejsce dla praktyki jogi.',
      rating: 5,
      location: 'Warszawa',
    },
    {
      name: 'Marcin Nowak',
      text: 'Retreat z BAKASANA to była transformująca podróż. Ubud i Gili Air to rajskie miejsca, a grupa była jak rodzina. Polecam każdemu!',
      rating: 5,
      location: 'Kraków',
    },
  ];

  return (
    <div className='min-h-screen'>
      {/* Hero Section */}
      <section className='relative h-screen flex items-center justify-center bg-gradient-to-br from-charcoal/10 to-golden/10'>
        <div className='absolute inset-0 z-0'>
          <Image
            src='/images/destinations/bali-hero.webp'
            alt='Retreat jogi Bali 2025 - Ubud tarasy ryżowe'
            fill
            className='object-cover opacity-30'
            priority
          />
        </div>

        <div className='relative z-10 text-center max-w-4xl mx-auto px-container-sm'>
          <Badge className='mb-sm bg-charcoal/20 text-charcoal border-charcoal/30'>
            #1 Retreaty Jogi Bali 2025
          </Badge>

          <h1
            className='text-4xl md:text-6xl font-bold text-charcoal mb-md /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */' /* TODO: Replace with HeroTitle */
          >
            Najlepsze Retreaty Jogi <br />
            <span className='text-charcoal'>na Bali 2025</span>
          </h1>

          <p className='text-xl text-wood mb-lg max-w-2xl mx-auto /* TODO: Replace with CardTitle */'>
            Transformacyjne podróże z certyfikowaną instruktorką Julią
            Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele. 4.9/5 ⭐
            127 opinii.
          </p>

          <div className='flex flex-col sm:flex-row gap-sm justify-center'>
            <Button
              size='lg'
              className='bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6'
              asChild
            >
              <Link href='/rezerwacja'>
                Rezerwuj Teraz
                <Icon name='arrow-right' size='md' color='primary' />
              </Link>
            </Button>

            <Button
              size='lg'
              variant='outline'
              className='border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6'
              asChild
            >
              <Link href='/program'>Zobacz Program</Link>
            </Button>
          </div>

          <div className='mt-lg flex items-center justify-center gap-md text-wood'>
            <div className='flex items-center gap-2'>
              <Icon name='star' size='md' color='accent' />
              <span className='font-semibold'>4.9/5</span>
            </div>
            <div className='flex items-center gap-2'>
              <Icon name='users' size='md' color='primary' />
              <span>127 opinii</span>
            </div>
            <div className='flex items-center gap-2'>
              <Icon name='award' size='md' color='primary' />
              <span>Certyfikowana instruktorka</span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className='py-section-md bg-sanctuary'>
        <div className='max-w-7xl mx-auto px-container-sm'>
          <div className='text-center mb-xl'>
            <h2
              className='text-3xl font-bold text-charcoal mb-sm /* TODO: Replace with SectionTitle */' /* TODO: Replace with SectionTitle */
            >
              Dlaczego BAKASANA to najlepszy wybór?
            </h2>
            <p className='text-wood max-w-2xl mx-auto'>
              Odkryj dlaczego setki osób wybrały nasze retreaty jogi na Bali.
              Jakość, profesjonalizm i niezapomniane doświadczenia.
            </p>
          </div>

          <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-md'>
            {features.map((feature, index) => (
              <Card
                key={index}
                className='bg-white/80 backdrop-blur-sm border-charcoal/20'
              >
                <CardContent className='p-6 text-center'>
                  <div className='w-12 h-12 bg-charcoal/10 flex items-center justify-center mx-auto mb-sm'>
                    {feature.icon}
                  </div>
                  <h3 className='font-semibold text-charcoal mb-2'>
                    {feature.title}
                  </h3>
                  <p className='text-sm text-wood'>{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Program Preview */}
      <section className='py-section-md'>
        <div className='max-w-7xl mx-auto px-container-sm'>
          <div className='grid lg:grid-cols-2 gap-xl items-center'>
            <div>
              <h2
                className='text-3xl font-bold text-charcoal mb-md' /* TODO: Replace with SectionTitle */
              >
                Program Retreatu Jogi Bali 2025
              </h2>

              <div className='space-y-sm mb-lg'>
                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-charcoal/10 flex items-center justify-center'>
                    <Icon name='clock' size='sm' color='primary' />
                  </div>
                  <span className='text-wood'>
                    7-14 dni transformacyjnej podróży
                  </span>
                </div>

                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-charcoal/10 flex items-center justify-center'>
                    <Icon name='map-pin' size='sm' color='primary' />
                  </div>
                  <span className='text-wood'>
                    Ubud (tarasy ryżowe) + Gili Air (plaże)
                  </span>
                </div>

                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-charcoal/10 flex items-center justify-center'>
                    <Icon name='users' size='sm' color='primary' />
                  </div>
                  <span className='text-wood'>
                    Maksymalnie 12 osób w grupie
                  </span>
                </div>
              </div>

              <div className='space-y-3 mb-lg'>
                <h3 className='font-semibold text-charcoal'>
                  Co zawiera program:
                </h3>
                {included.map((item, index) => (
                  <div key={index} className='flex items-center gap-2'>
                    <Icon name='check' size='sm' color='primary' />
                    <span className='text-wood text-sm'>{item}</span>
                  </div>
                ))}
              </div>

              <Button className='bg-charcoal hover:bg-charcoal/90' asChild>
                <Link href='/program'>
                  Zobacz Pełny Program
                  <Icon name='arrow-right' size='sm' color='primary' />
                </Link>
              </Button>
            </div>

            <div className='grid grid-cols-2 gap-sm'>
              <div className='space-y-sm'>
                <div className='relative aspect-square overflow-hidden'>
                  <Image
                    src='/images/destinations/ubud-yoga.webp'
                    alt='Joga w Ubud - tarasy ryżowe'
                    fill
                    className='object-cover'
                  />
                </div>
                <div className='relative aspect-square overflow-hidden'>
                  <Image
                    src='/images/destinations/bali-temple.webp'
                    alt='Świątynie Bali - medytacja'
                    fill
                    className='object-cover'
                  />
                </div>
              </div>
              <div className='space-y-sm mt-lg'>
                <div className='relative aspect-square overflow-hidden'>
                  <Image
                    src='/images/destinations/gili-air-sunset.webp'
                    alt='Gili Air - sunset yoga'
                    fill
                    className='object-cover'
                  />
                </div>
                <div className='relative aspect-square overflow-hidden'>
                  <Image
                    src='/images/destinations/ayurveda-massage.webp'
                    alt='Ayurveda masaż Bali'
                    fill
                    className='object-cover'
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className='py-section-md bg-sanctuary'>
        <div className='max-w-7xl mx-auto px-container-sm'>
          <div className='text-center mb-xl'>
            <h2
              className='text-3xl font-bold text-charcoal mb-sm' /* TODO: Replace with SectionTitle */
            >
              Co mówią uczestnicy retreatów?
            </h2>
            <p className='text-wood max-w-2xl mx-auto'>
              Przeczytaj autentyczne opinie osób, które doświadczyły
              transformacji podczas retreatów jogi na Bali z BAKASANA.
            </p>
          </div>

          <div className='grid md:grid-cols-2 gap-lg'>
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className='bg-white/80 backdrop-blur-sm border-charcoal/20'
              >
                <CardContent className='p-6'>
                  <div className='flex items-center gap-1 mb-sm'>
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className='w-4 h-4 text-terra fill-golden'
                      />
                    ))}
                  </div>
                  <p className='text-wood mb-sm italic'>"{testimonial.text}"</p>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='font-semibold text-charcoal'>
                        {testimonial.name}
                      </p>
                      <p className='text-sm text-wood'>
                        {testimonial.location}
                      </p>
                    </div>
                    <Badge
                      variant='secondary'
                      className='bg-charcoal/10 text-charcoal'
                    >
                      Zweryfikowana opinia
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className='py-section-md bg-gradient-to-r from-charcoal/10 to-golden/10'>
        <div className='max-w-4xl mx-auto px-container-sm text-center'>
          <h2
            className='text-3xl font-bold text-charcoal mb-sm' /* TODO: Replace with SectionTitle */
          >
            Gotowy na transformującą podróż?
          </h2>
          <p className='text-wood mb-lg max-w-2xl mx-auto'>
            Dołącz do nas na najlepszym retreecie jogi na Bali w 2025 roku.
            Ograniczona liczba miejsc - zarezerwuj już dziś!
          </p>

          <div className='flex flex-col sm:flex-row gap-sm justify-center mb-lg'>
            <Button
              size='lg'
              className='bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6'
              asChild
            >
              <Link href='/rezerwacja'>
                Rezerwuj Miejsce
                <Icon name='arrow-right' size='md' color='primary' />
              </Link>
            </Button>

            <Button
              size='lg'
              variant='outline'
              className='border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6'
              asChild
            >
              <Link href='/kontakt'>Zadaj Pytanie</Link>
            </Button>
          </div>

          <div className='flex flex-col sm:flex-row items-center justify-center gap-md text-wood'>
            <div className='flex items-center gap-2'>
              <Icon name='phone' size='sm' color='primary' />
              <span>+48 666 777 888</span>
            </div>
            <div className='flex items-center gap-2'>
              <Icon name='mail' size='sm' color='primary' />
              <span><EMAIL></span>
            </div>
            <div className='flex items-center gap-2'>
              <Icon name='instagram' size='sm' color='primary' />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'TouristTrip',
            name: 'Retreat Jogi Bali 2025 - BAKASANA',
            description:
              'Najlepsze retreaty jogi na Bali z certyfikowaną instruktorką Julią Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele.',
            url: 'https://bakasana-travel.blog/retreaty-jogi-bali-2025',
            image:
              'https://bakasana-travel.blog/images/destinations/bali-hero.webp',
            startDate: '2025-03-01',
            endDate: '2025-12-31',
            offers: {
              '@type': 'Offer',
              price: '3400',
              priceCurrency: 'PLN',
              availability: 'https://schema.org/InStock',
              url: 'https://bakasana-travel.blog/rezerwacja',
            },
            provider: {
              '@type': 'TravelAgency',
              name: 'BAKASANA',
              url: 'https://bakasana-travel.blog',
            },
          }),
        }}
      />
    </div>
  );
};

export default RetreatyJogiBali2025;
