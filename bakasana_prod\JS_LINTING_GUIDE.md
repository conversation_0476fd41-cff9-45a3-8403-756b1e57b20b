# 🔧 BAKASANA JAVASCRIPT LINTING & FORMATTING GUIDE

Kompletny przewodnik po ESLint i Prettier w projekcie Bakasana.

## 🚀 Szybkie Komendy

### Napraw wszystkie pliki JS (ZALECANE)
```bash
npm run code:fix
```
**Efekt**: Uruchamia ESLint + Prettier na wszystkich plikach JS/JSX

### Sprawdź kod bez zmian
```bash
npm run code:check
```
**Efekt**: Sprawdza problemy bez automatycznych poprawek

---

## 🔍 ESLint - Linting JavaScript

### 1. Podstawowe Komendy
```bash
# Napraw wszystkie problemy w src/
npm run lint

# Tylko sprawdź problemy (bez poprawek)
npm run lint:check

# Bezpośrednio z npx:
npx eslint src --fix
npx eslint src
```

### 2. Co ESLint Sprawdza
- **Nieużywane zmienne** - `no-unused-vars`
- **Console.log** - `no-console` (ostrzeżenia)
- **Składnia ES6+** - `prefer-const`, `no-var`
- **React/Next.js** - `react/prop-types`, `react/react-in-jsx-scope`
- **Błędy składni** - automatyczne wykrywanie

### 3. Konfiguracja ESLint
Plik: `eslint.config.mjs`
```javascript
// Ignorowane katalogi:
- node_modules/**
- .next/**
- build/**
- scripts/**
- lib/**

// Sprawdzane pliki:
- src/**/*.{js,jsx,ts,tsx}
```

---

## 🎨 Prettier - Formatowanie Kodu

### 1. Podstawowe Komendy
```bash
# Formatuj wszystkie pliki JS/JSX
npm run format

# Sprawdź formatowanie (bez zmian)
npm run format:check

# Bezpośrednio z npx:
npx prettier --write "src/**/*.{js,jsx}"
npx prettier --check "src/**/*.{js,jsx}"
```

### 2. Konfiguracja Prettier
Plik: `.prettierrc.json`
```json
{
  "semi": true,              // Średniki na końcu
  "singleQuote": true,       // Pojedyncze cudzysłowy
  "printWidth": 80,          // Maksymalna szerokość linii
  "tabWidth": 2,             // 2 spacje wcięcia
  "trailingComma": "es5",    // Przecinki na końcu
  "bracketSpacing": true,    // Spacje w nawiasach {}
  "arrowParens": "avoid"     // Bez nawiasów w arrow functions
}
```

### 3. Ignorowane Pliki
Plik: `.prettierignore`
- `node_modules/`
- `.next/`
- `*.config.js`
- `*.md` (dokumentacja)
- `package-lock.json`

---

## 🛠️ Integracja ESLint + Prettier

### 1. Automatyczne Naprawianie
```bash
# Uruchom oba narzędzia jednocześnie
npm run code:fix

# Równoważne z:
npm run lint && npm run format
```

### 2. Sprawdzanie Przed Commitem
```bash
# Sprawdź wszystko przed commitem
npm run code:check

# Jeśli są błędy, napraw je:
npm run code:fix
```

---

## 📊 Aktualne Problemy w Kodzie

### Znalezione Ostrzeżenia:
1. **Console statements** - 50+ wystąpień
   - Lokalizacja: API routes, hooks, lib files
   - Rozwiązanie: Usuń lub zamień na proper logging

2. **Unused variables** - 20+ wystąpień
   - Przykład: `amount_paid`, `payment_method`, `emailContent`
   - Rozwiązanie: Usuń nieużywane zmienne lub dodaj prefix `_`

3. **React warnings** - 10+ wystąpień
   - Nieużywane importy (np. `Image`)
   - Unescaped entities w JSX

### Naprawione Problemy:
✅ **Syntax errors** - Naprawiono błędy w:
- `src/app/program/srilanka/page.jsx`
- `src/app/retreaty-jogi-bali-2025/page.jsx`

✅ **Formatting** - Wszystkie pliki JS/JSX sformatowane

---

## 🎯 Najlepsze Praktyki

### 1. Przed Edycją Kodu
```bash
# Sprawdź aktualny stan
npm run code:check
```

### 2. Po Edycji Kodu
```bash
# Napraw formatowanie i błędy
npm run code:fix

# Sprawdź czy wszystko OK
npm run code:check
```

### 3. Przed Commitem
```bash
# Pełne sprawdzenie
npm run code:check

# Jeśli są błędy:
npm run code:fix
```

### 4. Ignorowanie Ostrzeżeń
```javascript
// Dla pojedynczej linii:
console.log('debug'); // eslint-disable-line no-console

// Dla całego pliku:
/* eslint-disable no-console */

// Dla nieużywanej zmiennej:
const _unusedVar = data; // prefix _ ignoruje warning
```

---

## 🔧 Konfiguracja VS Code

### 1. Rozszerzenia (Zalecane)
- **ESLint** - Microsoft
- **Prettier - Code formatter** - Prettier
- **Auto Rename Tag** - Jun Han

### 2. Ustawienia VS Code
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "eslint.autoFixOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### 3. Ręczne Formatowanie w VS Code
- **Format Document**: `Shift+Alt+F`
- **Fix ESLint**: `Ctrl+Shift+P` → "ESLint: Fix all auto-fixable Problems"

---

## 📈 Statystyki

### Przed Konfiguracją:
- ❌ Brak automatycznego formatowania
- ❌ Niespójny styl kodu
- ❌ Błędy składni w niektórych plikach

### Po Konfiguracji:
- ✅ ESLint skonfigurowany dla Next.js
- ✅ Prettier formatuje kod automatycznie
- ✅ Naprawiono błędy składni
- ✅ Dodano npm scripts dla łatwego użycia
- ⚠️ ~70 ostrzeżeń do ręcznego przejrzenia

---

## 🚀 Następne Kroki

1. **Przejrzyj ostrzeżenia**: `npm run lint:check`
2. **Usuń console.log** z produkcyjnego kodu
3. **Wyczyść nieużywane zmienne**
4. **Skonfiguruj pre-commit hooks** (opcjonalnie)

---

*Ostatnia aktualizacja: 2025-07-24*
