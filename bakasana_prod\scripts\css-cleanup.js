#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🧹 BAKASANA CSS CLEANUP - Kompleksowe czyszczenie CSS...\n');

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');

// Znajdź wszystkie pliki CSS
const cssFiles = [
  ...glob.sync(path.join(stylesDir, '*.css').replace(/\\/g, '/')),
  ...glob.sync(path.join(appDir, '*.css').replace(/\\/g, '/')),
].filter(file => !file.includes('node_modules') && !file.includes('.backup') && !file.includes('clean'));

console.log(`📁 Znaleziono ${cssFiles.length} plików CSS do czyszczenia:`);
cssFiles.forEach(file => console.log(`   - ${path.relative(process.cwd(), file)}`));
console.log();

// Statystyki
const stats = {
  duplicatesRemoved: 0,
  propertiesSorted: 0,
  filesProcessed: 0,
  errors: 0
};

// Funkcja do usuwania duplikatów selektorów
function removeDuplicateSelectors(cssContent) {
  const selectors = new Map();
  let duplicatesCount = 0;
  
  const result = cssContent.replace(/([^{}]+)\s*\{([^{}]*)\}/g, (match, selector, properties) => {
    const cleanSelector = selector.trim();
    
    // Pomiń komentarze i @rules
    if (cleanSelector.startsWith('/*') || cleanSelector.startsWith('@')) {
      return match;
    }
    
    if (selectors.has(cleanSelector)) {
      // Znaleziono duplikat - połącz właściwości
      const existingProps = selectors.get(cleanSelector);
      const newProps = properties.trim();
      
      // Połącz właściwości (nowe nadpisują stare)
      const combinedProps = `${existingProps}\n  ${newProps}`;
      selectors.set(cleanSelector, combinedProps);
      duplicatesCount++;
      
      return ''; // Usuń duplikat
    } else {
      selectors.set(cleanSelector, properties.trim());
      return match;
    }
  });
  
  stats.duplicatesRemoved += duplicatesCount;
  return result.replace(/\n\s*\n\s*\n/g, '\n\n'); // Usuń nadmiarowe puste linie
}

// Funkcja do usuwania duplikatów właściwości w selektorze
function removeDuplicateProperties(cssContent) {
  return cssContent.replace(/([^{}]+)\s*\{([^{}]*)\}/g, (match, selector, properties) => {
    const cleanSelector = selector.trim();
    
    // Pomiń komentarze i @rules
    if (cleanSelector.startsWith('/*') || cleanSelector.startsWith('@')) {
      return match;
    }
    
    const props = new Map();
    const propRegex = /([^:;]+):\s*([^;]+);?/g;
    let propMatch;
    
    // Zbierz wszystkie właściwości (ostatnia wartość wygrywa)
    while ((propMatch = propRegex.exec(properties)) !== null) {
      const property = propMatch[1].trim();
      const value = propMatch[2].trim();
      props.set(property, value);
    }
    
    // Zbuduj oczyszczone właściwości
    const cleanProperties = Array.from(props.entries())
      .map(([prop, value]) => `  ${prop}: ${value};`)
      .join('\n');
    
    return `${cleanSelector} {\n${cleanProperties}\n}`;
  });
}

// Kolejność właściwości CSS
const propertyOrder = [
  'position', 'top', 'right', 'bottom', 'left', 'z-index',
  'display', 'flex', 'flex-direction', 'flex-wrap', 'justify-content', 'align-items', 'align-content',
  'grid', 'grid-template', 'grid-template-rows', 'grid-template-columns', 'grid-gap',
  'float', 'clear', 'visibility', 'overflow', 'overflow-x', 'overflow-y',
  'box-sizing', 'width', 'min-width', 'max-width', 'height', 'min-height', 'max-height',
  'margin', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left',
  'padding', 'padding-top', 'padding-right', 'padding-bottom', 'padding-left',
  'border', 'border-top', 'border-right', 'border-bottom', 'border-left',
  'border-width', 'border-style', 'border-color', 'border-radius',
  'background', 'background-color', 'background-image', 'background-size', 'background-position',
  'box-shadow', 'color', 'font', 'font-family', 'font-size', 'font-weight', 'font-style',
  'line-height', 'text-align', 'text-decoration', 'text-transform', 'text-shadow',
  'transform', 'transition', 'animation', 'opacity', 'cursor'
];

// Funkcja do sortowania właściwości
function sortProperties(cssContent) {
  let sortedCount = 0;
  
  const result = cssContent.replace(/([^{}]+)\s*\{([^{}]*)\}/g, (match, selector, properties) => {
    const cleanSelector = selector.trim();
    
    if (cleanSelector.startsWith('/*') || cleanSelector.startsWith('@')) {
      return match;
    }
    
    const props = [];
    const propRegex = /([^:;]+):\s*([^;]+);?/g;
    let propMatch;
    
    while ((propMatch = propRegex.exec(properties)) !== null) {
      const property = propMatch[1].trim();
      const value = propMatch[2].trim();
      props.push({ property, value });
    }
    
    if (props.length > 1) {
      props.sort((a, b) => {
        const indexA = propertyOrder.indexOf(a.property);
        const indexB = propertyOrder.indexOf(b.property);
        
        if (indexA !== -1 && indexB !== -1) return indexA - indexB;
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;
        return a.property.localeCompare(b.property);
      });
      
      sortedCount++;
    }
    
    const sortedProperties = props.map(prop => `  ${prop.property}: ${prop.value};`).join('\n');
    return `${cleanSelector} {\n${sortedProperties}\n}`;
  });
  
  stats.propertiesSorted += sortedCount;
  return result;
}

// Funkcja do czyszczenia CSS
function cleanupCSS(cssContent) {
  let content = cssContent;
  
  // 1. Usuń duplikaty selektorów
  content = removeDuplicateSelectors(content);
  
  // 2. Usuń duplikaty właściwości
  content = removeDuplicateProperties(content);
  
  // 3. Sortuj właściwości
  content = sortProperties(content);
  
  // 4. Usuń nadmiarowe białe znaki
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
  content = content.replace(/^\s+|\s+$/g, '');
  
  return content;
}

// Funkcja do przetwarzania pliku
function processFile(filePath) {
  const fileName = path.basename(filePath);
  console.log(`🧹 Czyszczenie: ${fileName}`);
  
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const cleanedContent = cleanupCSS(originalContent);
    
    // Zapisz tylko jeśli są zmiany
    if (originalContent !== cleanedContent) {
      fs.writeFileSync(filePath, cleanedContent, 'utf8');
      console.log(`   ✅ Wyczyszczono ${fileName}`);
    } else {
      console.log(`   ℹ️  ${fileName} - brak zmian`);
    }
    
    stats.filesProcessed++;
    return true;
  } catch (error) {
    console.error(`   ❌ Błąd w ${fileName}:`, error.message);
    stats.errors++;
    return false;
  }
}

// Główna funkcja
function runCleanup() {
  console.log('🚀 Rozpoczynam kompleksowe czyszczenie CSS...\n');
  
  cssFiles.forEach(processFile);
  
  console.log('\n📊 PODSUMOWANIE CZYSZCZENIA:');
  console.log(`   🗑️  Usunięte duplikaty: ${stats.duplicatesRemoved}`);
  console.log(`   📋 Posortowane selektory: ${stats.propertiesSorted}`);
  console.log(`   ✅ Przetworzone pliki: ${stats.filesProcessed}`);
  console.log(`   ❌ Błędy: ${stats.errors}`);
  console.log(`   📁 Całkowita liczba plików: ${cssFiles.length}`);
  
  if (stats.errors === 0) {
    console.log('\n🎉 Wszystkie pliki CSS zostały wyczyszczone!');
  } else {
    console.log('\n⚠️  Niektóre pliki nie zostały przetworzone z powodu błędów.');
  }
}

// Uruchom czyszczenie
try {
  runCleanup();
  console.log('\n✨ Kompleksowe czyszczenie CSS zakończone!');
} catch (error) {
  console.error('❌ Błąd podczas czyszczenia:', error.message);
  process.exit(1);
}
