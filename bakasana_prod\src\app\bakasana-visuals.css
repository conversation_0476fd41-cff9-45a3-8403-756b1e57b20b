/* =============================================
   🎯 BAKASANA OPTIMIZED - ULTRA MINIMALIST & COHESIVE
   Perfect for yoga retreats in Bali & Sri Lanka
   Subtle black & white accents as requested
   ============================================= */

/* =============================================
   1. PROFESSIONAL HERO SECTION - RETREAT STYLE
   ============================================= */

/* Enhanced professional hero styles matching retreat page */
.professional-hero {
  background: linear-gradient(135deg, 
    rgba(253, 252, 248, 0.95) 0%,
    rgba(249, 247, 242, 0.98) 50%,
    rgba(245, 243, 239, 0.95) 100%
  );
}.professional-hero-badge {
  border: 1px solid rgba(193, 155, 104, 0.15);
  box-shadow: 0 4px 20px rgba(193, 155, 104, 0.08);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}.professional-hero-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}.professional-hero-button::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
  content: '';
}.professional-hero-button:hover::before {
  left: 100%;
}.professional-hero-stats {
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
}.professional-hero-stats .stat-value {
  background: linear-gradient(135deg, #C19B68 0%, #D1A46E 100%);
  font-weight: 500;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}.professional-hero-scroll {
  animation: gentleFloat 3s ease-in-out infinite;
}

@keyframes gentleFloat {0%, 100% {
  transform: translateY(0px);
  opacity: 0.4;
}50% {
  transform: translateY(10px);
  opacity: 0.8;
}
}

/* =============================================
   1. ORIGINAL HERO SECTION - FULL IMPLEMENTATION (BACKUP)
   ============================================= */

.bakasana-hero {
  position: relative;
  height: 100vh;
  width: 100%;
  background: linear-gradient(
    135deg,
    rgba(44, 41, 40, 0.4) 0%,
    rgba(44, 41, 40, 0.6) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}.bakasana-hero::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  background-image: url('/images/background/bali-hero-low-res.webp'),
    url('/images/background/bali-hero-1200.avif'),
    url('/images/background/bali-hero.webp');
  background-position: center 30%;
  /* Parallax effect */
  transition: filter 0.3s ease;
  /* Progressive loading: low-res → AVIF → WebP */
  background-size: cover;
  /* Slightly higher focus for better composition */
  background-repeat: no-repeat;
  background-attachment: fixed;
  content: '';
}.bakasana-hero-content {
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  color: white;
  text-align: center;
}.bakasana-hero-title {
  margin: 0 0 40px 0;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  font-size: 120px;
  font-weight: 200;
  line-height: 1.1;
  text-transform: uppercase;
  letter-spacing: 0.25em;
}.bakasana-hero-subtitle {
  margin: 0 0 20px 0;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 20px;
  font-weight: 400;
  opacity: 0.9;
}.bakasana-hero-quote {
  margin: 0 0 60px 0;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  font-size: 16px;
  font-style: italic;
  opacity: 0.8;
}.bakasana-scroll-hint {
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 300;
  animation: pulse 3s infinite;
  opacity: 0.7;
}

@keyframes pulse {
}

/* Responsive hero optimizations */
@media (max-width: 768px) {
}

@media (max-width: 480px) {
}

/* =============================================
   2. INTRO SECTION - NEW IMPLEMENTATION
   ============================================= */

.bakasana-intro {
  background: var(--sanctuary);
  padding: 100px 0;
  text-align: center;
}.bakasana-intro-divider {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 0 50px 0;
}.bakasana-intro-divider::before,
.bakasana-intro-divider::after {
  flex: 1;
  max-width: 80px;
  height: 1px;
  background: var(--stone-light);
  content: '';
}.bakasana-intro-divider::before {
  margin-right: 24px;
}.bakasana-intro-divider::after {
  margin-left: 24px;
}.bakasana-intro-diamond {
  width: 6px;
  height: 6px;
  background: var(--temple-gold);
  transform: rotate(45deg);
}.bakasana-intro-quote {
  max-width: 500px;
  margin: 0 0 50px 0;
  margin-right: auto;
  margin-left: auto;
  color: var(--temple-gold);
  font-family: 'Cormorant Garamond', serif;
  font-size: 33px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.3;
}.bakasana-intro-text {
  max-width: 550px;
  margin: 0 auto 50px auto;
  color: var(--charcoal);
  font-family: 'Inter', sans-serif;
  font-size: 17px;
  font-weight: 300;
  line-height: 1.7;
}.bakasana-intro-button {
  display: inline-block;
  padding: 18px 40px;
  border: 1px solid var(--soft-black);
  background: transparent;
  color: var(--soft-black);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 400;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.4s ease;
  letter-spacing: 0.5px;
}.bakasana-intro-button:hover {
  background: var(--soft-black);
  box-shadow: 0 4px 12px rgba(26, 24, 22, 0.15);
  color: var(--pure-white);
  transform: translateY(-1px);
}

/* =============================================
   3. THREE PATHS SECTION - TRZECH ŚCIEŻEK
   ============================================= */

.bakasana-paths {
  background: var(--whisper);
  padding: 100px 0;
  text-align: center;
  letter-spacing: 0.02em;
}.bakasana-paths-title {
  margin: 0 0 30px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 42px;
  font-weight: 300;
  letter-spacing: 0.02em;
}.bakasana-paths-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  max-width: 1100px;
  margin: 80px auto 0 auto;
  padding: 0 20px;
  gap: 30px;
}.bakasana-path-card {
  display: block;
  overflow: hidden;
  border: 1px solid rgba(139, 134, 128, 0.08);
  background: var(--pure-white);
  text-decoration: none;
  transition: all 0.4s ease;
}.bakasana-path-card:hover {
  border-color: var(--temple-gold);
  box-shadow: 0 11px 40px rgba(26, 24, 22, 0.08);
  transform: translateY(-3px);
}.bakasana-path-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 220px;
  background: linear-gradient(135deg, var(--stone-light) 0%, var(--whisper) 100%);
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 300;
}.bakasana-path-image::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(45deg, transparent 30%, var(--temple-gold) 50%, transparent 70%);
  transition: opacity 0.4s ease;
  opacity: 0;
  content: '';
}.bakasana-path-card:hover .bakasana-path-image::after {
  opacity: 0.03;
}.bakasana-path-content {
  padding: 35px 25px;
}.bakasana-path-category {
  margin: 0 0 12px 0;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.15em;
}.bakasana-path-title {
  margin: 0 0 15px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 22px;
  font-weight: 400;
  line-height: 1.3;
}.bakasana-path-description {
  margin: 0 0 20px 0;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  line-height: 1.6;
}.bakasana-path-price {
  color: var(--temple-gold);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* =============================================
   4. UPCOMING RETREATS - NAJBLIŻSZE TERMINY
   ============================================= */

.bakasana-retreats {
  background: var(--sanctuary);
  padding: 100px 0;
  text-align: center;
}.bakasana-retreats-title {
  margin: 0 0 30px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 42px;
  font-weight: 300;
  letter-spacing: 0.02em;
}.bakasana-retreats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  max-width: 1000px;
  margin: 80px auto 0 auto;
  padding: 0 20px;
  gap: 50px;
}.bakasana-retreat-card {
  overflow: hidden;
  padding: 0;
  border: 1px solid rgba(139, 134, 128, 0.06);
  background: var(--pure-white);
  text-align: left;
  transition: all 0.4s ease;
}.bakasana-retreat-card:hover {
  border-color: var(--temple-gold);
  box-shadow: 0 15px 45px rgba(26, 24, 22, 0.1);
  transform: translateY(-4px);
}.bakasana-retreat-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 260px;
  margin: 0;
  background: linear-gradient(135deg, var(--stone-light) 0%, var(--whisper) 100%);
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 300;
}.bakasana-retreat-image::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(45deg, transparent 30%, var(--temple-gold) 50%, transparent 70%);
  transition: opacity 0.4s ease;
  opacity: 0;
  content: '';
}.bakasana-retreat-card:hover .bakasana-retreat-image::after {
  opacity: 0.04;
}.bakasana-retreat-content {
  padding: 30px 25px;
}.bakasana-retreat-date {
  margin: 0 0 12px 0;
  color: var(--temple-gold);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.12em;
}.bakasana-retreat-title {
  margin: 0 0 8px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 26px;
  font-weight: 400;
  line-height: 1.3;
}.bakasana-retreat-location {
  margin: 0 0 20px 0;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 300;
}.bakasana-retreat-divider {
  width: 100%;
  height: 1px;
  margin: 20px 0;
  background: rgba(139, 134, 128, 0.12);
}.bakasana-retreat-features {
  margin: 0 0 25px 0;
  padding: 0;
  list-style: none;
}.bakasana-retreat-features li {
  position: relative;
  margin: 0 0 6px 0;
  padding-left: 12px;
  color: var(--charcoal);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 300;
  line-height: 1.5;
}.bakasana-retreat-features li::before {
  position: absolute;
  left: 0;
  color: var(--temple-gold);
  font-size: 16px;
  line-height: 1.2;
  content: '·';
}.bakasana-retreat-price {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 28px;
  font-weight: 400;
  letter-spacing: 0.01em;
}.bakasana-retreat-cta {
  display: inline-block;
  padding: 14px 28px;
  background: var(--soft-black);
  color: var(--pure-white);
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.4s ease;
  letter-spacing: 0.05em;
}.bakasana-retreat-cta:hover {
  background: var(--charcoal);
  box-shadow: 0 4px 12px rgba(26, 24, 22, 0.2);
  transform: translateY(-1px);
}

/* =============================================
   5. ABOUT JULIA - CIEPLEJSZA WERSJA
   ============================================= */

.bakasana-about-julia {
  background: var(--whisper);
  padding: 100px 0;
}.bakasana-julia-container {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 70px;
}.bakasana-julia-text {
  order: 1;
}.bakasana-julia-image-wrapper {
  order: 2;
}.bakasana-julia-quote {
  margin: 0 0 30px 0;
  color: #C9A575;
  font-family: 'Cormorant Garamond', serif;
  font-size: 32px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.3;
}.bakasana-julia-signature {
  margin: 0 0 40px 0;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 20px;
}.bakasana-julia-bio {
  margin: 0 0 40px 0;
  color: #2C2928;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 1.7;
}.bakasana-julia-stats {
  margin: 0 0 40px 0;
  padding: 0;
  list-style: none;
}.bakasana-julia-stats li {
  position: relative;
  margin: 0 0 15px 0;
  padding-left: 25px;
  color: #2C2928;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}.bakasana-julia-stats li::before {
  position: absolute;
  left: 0;
  color: #C9A575;
  font-size: 16px;
}.bakasana-julia-stats li:nth-child(1)::before {
  content: '∞';
}.bakasana-julia-stats li:nth-child(2)::before {
  content: '♡';
}.bakasana-julia-stats li:nth-child(3)::before {
  content: 'ॐ';
}.bakasana-julia-cta {
  display: inline-block;
  padding: 16px 40px;
  border: 1px solid #C9A575;
  background: transparent;
  color: #C9A575;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.3s ease;
}.bakasana-julia-cta:hover {
  background: #C9A575;
  color: white;
}.bakasana-julia-photo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 500px;
  background: #f0f0f0;
  color: #999;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}

/* =============================================
   6. SOCIAL PROOF - INSTAGRAM INTEGRATION
   ============================================= */

.bakasana-social {
  background: white;
  padding: 120px 0;
  text-align: center;
}.bakasana-social-handle {
  margin: 0 0 60px 0;
  color: #2C2928;
  font-family: 'Inter', sans-serif;
  font-size: 24px;
  font-weight: 400;
}.bakasana-instagram-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  max-width: 800px;
  margin: 0 auto 60px auto;
  padding: 0 20px;
  gap: 20px;
}.bakasana-instagram-item {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0f0f0;
  color: #999;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  transition: transform 0.3s ease;
  aspect-ratio: 1;
}.bakasana-instagram-item:hover {
  transform: scale(1.05);
}.bakasana-social-cta {
  display: inline-block;
  padding: 16px 40px;
  background: #C9A575;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  text-decoration: none;
  transition: background 0.3s ease;
}.bakasana-social-cta:hover {
  background: #B8935C;
}

/* =============================================
   7. CONTACT - UPROSZCZONY
   ============================================= */

.bakasana-contact {
  background: linear-gradient(135deg, #FDF9F3 0%, #F7F4F0 100%);
  padding: 120px 0;
  text-align: center;
}.bakasana-contact-title {
  margin: 0 0 20px 0;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 400;
}.bakasana-contact-subtitle {
  max-width: 600px;
  margin: 0 0 60px 0;
  margin-right: auto;
  margin-left: auto;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 18px;
}.bakasana-contact-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 40px;
}.bakasana-contact-option {
  padding: 40px 30px;
  background: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
  text-decoration: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}.bakasana-contact-option:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}.bakasana-contact-option h3 {
  margin: 0 0 15px 0;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 24px;
  font-weight: 500;
}.bakasana-contact-option p {
  margin: 0;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
}.bakasana-contact-om {
  margin: 60px 0 0 0;
  color: #C9A575;
  font-size: 32px;
}

/* =============================================
   8. FOOTER - MINIMALISTYCZNY
   ============================================= */

.bakasana-footer {
  background: #2C2928;
  color: white;
  padding: 80px 0;
  text-align: center;
}.bakasana-footer-logo {
  margin: 0 0 10px 0;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  font-size: 36px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.bakasana-footer-subtitle {
  margin: 0 0 40px 0;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
}.bakasana-footer-social {
  display: flex;
  justify-content: center;
  margin: 0 0 40px 0;
  gap: 30px;
}.bakasana-footer-social a {
  color: #9B9592;
  font-size: 24px;
  transition: color 0.3s ease;
}.bakasana-footer-social a:hover {
  color: #C9A575;
}.bakasana-footer-copyright {
  margin: 0;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}

/* =============================================
   ONLINE CLASSES SECTION - MINIMALISTIC ICONS
   ============================================= */

.online-section .feature-icon {
  font-size: 24px;
  color: #C9A575;
  margin-bottom: 20px;
  display: block;
  text-align: center;
}.online-section .feature-title {
  margin-bottom: 15px;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 22px;
  font-weight: 500;
  text-align: center;
}.online-section .feature-description {
  max-width: 280px;
  margin: 0 auto;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  text-align: center;
}.online-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 60px;
}.online-feature {
  padding: 40px 20px;
  text-align: center;
  transition: transform 0.3s ease;
}.online-feature:hover {
  transform: translateY(-3px);
}.online-feature:hover .feature-icon {
  color: #B8935C;
}

/* =============================================
   RESPONSIVE DESIGN
   ============================================= */

@media (max-width: 768px) {.bakasana-paths-grid,
  .bakasana-retreats-grid {
  grid-template-columns: 1fr;
}
}

@media (max-width: 480px) {.bakasana-paths-title,
  .bakasana-retreats-title,
  .bakasana-contact-title {
  font-size: 36px;
}
}