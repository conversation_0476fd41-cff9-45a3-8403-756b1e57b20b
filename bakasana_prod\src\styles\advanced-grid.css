/* =============================================
   🏗️ BAKASANA ADVANCED GRID SYSTEM
   Perfect 10/10 Responsive Layout System
   ============================================= */

/* ===== CONTAINER QUERIES SUPPORT ===== */
@supports (container-type: inline-size) {.grid-container {
  container-type: inline-size;
}
}

/* ===== RESPONSIVE GRID SYSTEM ===== */

/* Auto-fit Grid - Automatically adjusts columns based on content */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width, 280px), 1fr));
  gap: clamp(1rem, 4vw, 2rem);
  width: 100%;
}

/* Auto-fill Grid - Maintains empty columns */
.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--min-column-width, 280px), 1fr));
  gap: clamp(1rem, 4vw, 2rem);
  width: 100%;
}

/* Responsive Grid Variants */
.grid-responsive-cards {
  --min-column-width: 300px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width), 1fr));
  gap: clamp(1.5rem, 5vw, 3rem);
  align-items: start;
}.grid-responsive-services {
  display: grid;
  align-items: stretch;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width), 1fr));
  --min-column-width: 280px;
  gap: clamp(2rem, 6vw, 4rem);
}.grid-responsive-testimonials {
  display: grid;
  align-items: start;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width), 1fr));
  --min-column-width: 320px;
  gap: clamp(1.5rem, 4vw, 2.5rem);
}.grid-responsive-gallery {
  display: grid;
  align-items: center;
  grid-template-columns: repeat(auto-fill, minmax(var(--min-column-width), 1fr));
  --min-column-width: 250px;
  gap: clamp(1rem, 3vw, 1.5rem);
}

/* ===== CONTAINER QUERY RESPONSIVE GRIDS ===== */
@supports (container-type: inline-size) {.grid-container-responsive {
  display: grid;
  container-type: inline-size;
  gap: clamp(1rem, 4vw, 2rem);
}

  /* Small container - single column */
  @container (max-width: 400px) {
  }

  /* Medium container - two columns */
  @container (min-width: 401px) and (max-width: 800px) {
  }

  /* Large container - three columns */
  @container (min-width: 801px) and (max-width: 1200px) {
  }

  /* Extra large container - four columns */
  @container (min-width: 1201px) {
  }
}

/* ===== SPECIALIZED GRID LAYOUTS ===== */

/* Masonry-style Grid */
.grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-template-rows: masonry; /* Future CSS feature */
  gap: clamp(1rem, 4vw, 2rem);
  align-items: start;
}

/* Fallback for browsers without masonry support */
@supports not (grid-template-rows: masonry) {.grid-masonry {
  display: grid;
  align-items: start;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: clamp(1rem, 4vw, 2rem);
}
}

/* Hero Grid - Special layout for hero sections */
.grid-hero {
  display: grid;
  grid-template-columns: 1fr;
  gap: clamp(2rem, 8vw, 4rem);
  align-items: center;
  min-height: 100vh;
  padding: clamp(2rem, 8vw, 6rem) 0;
}

@media (min-width: 1024px) {.grid-hero {
  grid-template-columns: 1fr 1fr;
  gap: clamp(4rem, 10vw, 8rem);
}
}

/* Stats Grid - Perfect for statistics display */
.grid-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: clamp(1rem, 4vw, 2rem);
  text-align: center;
}

@media (min-width: 768px) {.grid-stats {
  grid-template-columns: repeat(4, 1fr);
  gap: clamp(2rem, 6vw, 4rem);
}
}

/* ===== GRID UTILITIES ===== */

/* Gap utilities */
.gap-responsive-sm { gap: clamp(0.5rem, 2vw, 1rem); }.gap-responsive-md {
  gap: clamp(1rem, 4vw, 2rem);
}.gap-responsive-lg {
  gap: clamp(1.5rem, 6vw, 3rem);
}.gap-responsive-xl {
  gap: clamp(2rem, 8vw, 4rem);
}

/* Alignment utilities */
.grid-items-start { align-items: start; }.grid-items-center {
  align-items: center;
}.grid-items-end {
  align-items: end;
}.grid-items-stretch {
  align-items: stretch;
}.grid-content-start {
  justify-content: start;
}.grid-content-center {
  justify-content: center;
}.grid-content-end {
  justify-content: end;
}.grid-content-between {
  justify-content: space-between;
}

/* ===== RESPONSIVE BREAKPOINT OVERRIDES ===== */

/* Mobile-first approach with progressive enhancement */
@media (max-width: 480px) {.grid-auto-fit,
  .grid-auto-fill,
  .grid-responsive-cards,
  .grid-responsive-services,
  .grid-responsive-testimonials {
  grid-template-columns: 1fr;
  gap: clamp(1rem, 6vw, 1.5rem);
}
}

@media (min-width: 481px) and (max-width: 768px) {
}

@media (min-width: 769px) and (max-width: 1024px) {.grid-responsive-cards {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {.grid-auto-fit,
  .grid-auto-fill,
  .grid-responsive-cards,
  .grid-responsive-services,
  .grid-responsive-testimonials,
  .grid-responsive-gallery {
  transition: none;
}
}

/* High contrast mode support */
@media (prefers-contrast: high) {
}

/* ===== PRINT STYLES ===== */
@media print {
}

/* ===== FUTURE-PROOF FEATURES ===== */

/* CSS Grid Level 3 - Subgrid support */
@supports (grid-template-rows: subgrid) {.grid-subgrid-rows {
  grid-template-rows: subgrid;
}.grid-subgrid-columns {
  grid-template-columns: subgrid;
}
}

/* CSS Grid Level 4 - Grid template areas enhancement */
.grid-semantic {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar main aside"
    "footer footer footer";
  grid-template-columns: 200px 1fr 200px;
  grid-template-rows: auto 1fr auto;
  gap: clamp(1rem, 4vw, 2rem);
  min-height: 100vh;
}

@media (max-width: 768px) {.grid-semantic {
  grid-template-rows: auto auto auto auto auto;
  grid-template-columns: 1fr;
  grid-template-areas: "header"
      "main"
      "sidebar"
      "aside"
      "footer";
}
}