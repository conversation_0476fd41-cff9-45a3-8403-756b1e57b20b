/* =============================================
   🔧 NAVBAR CONTRAST FIX - Desktop Navigation
   Ensures proper visibility and contrast for desktop navbar links
   ============================================= */

/* Desktop navbar link contrast enhancement */
@media (min-width: 1024px) {
  /* Base desktop navbar styles */
  .desktop-nav-link {
    color: #2F2B28 !important; /* Slightly darker for better contrast */
    font-weight: 450 !important; /* Slightly bolder for better visibility */
    opacity: 1 !important;
    text-shadow: none !important;
    padding: 6px 12px !important; /* Add subtle padding for better click area */
    border-radius: 4px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* Hover state with gold color */
  .desktop-nav-link:hover {
    color: #D4AF37 !important; /* Gold hover color to match mobile */
    transform: translateY(-1px) !important;
    background: rgba(212, 175, 55, 0.06) !important; /* Subtle background on hover */
  }

  /* Active state */
  .desktop-nav-link.active {
    color: #D4AF37 !important; /* Gold for active state */
    font-weight: 500 !important;
    background: rgba(212, 175, 55, 0.08) !important; /* Subtle background for active state */
  }

  /* Special styling for highlighted links */
  .desktop-nav-link.highlight {
    color: #FFFFFF !important; /* White text for better contrast */
    background: linear-gradient(135deg, #8B7355, #A68B5B) !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(139, 115, 85, 0.2) !important;
  }.desktop-nav-link.highlight:hover {
  background: linear-gradient(135deg, #A68B5B, #B8956C) !important;
  box-shadow: 0 3px 12px rgba(139, 115, 85, 0.25) !important;
  color: #FFFFFF !important;
  transform: translateY(-1px) !important;
}
}

/* Enhanced navbar background for luxury feel */
@media (min-width: 1024px) {.navbar-scrolled {
  border-bottom: 1px solid rgba(212, 175, 55, 0.15) !important;
  background: rgba(255, 253, 250, 0.95) !important;
  box-shadow: 0 2px 20px rgba(139, 115, 85, 0.08) !important;
  -webkit-backdrop-filter: blur(24px) !important;
  /* Warm white with slight tint */
    backdrop-filter: blur(24px) !important;
}.navbar-transparent {
  background: rgba(255, 253, 250, 0.25) !important;
  box-shadow: 0 1px 8px rgba(139, 115, 85, 0.04) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  /* Subtle warm transparency */
    backdrop-filter: blur(12px) !important;
}
}

/* Logo contrast enhancement */
@media (min-width: 1024px) {.navbar-logo {
  color: #2A2622 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  /* Darker for better contrast */
    font-weight: 400 !important;
}.navbar-logo:hover {
  color: #8B7355 !important;
  text-shadow: 0 2px 4px rgba(139, 115, 85, 0.15) !important;
  transform: scale(1.02) !important;
}
}

/* Mobile menu - for devices below 1024px */
@media (max-width: 1023px) {.mobile-nav-link {
  color: #3A3633 !important;
}.mobile-nav-link:hover {
  color: #D4AF37 !important;
}.mobile-nav-link.active {
  color: #D4AF37 !important;
}

  /* Mobile navbar background with warm tint */
  .navbar-scrolled {
    background: rgba(255, 253, 250, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 2px 15px rgba(139, 115, 85, 0.08) !important;
  }
}

/* Accessibility enhancements */
@media (min-width: 1024px) {.desktop-nav-link:focus {
  color: #D4AF37 !important;
  outline: 2px solid #D4AF37 !important;
  outline-offset: 2px !important;
}
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {.desktop-nav-link {
  color: #000000 !important;
}.desktop-nav-link:hover,
    .desktop-nav-link.active {
  color: #B8860B !important;
}
  }
}

/* Animation enhancements for better UX */
@media (min-width: 1024px) {
  
  /* Underline animation for active/hover states */
  .nav-underline {
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    opacity: 0;
    transform: scaleX(0);
    transition: all 0.3s ease;
  }.desktop-nav-link:hover .nav-underline,
  .desktop-nav-link.active .nav-underline {
  transform: scaleX(1);
  opacity: 1;
}
}

/* =============================================
   🚨 CRITICAL FIX - Force navbar visibility
   High specificity overrides to ensure navbar works
   ============================================= */

/* Force desktop menu visibility on desktop screens */
@media (min-width: 1024px) {nav .hidden.md\\:flex {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}
  
  /* Hide mobile elements on desktop */
  nav .md\\:hidden {
    display: none !important;
  }
  
  /* Ensure WhatsApp button is visible on desktop */
  nav .hidden.md\\:block {
    display: block !important;
    visibility: visible !important;
  }
}

/* Force mobile menu visibility on mobile screens */
@media (max-width: 1023px) {nav .md\\:hidden {
  display: block !important;
  visibility: visible !important;
}
  
  /* Hide desktop elements on mobile */
  nav .hidden.md\\:flex,
  nav .hidden.md\\:block {
    display: none !important;
  }
  
  /* Mobile menu container */
  nav .md\\:hidden.absolute {
    position: absolute !important;
  }
}

/* Additional safety overrides */
.navbar-logo {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure hamburger button works */
button[aria-label="Toggle menu"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@media (min-width: 1024px) {button[aria-label="Toggle menu"] {
  display: none !important;
}
}