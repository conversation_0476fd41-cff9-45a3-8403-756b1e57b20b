'use client';
import Image from 'next/image';
import Link from 'next/link';
import React, { useMemo } from 'react';

import AdvancedSEO from '@/components/SEO/AdvancedSEO';

import OnlineClassesSection from './OnlineClassesSection';
import PerformantWhatsApp from '../PerformantWhatsApp';
import ProfessionalHero from './ProfessionalHero';

import '@/app/bakasana-visuals.css';
import {
  getOrganizationStructuredData,
  getYogaInstructorStructuredData,
} from '@/lib/yogaStructuredData';

// Import nowych stylów Bakasana

// Simple Icon component
const SafeIcon = React.memo(({ Icon, className = '' }) => {
  if (!Icon) return null;
  return <Icon className={`w-6 h-6 ${className}`} />;
});
SafeIcon.displayName = 'SafeIcon';

// =============================================
// 1. HERO SECTION - UŻYWAMY PARALLAX HERO KOMPONENT
// =============================================

// =============================================
// 2. INTRO SECTION - NOWA SEKCJA ZARAZ PO HERO
// =============================================

const BakasanaIntroSection = React.memo(() => {
  return (
    <section className='bakasana-intro'>
      <div className='container'>
        {/* DIVIDER ZE ZŁOTYM DIAMENTEM */}
        <div className='bakasana-intro-divider'>
          <div className='bakasana-intro-diamond'></div>
        </div>

        {/* QUOTE - 28px, Cormorant italic, kolor: #C9A575 */}
        <p className='bakasana-intro-quote'>"Joga to powrót do siebie"</p>

        {/* TEKST - 18px, Inter, line-height: 1.8, max-width: 600px, centered */}
        <p className='bakasana-intro-text'>
          Cześć, jestem Julia. Prowadzę zajęcia jogi i retreaty na Bali oraz Sri
          Lance. Wierzę, że każdy może znaleźć swoją ścieżkę do wewnętrznego
          spokoju.
        </p>

        {/* GHOST BUTTON */}
        <Link
          href='/program'
          className='bakasana-intro-button'
          aria-label='Zobacz program retreatów'
        >
          Zobacz program →
        </Link>
      </div>
    </section>
  );
});
BakasanaIntroSection.displayName = 'BakasanaIntroSection';

// =============================================
// 3. TRZY ŚCIEŻKI - ZAMIAST "MIEJSCA GDZIE ODDYCHASZ"
// =============================================
const ThreePathsSection = React.memo(() => {
  const paths = useMemo(
    () => [
      {
        id: 'studio',
        category: 'ZAJĘCIA W STUDIU',
        title: 'Praktyka w grupie',
        description: 'Zajęcia w studiu w Rzeszowie w małych grupach',
        price: 'Od 80 zł/miesiąc',
        image: 'OBRAZ STUDIA',
        link: '/zajecia-online',
      },
      {
        id: 'online',
        category: 'ZAJĘCIA ONLINE',
        title: 'Joga z domu',
        description: 'Indywidualne i grupowe zajęcia online',
        price: 'Od 120 zł/miesiąc',
        image: 'OBRAZ ONLINE',
        link: '/zajecia-online',
      },
      {
        id: 'retreats',
        category: 'RETREATY',
        title: 'Wyjazdy na Bali i Sri Lankę',
        description: 'Tygodniowe retreaty jogi w pięknych miejscach',
        price: 'Od 2900 PLN',
        image: 'OBRAZ RETREATU',
        link: '/program',
      },
    ],
    []
  );

  return (
    <section className='bakasana-paths'>
      <div className='container'>
        {/* TYTUŁ - 48px, centered */}
        <h2 className='bakasana-paths-title'>Jak możesz ćwiczyć</h2>

        {/* DIVIDER */}
        <div className='bakasana-intro-divider'>
          <div className='bakasana-intro-diamond'></div>
        </div>

        {/* GRID Z TRZEMA KARTAMI */}
        <div className='bakasana-paths-grid'>
          {paths.map(path => (
            <Link
              key={path.id}
              href={path.link}
              className='bakasana-path-card'
              aria-label={`Wybierz ścieżkę: ${path.title}`}
            >
              {/* PLACEHOLDER DLA OBRAZU - aspect 4:5 */}
              <div className='bakasana-path-image'>
                <span style={{ textAlign: 'center', lineHeight: '1.4' }}>
                  {path.image}
                </span>
              </div>

              <div className='bakasana-path-content'>
                {/* KATEGORIA - 11px, spaced */}
                <div className='bakasana-path-category'>{path.category}</div>

                {/* TYTUŁ - 22px, elegant */}
                <h3 className='bakasana-path-title'>{path.title}</h3>

                {/* OPIS */}
                <p className='bakasana-path-description'>{path.description}</p>

                {/* CENA - subtle */}
                <div className='bakasana-path-price'>{path.price}</div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
});
ThreePathsSection.displayName = 'ThreePathsSection';

// =============================================
// 4. NAJBLIŻSZE TERMINY - UPROSZCZONE KARTY
// =============================================

const UpcomingRetreatsSection = React.memo(() => {
  const upcomingRetreats = useMemo(
    () => [
      {
        id: 'bali-march',
        date: 'MARZEC 15-25',
        title: 'Retreat na Bali',
        location: 'Ubud, Bali',
        image: 'OBRAZ BALI',
        features: ['10 dni jogi', 'Grupa 8 osób', 'Wyżywienie i noclegi'],
        price: '3200 PLN',
        cta: 'Szczegóły',
        status: 'available',
        link: '/program/bali-march',
      },
      {
        id: 'srilanka-april',
        date: 'KWIECIEŃ 10-20',
        title: 'Retreat na Sri Lance',
        location: 'Południe Sri Lanki',
        image: 'OBRAZ SRI LANKA',
        features: ['Joga i ajurweda', 'Blisko oceanu', 'Relaks i odnowa'],
        price: '2900 PLN',
        cta: 'Zarezerwuj',
        status: 'last-minute',
        link: '/program/srilanka-april',
      },
    ],
    []
  );

  return (
    <section className='bakasana-retreats'>
      <div className='container'>
        {/* TYTUŁ - 48px */}
        <h2 className='bakasana-retreats-title'>Najbliższe retreaty</h2>

        {/* DIVIDER */}
        <div className='bakasana-intro-divider'>
          <div className='bakasana-intro-diamond'></div>
        </div>

        {/* GRID Z KARTAMI RETREATÓW */}
        <div className='bakasana-retreats-grid'>
          {upcomingRetreats.map(retreat => (
            <article key={retreat.id} className='bakasana-retreat-card'>
              {/* PLACEHOLDER DLA OBRAZU */}
              <div className='bakasana-retreat-image'>
                <span style={{ textAlign: 'center', lineHeight: '1.4' }}>
                  {retreat.image}
                </span>
              </div>

              <div className='bakasana-retreat-content'>
                {/* DATA NA GÓRZE */}
                <div className='bakasana-retreat-date'>{retreat.date}</div>

                {/* TYTUŁ - 26px */}
                <h3 className='bakasana-retreat-title'>{retreat.title}</h3>

                {/* LOKALIZACJA */}
                <p className='bakasana-retreat-location'>{retreat.location}</p>

                {/* LINIA */}
                <div className='bakasana-retreat-divider'></div>

                {/* BULLETS */}
                <ul className='bakasana-retreat-features'>
                  {retreat.features.map((feature, index) => (
                    <li key={index}>{feature}</li>
                  ))}
                </ul>

                {/* CENA DUŻA */}
                <div className='bakasana-retreat-price'>{retreat.price}</div>

                {/* RÓŻNE CTA */}
                <Link
                  href={retreat.link}
                  className='bakasana-retreat-cta'
                  aria-label={`${retreat.cta} - ${retreat.title}`}
                >
                  {retreat.cta}
                </Link>
              </div>
            </article>
          ))}
        </div>

        {/* LINK DO WSZYSTKICH RETREATÓW */}
        <div style={{ textAlign: 'center', marginTop: '60px' }}>
          <Link
            href='/program'
            className='bakasana-intro-button'
            aria-label='Zobacz wszystkie retreaty'
          >
            Zobacz wszystkie retreaty →
          </Link>
        </div>
      </div>
    </section>
  );
});
UpcomingRetreatsSection.displayName = 'UpcomingRetreatsSection';

// =============================================
// 5. O JULII - CIEPLEJSZA WERSJA
// =============================================
const AboutJuliaSection = React.memo(() => {
  return (
    <section className='bakasana-about-julia'>
      <div className='bakasana-julia-container'>
        {/* TEKST - ORDER 1 */}
        <div className='bakasana-julia-text'>
          {/* QUOTE DUŻY */}
          <p className='bakasana-julia-quote'>
            "Joga to nie tylko ćwiczenia, to sposób życia"
          </p>

          {/* SIGNATURE */}
          <p className='bakasana-julia-signature'>— Julia</p>

          {/* TEKST O JULII - MAX 3 AKAPITY, OSOBISTY TON */}
          <div className='bakasana-julia-bio'>
            <p>
              Jestem Julia, instruktorką jogi i fizjoterapeutką. Joga weszła w
              moje życie kilka lat temu i całkowicie je zmieniła.
            </p>

            <p>
              Bali i Sri Lanka to miejsca, które pokazały mi, jak ważny jest
              spokój i równowaga. Chcę dzielić się tym doświadczeniem z innymi.
            </p>

            <p>
              Mam certyfikat RYT 500 i 8 lat doświadczenia jako fizjoterapeutka.
              Łączę wiedzę o ciele z praktyką jogi.
            </p>
          </div>

          {/* MAŁE IKONY */}
          <ul className='bakasana-julia-stats'>
            <li>Certyfikat RYT 500</li>
            <li>8 lat doświadczenia</li>
            <li>200+ uczniów</li>
          </ul>

          {/* CTA */}
          <Link
            href='/kontakt'
            className='bakasana-julia-cta'
            aria-label='Skontaktuj się z Julią'
          >
            Skontaktuj się →
          </Link>
        </div>

        {/* ZDJĘCIE JULII - ORDER 2 */}
        <div className='bakasana-julia-image-wrapper'>
          <div className='bakasana-julia-photo'>
            JULIA PHOTO
            <br />
            naturalne światło
            <br />
            ciepłe kolory
            <br />
            uśmiech
          </div>
        </div>
      </div>
    </section>
  );
});
AboutJuliaSection.displayName = 'AboutJuliaSection';

// =============================================
// 6. SOCIAL PROOF - ZAMIAST TESTIMONIALI
// =============================================

const SocialProofSection = React.memo(() => {
  const instagramPosts = useMemo(
    () => ['IMG1', 'IMG2', 'IMG3', 'IMG4', 'IMG5', 'IMG6'],
    []
  );

  return (
    <section className='bakasana-social'>
      <div className='container'>
        {/* INSTAGRAM HANDLE */}
        <h2 className='bakasana-social-handle'>@bakasana.retreats</h2>

        {/* INSTAGRAM GRID */}
        <div className='bakasana-instagram-grid'>
          {instagramPosts.map((post, index) => (
            <div key={index} className='bakasana-instagram-item'>
              {post}
            </div>
          ))}
        </div>

        {/* CTA */}
        <Link
          href='https://instagram.com/bakasana.retreats'
          target='_blank'
          rel='noopener noreferrer'
          className='bakasana-social-cta'
          aria-label='Dołącz do naszej społeczności na Instagram'
        >
          Dołącz do naszej społeczności →
        </Link>
      </div>
    </section>
  );
});
SocialProofSection.displayName = 'SocialProofSection';

// =============================================
// 7. KONTAKT - UPROSZCZONY
// =============================================
const ContactSection = React.memo(() => {
  const contactOptions = useMemo(
    () => [
      {
        id: 'whatsapp',
        title: 'WhatsApp',
        description: 'Najszybciej',
        component: (
          <PerformantWhatsApp
            variant='icon'
            message='Cześć! Interesuję się retreatami jogowymi na Bali i Sri Lanka.'
            className='text-enterprise-brown hover:text-terra transition-colors'
          />
        ),
        href: null, // używamy komponentu zamiast href
      },
      {
        id: 'email',
        title: 'Email',
        description: 'W 24h',
        href: 'mailto:<EMAIL>',
        target: '_self',
      },
      {
        id: 'phone',
        title: 'Telefon',
        description: 'Umów rozmowę',
        href: 'tel:+48606101523',
        target: '_self',
      },
    ],
    []
  );

  return (
    <section className='bakasana-contact'>
      <div className='container'>
        {/* TYTUŁ */}
        <h2 className='bakasana-contact-title'>Skontaktuj się ze mną</h2>

        {/* DIVIDER */}
        <div className='bakasana-intro-divider'>
          <div className='bakasana-intro-diamond'></div>
        </div>

        {/* SUBTITLE */}
        <p className='bakasana-contact-subtitle'>
          Masz pytania o zajęcia lub retreaty?
          <br />
          Napisz do mnie - chętnie odpowiem.
        </p>

        {/* 3 OPCJE KONTAKTU */}
        <div className='bakasana-contact-options'>
          {contactOptions.map(option =>
            option.component ? (
              <div key={option.id} className='bakasana-contact-option'>
                <h3>{option.title}</h3>
                <p>{option.description}</p>
                {option.component}
              </div>
            ) : (
              <a
                key={option.id}
                href={option.href}
                target={option.target}
                rel={
                  option.target === '_blank' ? 'noopener noreferrer' : undefined
                }
                className='bakasana-contact-option'
                aria-label={`Skontaktuj się przez ${option.title}`}
              >
                <h3>{option.title}</h3>
                <p>{option.description}</p>
              </a>
            )
          )}
        </div>

        {/* OM SYMBOL */}
        <div className='bakasana-contact-om'>ॐ</div>
      </div>
    </section>
  );
});
ContactSection.displayName = 'ContactSection';

// =============================================
// 8. FOOTER - MINIMALISTYCZNY
// =============================================

const BakasanaFooter = React.memo(() => {
  const socialLinks = useMemo(
    () => [
      {
        id: 'instagram',
        label: 'Instagram',
        href: 'https://instagram.com/bakasana.retreats',
        icon: 'IG',
      },
      {
        id: 'facebook',
        label: 'Facebook',
        href: 'https://facebook.com/bakasana.retreats',
        icon: 'FB',
      },
      {
        id: 'whatsapp',
        label: 'WhatsApp',
        component: (
          <PerformantWhatsApp
            variant='icon'
            className='text-enterprise-brown hover:text-terra'
          />
        ),
        href: null, // używamy komponentu zamiast href
        icon: 'WA',
      },
    ],
    []
  );

  return (
    <footer className='bakasana-footer'>
      <div className='container'>
        {/* LOGO */}
        <h3 className='bakasana-footer-logo'>BAKASANA</h3>

        {/* SUBTITLE */}
        <p className='bakasana-footer-subtitle'>
          Joga • Retreaty • Życie w równowadze
        </p>

        {/* SOCIAL IKONY */}
        <div className='bakasana-footer-social'>
          {socialLinks.map(link =>
            link.component ? (
              <div key={link.id} className='social-icon-wrapper'>
                {link.component}
              </div>
            ) : (
              <a
                key={link.id}
                href={link.href}
                target='_blank'
                rel='noopener noreferrer'
                aria-label={`Odwiedź nas na ${link.label}`}
              >
                {link.icon}
              </a>
            )
          )}
        </div>

        {/* COPYRIGHT */}
        <p className='bakasana-footer-copyright'>
          © 2024 • Z miłością z Rzeszowa i Bali
        </p>
      </div>
    </footer>
  );
});
BakasanaFooter.displayName = 'BakasanaFooter';

// =============================================
// GŁÓWNY KOMPONENT - NOWA STRUKTURA
// =============================================
const WellnessPage = ({ latestPosts }) => {
  const organizationData = useMemo(() => getOrganizationStructuredData(), []);
  const instructorData = useMemo(() => getYogaInstructorStructuredData(), []);

  return (
    <div className='wellness-page bg-sanctuary'>
      {/* Advanced SEO Component */}
      <AdvancedSEO
        title='BAKASANA - Najlepsze Retreaty Jogi na Bali i Sri Lanka 2025 | Julia Jakubowicz'
        description='Odkryj transformacyjne retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz. Małe grupy, luksusowe hotele, daily joga, medytacja i ayurveda. Ubud, Gili Air, Sigiriya - rezervuj już dziś!'
        keywords='retreaty jogi bali 2025, joga sri lanka, julia jakubowicz joga, ubud yoga retreat, gili air joga, sigiriya yoga, transformacyjne podróże, medytacja bali, ayurveda sri lanka, joga wakacje azja, duchowa podróż bali, yoga teacher training'
        structuredData={[organizationData, instructorData]}
        canonicalUrl='https://bakasana-travel.blog'
        imageUrl='https://bakasana-travel.blog/images/og/bakasana-og-homepage-2025.jpg'
      />

      {/* 1. HERO SECTION - PROFESSIONAL STYLE */}
      <ProfessionalHero />

      {/* 2. INTRO SECTION */}
      <BakasanaIntroSection />

      {/* 3. TRZY ŚCIEŻKI */}
      <ThreePathsSection />

      {/* 4. NAJBLIŻSZE TERMINY */}
      <UpcomingRetreatsSection />

      {/* 5. ZAJĘCIA ONLINE - ZOSTAJE */}
      <OnlineClassesSection />

      {/* 6. O JULII */}
      <AboutJuliaSection />

      {/* 7. SOCIAL PROOF */}
      <SocialProofSection />

      {/* 8. KONTAKT */}
      <ContactSection />

      {/* 9. FOOTER */}
      <BakasanaFooter />

      {/* Structured Data dla SEO */}
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getOrganizationStructuredData()),
        }}
      />
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getYogaInstructorStructuredData()),
        }}
      />
    </div>
  );
};

export default WellnessPage;
