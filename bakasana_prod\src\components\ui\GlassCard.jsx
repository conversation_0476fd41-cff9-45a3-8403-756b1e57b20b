'use client';
/**
import React, { forwardRef } from 'react';

import { cn  } from '@/lib/utils';
import { useScrollReveal, useReducedMotion  } from '@/hooks/useAdvancedAnimations';


 * 🔮 BAKASANA - GLASS CARD COMPONENT
 * 
 * Premium glassmorphism card with:
 * - Backdrop blur effects
 * - Subtle borders and shadows
 * - Hover interactions
 * - Responsive design
 * - Accessibility features
 * 
 * Usage:
 * <GlassCard intensity="medium" hover="lift">
 *   <h3>Card Title</h3>
 *   <p>Card content</p>
 * </GlassCard>
 */

const GlassCard = forwardRef(
  (
    {
      children,
      intensity = 'medium',
      hover = 'lift',
      className,
      reveal = false,
      magnetic = false,
      ...props
    },
    ref
  ) => {
    const [revealRef, isRevealed] = useScrollReveal();
    const prefersReducedMotion = useReducedMotion();

    // Intensity levels for glassmorphism
    const intensities = {
      light: {
        background: 'rgba(253, 252, 248, 0.4)',
        backdrop: 'blur(8px)',
        border: 'rgba(255, 255, 255, 0.1)',
        shadow: '0 4px 16px rgba(42, 39, 36, 0.04)',
      },
      medium: {
        background: 'rgba(253, 252, 248, 0.7)',
        backdrop: 'blur(20px)',
        border: 'rgba(255, 255, 255, 0.18)',
        shadow: '0 8px 32px rgba(42, 39, 36, 0.06)',
      },
      strong: {
        background: 'rgba(253, 252, 248, 0.9)',
        backdrop: 'blur(30px)',
        border: 'rgba(255, 255, 255, 0.25)',
        shadow: '0 12px 48px rgba(42, 39, 36, 0.08)',
      },
    };

    // Hover effects
    const hoverEffects = {
      none: '',
      lift:
        !prefersReducedMotion &&
        'hover:transform hover:-translate-y-2 hover:shadow-2xl',
      scale: !prefersReducedMotion && 'hover:scale-105',
      glow:
        !prefersReducedMotion &&
        'hover:shadow-charcoal-gold/20 hover:shadow-2xl',
      tilt:
        !prefersReducedMotion &&
        'hover:transform hover:rotate-1 hover:scale-105',
    };

    const currentIntensity = intensities[intensity] || intensities.medium;
    const currentHover = hoverEffects[hover] || hoverEffects.lift;

    const baseClasses = cn(
      // Base styling
      'relative overflow-hidden rectangular',
      'border border-solid',
      'transition-all duration-300 ease-out',
      'will-change-transform',

      // Glassmorphism effects
      'backdrop-blur-lg',
      '-webkit-backdrop-filter: blur(20px)',

      // Hover effects
      currentHover,

      // Magnetic effect
      magnetic && !prefersReducedMotion && 'magnetic-element',

      // Reveal animation
      reveal && 'reveal-on-scroll',
      reveal && isRevealed && 'revealed',

      // Custom className
      className
    );

    const cardRef = reveal ? revealRef : ref;

    return (
      <div
        ref={cardRef}
        className={baseClasses}
        style={{
          background: currentIntensity.background,
          backdropFilter: currentIntensity.backdrop,
          WebkitBackdropFilter: currentIntensity.backdrop,
          borderColor: currentIntensity.border,
          boxShadow: currentIntensity.shadow,
        }}
        {...props}
      >
        {/* Subtle gradient overlay */}
        <div
          className='absolute inset-0 opacity-30 pointer-events-none'
          style={{
            background: `linear-gradient(
            135deg,
            rgba(184, 147, 92, 0.1) 0%,
            rgba(212, 175, 55, 0.05) 50%,
            rgba(184, 147, 92, 0.1) 100%
          )`,
          }}
        />

        {/* Content */}
        <div className='relative z-10 p-6'>{children}</div>

        {/* Hover glow effect */}
        <div className='absolute inset-0 opacity-0 transition-opacity duration-300 hover:opacity-100 pointer-events-none'>
          <div
            className='absolute inset-0 rectangular'
            style={{
              background: `radial-gradient(
              circle at 50% 50%,
              rgba(184, 147, 92, 0.1) 0%,
              transparent 70%
            )`,
            }}
          />
        </div>

        {/* Animated border on hover */}
        <div className='absolute inset-0 rectangular opacity-0 transition-opacity duration-300 hover:opacity-100 pointer-events-none'>
          <div
            className='absolute inset-0 rectangular'
            style={{
              background: `linear-gradient(
              90deg,
              transparent,
              rgba(184, 147, 92, 0.5),
              transparent
            )`,
              backgroundSize: '200% 100%',
              animation: !prefersReducedMotion
                ? 'borderGlow 3s ease-in-out infinite'
                : 'none',
            }}
          />
        </div>
      </div>
    );
  }
);

GlassCard.displayName = 'GlassCard';

// Add required styles to document
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes borderGlow {
      0%, 100% {
        background-position: -200% 0;
      }
      50% {
        background-position: 200% 0;
      }
    }
    
    .reveal-on-scroll {
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .reveal-on-scroll.revealed {
      opacity: 1;
      transform: translateY(0);
    }
    
    @supports (backdrop-filter: blur(20px)) {
      .glass-card {
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
      }
    }
    
    @media (prefers-reduced-motion: reduce) {
      .reveal-on-scroll {
        opacity: 1;
        transform: none;
        transition: none;
      }
      
      .magnetic-element {
        transition: none;
      }
    }
  `;
  document.head.appendChild(style);
}

export default GlassCard;
