'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

/**
 * LuxuryScrollProgress - Luksusowy pasek postępu przewijania
 * Z subtelnym złotym gradientem i płynną animacją
 */

export default function LuxuryScrollProgress({
  height = 3,
  showPercentage = false,
  goldAccent = true,
  className = '',
}) {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateScrollProgress = () => {
      if (typeof window !== 'undefined') {
        const scrolled = window.scrollY;
        const maxScroll =
          document.documentElement.scrollHeight - window.innerHeight;
        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;

        setScrollProgress(progress);
        setIsVisible(scrolled > 100); // Pokaż po przewinięciu 100px
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', updateScrollProgress, {
        passive: true,
      });
      updateScrollProgress(); // Initial call

      return () => window.removeEventListener('scroll', updateScrollProgress);
    }
  }, []);

  return (
    <motion.div
      className={`fixed top-0 left-0 right-0 z-40 ${className}`}
      initial={{ opacity: 0, y: -height }}
      animate={{
        opacity: isVisible ? 1 : 0,
        y: isVisible ? 0 : -height,
      }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      {/* Background */}
      <div
        className='w-full bg-stone-light/20 backdrop-blur-sm'
        style={{ height: `${height}px` }}
      >
        {/* Progress bar */}
        <motion.div
          className={`h-full ${
            goldAccent
              ? 'bg-gradient-to-r from-enterprise-brown via-terra to-sand'
              : 'bg-charcoal'
          }`}
          style={{
            scaleX: scrollProgress,
            transformOrigin: '0%',
          }}
          transition={{ duration: 0.1, ease: 'easeOut' }}
        />

        {/* Subtle glow effect */}
        {goldAccent && (
          <motion.div
            className='absolute top-0 left-0 h-full bg-gradient-to-r from-enterprise-brown/30 via-terra/30 to-sand/30 blur-sm'
            style={{
              scaleX: scrollProgress,
              transformOrigin: '0%',
              width: '100%',
            }}
            transition={{ duration: 0.1, ease: 'easeOut' }}
          />
        )}
      </div>

      {/* Percentage indicator */}
      {showPercentage && isVisible && (
        <motion.div
          className='absolute top-full right-4 mt-2 px-3 py-1 bg-sanctuary/90 backdrop-blur-sm rounded-full shadow-elegant border border-enterprise-brown/10'
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <span className='text-xs font-inter font-medium text-enterprise-brown'>
            {Math.round(scrollProgress * 100)}%
          </span>
        </motion.div>
      )}
    </motion.div>
  );
}

// Wariant z sekcjami
export function SectionScrollProgress({
  sections = [],
  goldAccent = true,
  className = '',
}) {
  const [activeSection, setActiveSection] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateProgress = () => {
      if (typeof window !== 'undefined') {
        const scrolled = window.scrollY;
        const maxScroll =
          document.documentElement.scrollHeight - window.innerHeight;
        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;

        setScrollProgress(progress);

        // Determine active section
        const sectionElements = sections
          .map(section => document.getElementById(section.id))
          .filter(Boolean);

        let currentSection = 0;
        sectionElements.forEach((element, index) => {
          if (element) {
            const rect = element.getBoundingClientRect();
            if (rect.top <= window.innerHeight / 2) {
              currentSection = index;
            }
          }
        });

        setActiveSection(currentSection);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', updateProgress, { passive: true });
      updateProgress();

      return () => window.removeEventListener('scroll', updateProgress);
    }
  }, [sections]);

  if (sections.length === 0) {
    return (
      <LuxuryScrollProgress goldAccent={goldAccent} className={className} />
    );
  }

  return (
    <motion.div
      className={`fixed top-0 left-0 right-0 z-40 ${className}`}
      initial={{ opacity: 0, y: -4 }}
      animate={{ opacity: scrollProgress > 0.05 ? 1 : 0, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Main progress bar */}
      <div className='h-1 bg-stone-light/20 backdrop-blur-sm'>
        <motion.div
          className={`h-full ${
            goldAccent
              ? 'bg-gradient-to-r from-enterprise-brown via-terra to-sand'
              : 'bg-charcoal'
          }`}
          style={{
            scaleX: scrollProgress,
            transformOrigin: '0%',
          }}
          transition={{ duration: 0.1 }}
        />
      </div>

      {/* Section indicators */}
      <div className='absolute top-full left-0 right-0 flex justify-center mt-2'>
        <div className='flex space-x-2 px-4 py-2 bg-sanctuary/90 backdrop-blur-sm rounded-full shadow-elegant border border-enterprise-brown/10'>
          {sections.map((section, index) => (
            <motion.button
              key={section.id}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === activeSection
                  ? goldAccent
                    ? 'bg-enterprise-brown scale-125'
                    : 'bg-charcoal scale-125'
                  : 'bg-stone-light hover:bg-stone'
              }`}
              onClick={() => {
                const element = document.getElementById(section.id);
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              title={section.title}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}

// Wariant minimalny
export function MinimalScrollProgress({ goldAccent = true }) {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateProgress = () => {
      if (typeof window !== 'undefined') {
        const scrolled = window.scrollY;
        const maxScroll =
          document.documentElement.scrollHeight - window.innerHeight;
        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;
        setScrollProgress(progress);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', updateProgress, { passive: true });
      updateProgress();

      return () => window.removeEventListener('scroll', updateProgress);
    }
  }, []);

  return (
    <div className='fixed top-0 left-0 right-0 h-0.5 z-40'>
      <motion.div
        className={`h-full ${
          goldAccent
            ? 'bg-gradient-to-r from-enterprise-brown to-terra'
            : 'bg-charcoal'
        }`}
        style={{
          scaleX: scrollProgress,
          transformOrigin: '0%',
        }}
        transition={{ duration: 0.1 }}
      />
    </div>
  );
}
