{"timestamp": "2025-07-24T19:16:41.747Z", "summary": {"totalFiles": 12, "totalSize": 226420, "totalClasses": 811, "totalUsedClasses": 0, "overallUsage": 0}, "files": [{"file": "main.css", "fileSize": 6398, "classes": {"total": 21, "used": 0, "unused": 21, "usagePercent": 0}, "ids": {"total": 15, "used": 0, "unused": 15, "usagePercent": 0}, "variables": {"total": 30, "used": 0, "unused": 30, "usagePercent": 0}, "unusedClasses": ["css", "skip-link", "font-primary", "font-secondary", "font-cormorant", "font-inter", "visually-hidden", "text-center", "text-left", "text-right", "uppercase", "lowercase", "capitalize", "container-query", "backdrop-blur", "grid-fallback", "will-change-transform", "will-change-opacity", "contain-layout", "contain-paint", "contain-strict"], "unusedIds": ["FDFCF8", "F9F7F3", "FAF8F4", "F6F2E8", "F8F5F0", "F4F0E8", "B5B0A8", "A8B5A8", "D2CDC6", "B8935C", "A0845C", "D4AF37", "FFFFFF", "FFD700", "CD853F"], "unusedVariables": ["--sanctuary", "--whisper", "--rice", "--linen", "--pearl", "--silk", "--charcoal", "--charcoal-light", "--stone", "--stone-light", "--sage", "--ash", "--temple-gold", "--enterprise-brown", "--terra", "--golden-amber", "--pure-white", "--soft-black", "--font-primary", "--font-secondary", "--container-max", "--section-padding", "--element-breathing", "--card-internal", "--micro-spacing", "--shadow-subtle", "--shadow-elevated", "--shadow-premium", "--transition-base", "--transition-smooth"]}, {"file": "hero.css", "fileSize": 9542, "classes": {"total": 33, "used": 0, "unused": 33, "usagePercent": 0}, "ids": {"total": 1, "used": 0, "unused": 1, "usagePercent": 0}, "variables": {"total": 19, "used": 0, "unused": 19, "usagePercent": 0}, "unusedClasses": ["hero-section", "hero-bg", "webp", "hero-parallax-layer", "hero-gradient-overlay", "hero-gradient-overlay--interactive", "hero-texture-overlay", "w3", "org", "hero-content", "hero-badge", "hero-title", "hero-subtitle", "hero-description", "hero-stats", "hero-stat", "hero-stat-number", "hero-stat-label", "hero-buttons", "hero-button", "hero-button--whatsapp", "hero-side-form", "form-button", "hero-floating-elements", "hero-floating-dot", "hero-fade-in", "hero-fade-in--delay-1", "hero-fade-in--delay-2", "hero-fade-in--delay-3", "hero-fade-in--delay-4", "hero-fade-in--delay-5", "hero-fade-in--delay-6", "hero-fade-in--delay-7"], "unusedIds": ["FCF6EE"], "unusedVariables": ["--interactive", "--mouse-x", "--mouse-y", "--enterprise-brown", "--charcoal", "--charcoal-light", "--sage", "--whatsapp", "--pearl", "--ash", "--terra", "--temple-gold", "--delay-1", "--delay-2", "--delay-3", "--delay-4", "--delay-5", "--delay-6", "--delay-7"]}, {"file": "sections.css", "fileSize": 7710, "classes": {"total": 46, "used": 0, "unused": 46, "usagePercent": 0}, "ids": {"total": 0, "used": 0, "unused": 0, "usagePercent": 0}, "variables": {"total": 20, "used": 0, "unused": 20, "usagePercent": 0}, "unusedClasses": ["section", "section--large", "section--medium", "section--small", "section-container", "section-content", "section-header", "section-subtitle", "section--linen", "section--sanctuary", "section--whisper", "section--pearl", "section--silk", "section-grid", "section-grid--2", "section-grid--3", "section-grid--4", "section-card", "section-card-title", "section-card-text", "section-card-link", "arrow", "testimonial-card", "testimonial-rating", "testimonial-text", "testimonial-author", "faq-item", "faq-question", "faq-answer", "stats-grid", "stat-item", "stat-number", "stat-label", "section-button", "section-button--primary", "section-button--secondary", "spacing-micro", "spacing-small", "spacing-medium", "spacing-large", "spacing-xlarge", "section-fade-in", "section-fade-in--delay-1", "section-fade-in--delay-2", "section-fade-in--delay-3", "section-fade-in--delay-4"], "unusedIds": [], "unusedVariables": ["--large", "--medium", "--small", "--charcoal", "--sage", "--linen", "--sanctuary", "--whisper", "--pearl", "--silk", "--enterprise-brown", "--terra", "--charcoal-light", "--primary", "--secondary", "--delay-1", "--delay-2", "--delay-3", "--delay-4", "--temple-gold"]}, {"file": "modern-css.css", "fileSize": 9553, "classes": {"total": 40, "used": 0, "unused": 40, "usagePercent": 0}, "ids": {"total": 0, "used": 0, "unused": 0, "usagePercent": 0}, "variables": {"total": 13, "used": 0, "unused": 13, "usagePercent": 0}, "unusedClasses": ["container-query", "card-container", "sidebar-container", "container-responsive-text", "container-responsive-heading", "card-content", "card-image", "sidebar-nav", "sidebar-nav-item", "grid-parent", "grid-child-subgrid", "card-grid-parent", "card-with-subgrid", "card-grid-container", "full-height-dynamic", "hero-dynamic", "full-height-large", "full-height-small", "logical-spacing", "logical-text", "nested-component", "nested-title", "nested-subtitle", "nested-content", "advanced-custom-properties", "scroll-driven-animation", "parallax-scroll", "anchor-element", "anchored-tooltip", "masonry-grid", "masonry-item", "modern-css-base", "modern-btn", "modern-card", "modern-sr-only", "modern-focus-visible", "glass-effect", "wide-gamut-colors", "variable-font", "variable-font-bold"], "unusedIds": [], "unusedVariables": ["--enterprise-brown", "--stone", "--sanctuary", "--charcoal", "--whisper", "--shadow-layers", "--gradient-complex", "--linen", "--animation-sequence", "--anchor", "--tooltip-anchor", "--font-secondary", "--shadow-subtle"]}, {"file": "advanced-typography.css", "fileSize": 7601, "classes": {"total": 24, "used": 0, "unused": 24, "usagePercent": 0}, "ids": {"total": 0, "used": 0, "unused": 0, "usagePercent": 0}, "variables": {"total": 23, "used": 0, "unused": 23, "usagePercent": 0}, "unusedClasses": ["heading-display", "heading-hero", "heading-section", "heading-subsection", "heading-card", "heading-small", "text-lead", "text-body", "text-small", "text-caption", "text-spacing-tight", "text-spacing-normal", "text-spacing-relaxed", "text-spacing-loose", "text-responsive-center", "text-responsive-left", "text-responsive-right", "typography-container", "reading-width", "reading-optimized", "variable-font", "variable-font-light", "variable-font-bold", "text-spacing-optimized"], "unusedIds": [], "unusedVariables": ["--text-xs", "--text-sm", "--text-base", "--text-lg", "--text-xl", "--text-2xl", "--text-3xl", "--text-4xl", "--text-5xl", "--text-6xl", "--text-7xl", "--leading-none", "--leading-tight", "--leading-snug", "--leading-normal", "--leading-relaxed", "--leading-loose", "--tracking-tighter", "--tracking-tight", "--tracking-normal", "--tracking-wide", "--tracking-wider", "--tracking-widest"]}, {"file": "microinteractions.css", "fileSize": 14388, "classes": {"total": 35, "used": 0, "unused": 35, "usagePercent": 0}, "ids": {"total": 3, "used": 0, "unused": 3, "usagePercent": 0}, "variables": {"total": 35, "used": 0, "unused": 35, "usagePercent": 0}, "unusedClasses": ["com", "app", "btn-primary", "card-interactive", "tilt-active", "link-enhanced", "form-field", "reveal-on-scroll", "revealed", "reveal-stagger", "magnetic-element", "cursor-follower", "expanded", "glass-card", "loading-shimmer", "parallax-container", "parallax-layer", "parallax-slow", "parallax-medium", "parallax-fast", "focus-visible", "will-change-transform", "will-change-opacity", "gpu-accelerated", "animate-fade-in-up", "animate-fade-in-scale", "animate-slide-in-right", "animate-bounce-in", "animate-float", "touch-feedback", "enhanced-glass", "unified-card-premium", "link-premium", "btn-ripple", "container-animations"], "unusedIds": ["C4996B", "B8935C", "e74c3c"], "unusedVariables": ["--duration-instant", "--duration-quick", "--duration-medium", "--duration-slow", "--duration-extended", "--ease-smooth", "--ease-bounce", "--ease-swift", "--ease-elastic", "--ease-premium", "--origin-center", "--origin-top", "--origin-bottom", "--origin-left", "--origin-right", "--scale-hover", "--scale-active", "--scale-focus", "--mouse-x", "--mouse-y", "--entrance-duration", "--spring-elastic", "--micro-duration", "--spring-smooth", "--temple-gold", "--standard-duration", "--golden-amber", "--stone", "--sanctuary", "--whisper", "--parallax-slow", "--parallax-medium", "--parallax-fast", "--golden", "--sunset"]}, {"file": "premium-utilities.css", "fileSize": 6077, "classes": {"total": 36, "used": 0, "unused": 36, "usagePercent": 0}, "ids": {"total": 8, "used": 0, "unused": 8, "usagePercent": 0}, "variables": {"total": 7, "used": 0, "unused": 7, "usagePercent": 0}, "unusedClasses": ["animate-premium-fade-in", "animate-premium-slide-up", "animate-premium-scale-in", "hover-lift", "hover-glow", "hover-magnetic", "focus-premium", "loading-shimmer", "loading-pulse-premium", "glass-premium", "glass-dark", "gradient-premium", "gradient-warm", "gradient-sunset", "shadow-premium", "shadow-premium-hover", "shadow-premium-focus", "text-premium", "text-balance", "text-pretty", "container-premium", "container-narrow", "grid-premium", "grid-masonry", "scroll-smooth-premium", "scroll-snap-premium", "sr-only-premium", "skip-link-premium", "will-change-premium", "contain-premium", "gpu-accelerated", "mobile-optimized", "desktop-enhanced", "print-hidden", "print-optimized", "respect-motion-preference"], "unusedIds": ["FDFCF8", "F9F7F3", "F5F2ED", "C19B68", "B8935C", "A67C52", "E6C18A", "D4AF7A"], "unusedVariables": ["--duration-medium", "--ease-premium", "--duration-quick", "--duration-instant", "--ease-swift", "--charcoal", "--silk"]}, {"file": "unified-system.css", "fileSize": 7698, "classes": {"total": 30, "used": 0, "unused": 30, "usagePercent": 0}, "ids": {"total": 4, "used": 0, "unused": 4, "usagePercent": 0}, "variables": {"total": 17, "used": 0, "unused": 17, "usagePercent": 0}, "unusedClasses": ["btn-unified", "card-unified", "input-unified", "animate-fade-in-organic", "animate-stagger-1", "animate-stagger-2", "animate-stagger-3", "animate-stagger-4", "animate-warm-pulse", "section-organic", "container-organic", "text-warm-primary", "text-warm-secondary", "text-warm-accent", "text-warm-muted", "bg-warm-primary", "bg-warm-accent", "bg-warm-muted", "space-organic-xs", "space-organic-sm", "space-organic-md", "space-organic-lg", "space-organic-xl", "shadow-warm-subtle", "shadow-warm-elegant", "shadow-warm-premium", "border-warm", "border-warm-strong", "transition-organic", "transition-warm"], "unusedIds": ["FDFCF8", "B8935C", "A8A39E", "FFFFFF"], "unusedVariables": ["--warm-sanctuary", "--warm-charcoal", "--warm-enterprise", "--warm-terra", "--warm-sage", "--warm-stone", "--organic-xs", "--organic-sm", "--organic-md", "--organic-lg", "--organic-xl", "--organic-2xl", "--shadow-warm-subtle", "--shadow-warm-elegant", "--shadow-warm-premium", "--transition-organic", "--transition-warm"]}, {"file": "color-migration.css", "fileSize": 6641, "classes": {"total": 45, "used": 0, "unused": 45, "usagePercent": 0}, "ids": {"total": 1, "used": 0, "unused": 1, "usagePercent": 0}, "variables": {"total": 32, "used": 0, "unused": 32, "usagePercent": 0}, "unusedClasses": ["text-charcoal", "text-charcoal-gold", "text-sand", "text-sand-lotus", "text-charcoal-light", "bg-charcoal", "bg-charcoal-gold", "bg-sand", "bg-sanctuary", "bg-whisper", "bg-silk", "border-charcoal", "border-charcoal-gold", "border-sand", "bg-yellow-50", "border-yellow-200", "text-yellow-800", "text-yellow-700", "bg-red-50", "border-red-200", "text-red-800", "text-red-700", "bg-blue-50", "border-blue-200", "text-blue-800", "text-blue-700", "bg-green-50", "border-green-200", "text-green-800", "text-green-700", "bg-purple-50", "border-purple-200", "text-purple-800", "text-purple-700", "hover", "prose-headings", "prose", "prose-p", "prose-a", "prose-strong", "prose-blockquote", "prose-ul", "prose-ol", "config", "js"], "unusedIds": ["B85450"], "unusedVariables": ["--temple", "--warm-charcoal", "--temple-light", "--charcoal-light", "--temple-dark", "--soft-black", "--temple-gold", "--warm-enterprise", "--golden", "--warm-terra", "--golden-lotus", "--golden-amber", "--wood-light", "--wood-dark", "--rice", "--warm-sanctuary", "--mist", "--warm-linen", "--shell", "--warm-silk", "--blog-info", "--blog-info-bg", "--blog-info-border", "--blog-warning", "--blog-warning-bg", "--blog-warning-border", "--blog-success", "--blog-success-bg", "--blog-success-border", "--blog-error", "--blog-error-bg", "--blog-error-border"]}, {"file": "globals.css", "fileSize": 118953, "classes": {"total": 396, "used": 0, "unused": 396, "usagePercent": 0}, "ids": {"total": 25, "used": 0, "unused": 25, "usagePercent": 0}, "variables": {"total": 77, "used": 0, "unused": 77, "usagePercent": 0}, "unusedClasses": ["css", "skip-link", "animate-fade-in", "whatsapp-elegant", "whatsapp-float", "whatsapp-icon", "whatsapp-enhanced", "js", "googlea<PERSON>", "com", "rectangular", "rectangular-subtle", "rectangular-elevated", "rectangular-button", "rectangular-input", "rectangular-card", "btn", "button", "card", "badge", "elegant-border", "hero-element", "enterprise-fade", "golden-spacing", "golden-padding", "logo", "hero-title", "section-header", "card-title", "body-text", "nav-link", "subtle-text", "online-section", "online-title", "mobile-menu-button", "hamburger-container", "hamburger-line", "open", "mobile-menu-overlay", "mobile-menu", "mobile-menu-content", "mobile-nav-links", "mobile-nav-item", "mobile-nav-link", "active", "online-classes", "mobile-dropdown", "mobile-dropdown-content", "mobile-dropdown-section", "mobile-dropdown-header", "sub-link", "navigation", "scrolled", "nav-menu", "dropdown-menu", "dropdown-item", "dropdown-header", "online-subtitle", "online-features", "online-feature", "feature-icon", "feature-title", "feature-description", "destination-card", "retreat-card", "blog-card", "hero", "hero-content", "hero-subtitle", "hero-quote", "hero-meta", "hero-cta", "hero-cta-link", "subtle-link", "container", "section", "section-breathe", "section-smooth", "section-warm", "section-spiritual", "section-testimonials", "destinations", "destinations-grid", "card-image", "destination-image", "card-overlay", "card-content", "card-meta", "card-description", "card-details", "btn-ghost", "btn-primary", "btn-accent", "section-divider", "about-julia-section", "divider-line", "divider-symbol", "about-julia-content", "julia-image-wrapper", "julia-image", "about-julia-title", "title-accent", "julia-quote", "stats-container", "stat-box", "font-primary", "font-secondary", "font-accent", "footer", "footer-content", "spiritual-greeting", "footer-links", "footer-link", "social-links", "social-icon", "copyright", "testimonial-card", "julia-signature", "signature-line", "signature-text", "testimonials-section", "contact-section", "contact-cards", "contact-card", "blog-section", "blog-cards-grid", "online-cta", "online-classes-card", "font-cormorant", "font-inter", "letter-spacing-wide", "letter-spacing-wider", "text-sanctuary", "text-charcoal", "text-stone", "text-charcoal-gold", "text-sage-green", "bg-sanctuary", "bg-charcoal", "bg-whisper", "container-responsive", "section-padding", "container-padding", "breathe-spacing", "text-center", "max-width-content", "opacity-whisper", "opacity-subtle", "opacity-soft", "opacity-visible", "nav-links", "about-container", "hover-element", "smooth-transition", "transition-instant", "transition-quick", "transition-medium", "transition-slow", "gpu-acceleration", "focus-visible", "sr-only", "mandala-outer", "mandala-middle", "mandala-inner", "om-symbol", "balinese-greeting", "temple-gold-text", "sage-text", "sri-lankan-accent", "lotus-divider", "retreat-image", "retreat-image-container", "sacred-quote", "enterprise-image", "sacred-divider", "sacred-divider-content", "about-page-elegant", "about-hero-refined", "w3", "org", "about-hero-container", "about-hero-ornament", "about-hero-content-elegant", "about-hero-name", "about-hero-divider", "about-hero-credentials", "about-hero-quote", "about-content-refined", "about-content-container", "about-content-grid", "about-photo-column", "about-photo-frame", "about-photo-placeholder", "about-photo-ornament", "about-photo-ornament-top", "about-photo-ornament-bottom", "about-photo-content", "about-photo-text", "about-photo-label", "about-photo-name", "about-text-column", "about-text-content", "about-text-intro", "about-text-paragraph", "about-credentials-refined", "about-credentials-container", "about-credentials-header", "about-credentials-title", "about-credentials-subtitle", "about-credentials-grid-refined", "about-credential-card", "about-credential-icon-refined", "about-credential-content", "about-credential-label-refined", "about-credential-value-refined", "about-credential-description", "about-journeys-refined", "about-journeys-container", "about-journeys-header-refined", "about-journeys-title-refined", "about-journeys-ornament", "about-journeys-description-refined", "about-journeys-grid-refined", "about-journey-card-refined", "about-journey-header", "about-journey-title-refined", "about-journey-subtitle", "about-journey-content", "about-journey-description-refined", "about-journey-link-refined", "about-cta-refined", "about-cta-container", "about-cta-content-refined", "about-cta-ornament", "about-cta-title-refined", "about-cta-description-refined", "about-cta-button-refined", "about-content-centered", "about-content-container-centered", "about-photo-centered", "about-photo-frame-small", "about-photo-placeholder-small", "about-photo-glow", "about-text-centered", "about-text-content-centered", "about-text-paragraph-centered", "about-text-column-left", "about-text-column-right", "about-journeys-centered", "about-journeys-container-centered", "about-journeys-header-centered", "about-journeys-title-centered", "about-journeys-description-centered", "about-journeys-card-single", "about-journey-card-centered", "about-journey-header-centered", "about-journey-title-centered", "about-journey-subtitle-centered", "about-journey-content-centered", "about-journey-description-centered", "about-journey-button-centered", "about-page-integrated", "content-separator", "about-hero-minimal", "about-hero-container-minimal", "about-hero-content-minimal", "about-hero-name-minimal", "about-hero-credentials-minimal", "about-hero-quote-minimal", "about-content-integrated", "about-content-container-integrated", "about-photo-integrated", "about-photo-frame-integrated", "about-photo-placeholder-integrated", "about-text-integrated", "about-text-content-integrated", "about-text-paragraph-integrated", "about-story-integrated", "about-story-container", "about-story-title", "about-story-content", "about-story-paragraph", "about-credentials-integrated", "about-credentials-container-integrated", "about-credentials-title-integrated", "about-credentials-list-integrated", "about-credential-item-integrated", "about-credential-marker", "about-credential-content-integrated", "about-credential-label-integrated", "about-credential-description-integrated", "about-journeys-integrated", "about-journeys-container-integrated", "about-journeys-title-integrated", "about-journeys-content-integrated", "about-journeys-description-integrated", "about-journeys-destinations", "about-destination-item", "about-destination-name", "about-destination-description", "about-journey-button-integrated", "about-online-integrated", "about-online-container-integrated", "about-online-title-integrated", "about-online-content-integrated", "about-online-description-integrated", "about-online-methods", "about-online-method-item", "about-online-method-icon", "about-online-method-content", "about-online-method-title", "about-online-method-description", "about-online-platforms", "about-online-platforms-title", "about-online-platforms-list", "about-online-platform-item", "about-online-button-integrated", "about-cta-integrated", "about-cta-container-integrated", "about-cta-content-integrated", "about-cta-title-integrated", "about-cta-description-integrated", "about-cta-button-integrated", "magazine-hero", "magazine-hero-content", "magazine-header-line", "magazine-title", "magazine-subtitle", "magazine-meta", "magazine-content", "magazine-grid", "magazine-featured", "magazine-secondary", "magazine-grid-small", "magazine-card", "magazine-card-featured", "magazine-card-link", "magazine-card-image", "magazine-image-bg", "magazine-image-overlay", "magazine-category", "magazine-card-content", "magazine-card-title", "magazine-card-excerpt", "magazine-card-footer", "magazine-read-more", "magazine-read-time", "magazine-empty", "magazine-empty-content", "magazine-empty-title", "magazine-empty-text", "text-balance", "smooth-scroll", "backdrop-blur-subtle", "glass-effect", "elegant-shadow", "elegant-shadow-hover", "gradient-text", "spiritual-ornament", "micro-interaction", "enterprise-card", "section-transition", "will-change-transform", "will-change-opacity", "gpu-layer", "focus-visible-only", "destinations-section", "destination-image-wrapper", "destination-title", "destination-meta", "destination-description", "destination-testimonial", "destination-link", "about-julia-simple", "about-julia-container", "julia-portrait-wrapper", "julia-portrait", "julia-title", "divider-gold", "julia-bio", "text-gold", "julia-cta", "contact-simple", "contact-container", "contact-title", "contact-subtitle", "contact-options", "contact-button", "dark-section", "subsection-header", "small-text", "text-main", "text-section", "text-primary", "text-accent", "text-footer"], "unusedIds": ["FDFCF8", "F9F7F3", "FAF8F4", "B8935C", "D4AF37", "FFFFFF", "B5B0A8", "A8B5A8", "B8C5D1", "C9A575", "FAF5F0", "FDF9F3", "E8E8E8", "e0e0e0", "a0a0a0", "fdfcf8", "f9f7f2", "f5f3ef", "f1efeb", "FAF8F5", "F5F0E8", "BE9561", "F5EDE4", "F5F5F5", "B8956A"], "unusedVariables": ["--charcoal", "--sanctuary", "--font-secondary", "--duration-quick", "--ease-premium", "--temple-gold", "--container-max", "--section-padding", "--element-breathing", "--card-internal", "--micro-spacing", "--nano-spacing", "--hero-spacing", "--whisper-spacing", "--container-padding-mobile", "--container-padding-tablet", "--container-padding-desktop", "--container-padding-large", "--container-padding-ultra", "--mobile", "--tablet", "--desktop", "--large", "--ultra", "--stone", "--whisper", "--rice", "--golden-amber", "--pure-white", "--soft-black", "--charcoal-light", "--stone-light", "--sage-whisper", "--ocean-mist", "--glass-nav", "--subtle-shadow", "--hover-overlay", "--font-primary", "--font-accent", "--text-xs", "--text-sm", "--text-base", "--text-lg", "--text-xl", "--text-2xl", "--text-3xl", "--text-4xl", "--text-5xl", "--text-6xl", "--text-7xl", "--text-8xl", "--font-ultra-light", "--font-whisper", "--font-light", "--font-normal", "--opacity-whisper", "--opacity-subtle", "--opacity-soft", "--opacity-visible", "--primary", "--secondary", "--background", "--accent", "--human-warmth", "--warm-black", "--warm-black-gradient", "--sanctuary-variant", "--sage-green", "--breathe-spacing", "--duration-instant", "--ease-swift", "--duration-medium", "--ease-smooth", "--duration-slow", "--ease-elastic", "--om-symbol", "--ocean-blue"]}, {"file": "enhanced-globals.css", "fileSize": 11351, "classes": {"total": 30, "used": 0, "unused": 30, "usagePercent": 0}, "ids": {"total": 11, "used": 0, "unused": 11, "usagePercent": 0}, "variables": {"total": 40, "used": 0, "unused": 40, "usagePercent": 0}, "unusedClasses": ["js", "googlea<PERSON>", "com", "text-brand-primary", "text-brand-secondary", "text-brand-accent", "heading-hero", "heading-section", "heading-subsection", "heading-card", "btn-primary", "btn-secondary", "btn-ghost", "card-elevated", "card-minimal", "section-divider", "section-divider-diamond", "animate-fade-in-up", "animate-fade-in-scale", "animate-float", "animate-shimmer", "sr-only", "skip-link", "gpu-accelerated", "will-change-transform", "will-change-opacity", "loading-skeleton", "loading-pulse", "smooth-scroll", "scroll-padding"], "unusedIds": ["C9A575", "B8956A", "D4B685", "FDFCF8", "F9F7F2", "F5F3EF", "E8E6E2", "F0EFEB", "A8B4A9", "D4AF37", "E6C65B"], "unusedVariables": ["--golden-lotus", "--deep-golden", "--light-golden", "--sanctuary", "--soft-sanctuary", "--warm-sanctuary", "--charcoal", "--soft-charcoal", "--light-charcoal", "--mist", "--soft-mist", "--sage", "--soft-sage", "--temple", "--soft-temple", "--font-primary", "--font-secondary", "--font-accent", "--space-xs", "--space-sm", "--space-md", "--space-lg", "--space-xl", "--space-2xl", "--space-3xl", "--space-4xl", "--space-5xl", "--shadow-subtle", "--shadow-soft", "--shadow-medium", "--shadow-large", "--shadow-golden", "--transition-fast", "--transition-normal", "--transition-slow", "--border-radius-sm", "--border-radius-md", "--border-radius-lg", "--border-radius-xl", "--border-radius-full"]}, {"file": "bakasana-visuals.css", "fileSize": 20508, "classes": {"total": 75, "used": 0, "unused": 75, "usagePercent": 0}, "ids": {"total": 7, "used": 0, "unused": 7, "usagePercent": 0}, "variables": {"total": 8, "used": 0, "unused": 8, "usagePercent": 0}, "unusedClasses": ["professional-hero", "professional-hero-badge", "professional-hero-button", "professional-hero-stats", "stat-value", "professional-hero-scroll", "b<PERSON><PERSON>a-hero", "webp", "avif", "baka<PERSON>a-hero-content", "b<PERSON><PERSON>a-hero-title", "bakasana-hero-subtitle", "b<PERSON><PERSON><PERSON>-hero-quote", "bakasana-scroll-hint", "baka<PERSON>a-intro", "bakasana-intro-divider", "baka<PERSON>a-intro-diamond", "b<PERSON><PERSON><PERSON>-intro-quote", "baka<PERSON><PERSON>-intro-text", "baka<PERSON>a-intro-button", "bakasana-paths", "b<PERSON><PERSON><PERSON>-paths-title", "bakasana-paths-grid", "bakasana-path-card", "bakasana-path-image", "bakasana-path-content", "bakasana-path-category", "baka<PERSON>a-path-title", "bakasana-path-description", "bakasana-path-price", "bakasana-retreats", "baka<PERSON>a-retreats-title", "bakasana-retreats-grid", "baka<PERSON>a-retreat-card", "bakasana-retreat-image", "bakasana-retreat-content", "baka<PERSON>a-retreat-date", "b<PERSON><PERSON><PERSON>-retreat-title", "bakasana-retreat-location", "bakasana-retreat-divider", "bakasana-retreat-features", "bakasana-retreat-price", "bakasana-retreat-cta", "bakasana-about-julia", "bakasana-julia-container", "bakasana-julia-text", "bakasana-julia-image-wrapper", "baka<PERSON>a-julia-quote", "baka<PERSON>a-julia-signature", "bakasana-julia-bio", "bakasana-julia-stats", "bakasana-julia-cta", "b<PERSON><PERSON><PERSON>-julia-photo", "bakasana-social", "baka<PERSON>a-social-handle", "bakasana-instagram-grid", "bakasana-instagram-item", "bakasana-social-cta", "bakasana-contact", "b<PERSON><PERSON><PERSON>-contact-title", "bakasana-contact-subtitle", "bakasana-contact-options", "bakasana-contact-option", "bakasana-contact-om", "baka<PERSON>a-footer", "baka<PERSON>a-footer-logo", "bakasana-footer-subtitle", "baka<PERSON>a-footer-social", "baka<PERSON><PERSON>-footer-copyright", "online-section", "feature-icon", "feature-title", "feature-description", "online-features", "online-feature"], "unusedIds": ["C19B68", "D1A46E", "C9A575", "f0f0f0", "B8935C", "FDF9F3", "F7F4F0"], "unusedVariables": ["--sanctuary", "--stone-light", "--temple-gold", "--charcoal", "--soft-black", "--pure-white", "--whisper", "--stone"]}]}