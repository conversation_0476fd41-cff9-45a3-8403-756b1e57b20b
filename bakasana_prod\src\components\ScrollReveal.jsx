'use client';

import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Warianty animacji
const fadeInUp = {
  hidden: {
    opacity: 0,
    y: 60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94], // Custom easing
    },
  },
};

const fadeInLeft = {
  hidden: {
    opacity: 0,
    x: -60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const fadeInRight = {
  hidden: {
    opacity: 0,
    x: 60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const staggerItem = {
  hidden: {
    opacity: 0,
    y: 30,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

// Komponenty animowane
export function ScrollReveal({
  children,
  variant = 'fadeInUp',
  delay = 0,
  threshold = 0.1,
  triggerOnce = true,
  className = '',
}) {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce,
  });

  const variants = {
    fadeInUp,
    fadeInLeft,
    fadeInRight,
  };

  return (
    <motion.div
      ref={ref}
      initial='hidden'
      animate={inView ? 'visible' : 'hidden'}
      variants={variants[variant]}
      transition={{ delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function StaggerContainer({
  children,
  threshold = 0.1,
  triggerOnce = true,
  className = '',
}) {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce,
  });

  return (
    <motion.div
      ref={ref}
      initial='hidden'
      animate={inView ? 'visible' : 'hidden'}
      variants={staggerContainer}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function StaggerItem({ children, className = '' }) {
  return (
    <motion.div variants={staggerItem} className={className}>
      {children}
    </motion.div>
  );
}

// Parallax effect dla obrazów
export function ParallaxImage({ src, alt, className = '', intensity = 0.5 }) {
  const [ref, inView] = useInView({
    threshold: 0,
    triggerOnce: false,
  });

  return (
    <motion.div
      ref={ref}
      className={`overflow-hidden ${className}`}
      style={{
        y: inView ? 0 : intensity * 100,
      }}
      transition={{
        duration: 0.8,
        ease: 'easeOut',
      }}
    >
      <motion.img
        src={src}
        alt={alt}
        className='w-full h-full object-cover'
        style={{
          scale: inView ? 1 : 1.1,
        }}
        transition={{
          duration: 1.2,
          ease: 'easeOut',
        }}
      />
    </motion.div>
  );
}

// Floating animation dla elementów
export function FloatingElement({
  children,
  intensity = 10,
  duration = 3,
  className = '',
}) {
  return (
    <motion.div
      className={className}
      animate={{
        y: [-intensity, intensity, -intensity],
        rotate: [-1, 1, -1],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      {children}
    </motion.div>
  );
}

// Magnetic hover effect dla przycisków
export function MagneticButton({
  children,
  className = '',
  intensity = 0.3,
  ...props
}) {
  return (
    <motion.button
      className={className}
      whileHover={{
        scale: 1.05,
        y: -2,
      }}
      whileTap={{
        scale: 0.95,
      }}
      transition={{
        type: 'spring',
        stiffness: 400,
        damping: 17,
      }}
      {...props}
    >
      {children}
    </motion.button>
  );
}

// Smooth scale hover dla kart
export function HoverCard({ children, className = '', ...props }) {
  return (
    <motion.div
      className={className}
      whileHover={{
        scale: 1.02,
        y: -4,
        boxShadow: '0 20px 40px rgba(139, 115, 85, 0.15)',
      }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 20,
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Progress bar dla scroll
export function ScrollProgress() {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [ref, inView] = useInView({
    threshold: 0,
    triggerOnce: false,
  });

  useEffect(() => {
    const updateScrollProgress = () => {
      if (typeof window !== 'undefined') {
        const scrolled = window.scrollY;
        const maxScroll =
          document.documentElement.scrollHeight - window.innerHeight;
        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;
        setScrollProgress(progress);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', updateScrollProgress);
      updateScrollProgress(); // Initial call

      return () => window.removeEventListener('scroll', updateScrollProgress);
    }
  }, []);

  return (
    <motion.div
      ref={ref}
      className='fixed top-0 left-0 right-0 h-1 bg-enterprise-brown/20 z-50'
      initial={{ scaleX: 0 }}
      animate={{ scaleX: inView ? 1 : 0 }}
      style={{ transformOrigin: '0%' }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className='h-full bg-gradient-to-r from-charcoal to-golden'
        style={{ scaleX: scrollProgress }}
        transition={{ duration: 0.1 }}
      />
    </motion.div>
  );
}
