'use client';

import React from 'react';
import {
  ScrollReveal,
  StaggerContainer,
  StaggerItem,
} from '@/components/ScrollReveal';
import {
  SectionTitle,
  BodyText,
  LeadText,
} from '@/components/ui/UnifiedTypography';
import { TestimonialCard } from '@/components/ui/UnifiedCard';
import { getFeaturedTestimonials } from '@/data/testimonialsData';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { Icon } from '@/components/ui/IconSystem';

/**
 * LuxuryTestimonials - Sekcja testimoniali z luksusowymi elementami
 * Subtelne złote akcenty, animacje scroll reveal, autentyczne zdjęcia
 */

export default function LuxuryTestimonials({
  title = 'Głosy naszych uczestniczek',
  subtitle = '<PERSON><PERSON><PERSON> podróż z BAKASANA to wyjątkowe doświadczenie transformacji i duchowego wzrostu',
  showImages = true,
  maxTestimonials = 3,
  variant = 'default', // 'default', 'compact', 'featured'
}) {
  const testimonials = getFeaturedTestimonials(maxTestimonials);

  return (
    <section className='py-section bg-whisper relative overflow-hidden'>
      {/* Subtelny wzór w tle */}
      <div className='absolute inset-0 opacity-[0.02]'>
        <div className='absolute inset-0 bg-gradient-to-br from-enterprise-brown/5 to-transparent' />
        <div
          className='absolute inset-0 opacity-30'
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C19B68' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className='container mx-auto px-container-sm relative'>
        <ScrollReveal variant='fadeInUp' className='text-center mb-2xl'>
          <SectionTitle className='mb-md'>{title}</SectionTitle>
          <LeadText className='text-sage max-w-2xl mx-auto'>
            {subtitle}
          </LeadText>
        </ScrollReveal>

        <StaggerContainer className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg max-w-6xl mx-auto'>
          {testimonials.map((testimonial, index) => (
            <StaggerItem key={testimonial.id}>
              <TestimonialCard
                className='h-full group relative overflow-hidden'
                itemScope
                itemType='https://schema.org/Review'
              >
                {/* Subtelny złoty akcent na hover */}
                <div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-enterprise-brown via-terra to-sand transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left' />

                {/* Zdjęcie uczestniczki */}
                {showImages && (
                  <div className='flex items-center mb-md'>
                    <div className='relative w-16 h-16 rounded-full overflow-hidden mr-4 ring-2 ring-enterprise-brown/10 group-hover:ring-enterprise-brown/30 transition-all duration-300'>
                      <OptimizedImage
                        src={testimonial.image}
                        alt={`${testimonial.name} - uczestniczka ${testimonial.retreat}`}
                        fill
                        className='object-cover'
                        sizes='64px'
                        placeholder='blur'
                        blurDataURL='data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=='
                      />
                    </div>
                    <div>
                      <h4
                        className='font-cormorant text-lg text-charcoal font-medium'
                        itemProp='author'
                        itemScope
                        itemType='https://schema.org/Person'
                      >
                        <span itemProp='name'>{testimonial.name}</span>
                      </h4>
                      <p className='text-sm text-sage'>
                        {testimonial.location} • {testimonial.retreat}
                      </p>
                    </div>
                  </div>
                )}

                {/* Rating z subtelną animacją */}
                <div
                  className='flex mb-md'
                  itemProp='reviewRating'
                  itemScope
                  itemType='https://schema.org/Rating'
                >
                  {[...Array(5)].map((_, i) => (
                    <Icon
                      key={i}
                      name='star'
                      size='sm'
                      className={`transition-all duration-300 ${
                        i < testimonial.rating
                          ? 'text-enterprise-brown group-hover:text-terra'
                          : 'text-stone-light'
                      }`}
                      style={{
                        transitionDelay: `${i * 100}ms`,
                      }}
                    />
                  ))}
                  <meta itemProp='ratingValue' content={testimonial.rating} />
                  <meta itemProp='bestRating' content='5' />
                </div>

                {/* Cytat */}
                <blockquote className='mb-md'>
                  <BodyText
                    className='text-charcoal-light italic leading-relaxed relative'
                    itemProp='reviewBody'
                  >
                    {/* Subtelne cudzysłowy */}
                    <span className='absolute -top-2 -left-2 text-4xl text-enterprise-brown/20 font-cormorant leading-none'>
                      "
                    </span>
                    {testimonial.quote}
                    <span className='absolute -bottom-4 -right-2 text-4xl text-enterprise-brown/20 font-cormorant leading-none'>
                      "
                    </span>
                  </BodyText>
                </blockquote>

                {/* Highlights */}
                {testimonial.highlights && (
                  <div className='flex flex-wrap gap-2 mt-md'>
                    {testimonial.highlights.slice(0, 2).map((highlight, i) => (
                      <span
                        key={i}
                        className='px-3 py-1 text-xs text-enterprise-brown bg-enterprise-brown/5 rounded-full border border-enterprise-brown/10 group-hover:bg-enterprise-brown/10 group-hover:border-enterprise-brown/20 transition-all duration-300'
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                )}

                {/* Metadata dla SEO */}
                <meta itemProp='datePublished' content={testimonial.date} />
                <div
                  itemProp='itemReviewed'
                  itemScope
                  itemType='https://schema.org/Service'
                >
                  <meta itemProp='name' content='BAKASANA Retreaty Jogi' />
                </div>
              </TestimonialCard>
            </StaggerItem>
          ))}
        </StaggerContainer>

        {/* CTA do pełnych testimoniali */}
        <ScrollReveal variant='fadeInUp' className='text-center mt-xl'>
          <div className='inline-flex items-center text-enterprise-brown hover:text-terra transition-colors duration-300 group cursor-pointer'>
            <span className='font-inter font-light tracking-wide'>
              Zobacz więcej opinii
            </span>
            <Icon
              name='arrow-right'
              size='sm'
              className='ml-2 transform group-hover:translate-x-1 transition-transform duration-300'
            />
          </div>
        </ScrollReveal>
      </div>

      {/* Subtelny gradient na dole */}
      <div className='absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-linen/50 to-transparent pointer-events-none' />
    </section>
  );
}

// Wariant kompaktowy dla innych stron
export function CompactTestimonials({ maxTestimonials = 2 }) {
  return (
    <LuxuryTestimonials
      title='Co mówią nasze uczestniczki'
      subtitle='Autentyczne opinie kobiet, które doświadczyły transformacji'
      maxTestimonials={maxTestimonials}
      variant='compact'
    />
  );
}

// Wariant z wyróżnionym testimonialem
export function FeaturedTestimonial({ testimonialId }) {
  const testimonials = getFeaturedTestimonials(1);

  return (
    <section className='py-xl bg-sanctuary'>
      <div className='container mx-auto px-container-sm'>
        <ScrollReveal
          variant='fadeInUp'
          className='max-w-4xl mx-auto text-center'
        >
          <TestimonialCard className='p-xl relative overflow-hidden'>
            {/* Dekoracyjny element */}
            <div className='absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-enterprise-brown to-transparent' />

            <div className='mb-lg'>
              <Icon
                name='quote'
                size='lg'
                className='text-enterprise-brown/30 mx-auto mb-md'
              />
              <BodyText className='text-xl text-charcoal-light italic leading-relaxed mb-lg'>
                {testimonials[0]?.quote}
              </BodyText>
              <div className='flex items-center justify-center'>
                <div className='w-12 h-12 rounded-full overflow-hidden mr-4'>
                  <OptimizedImage
                    src={testimonials[0]?.image}
                    alt={testimonials[0]?.name}
                    width={48}
                    height={48}
                    className='object-cover'
                  />
                </div>
                <div className='text-left'>
                  <p className='font-cormorant text-lg text-charcoal font-medium'>
                    {testimonials[0]?.name}
                  </p>
                  <p className='text-sm text-sage'>
                    {testimonials[0]?.retreat}
                  </p>
                </div>
              </div>
            </div>
          </TestimonialCard>
        </ScrollReveal>
      </div>
    </section>
  );
}
