'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { UnifiedButton } from '@/components/ui/UnifiedButton';
import PerformantWhatsApp from './PerformantWhatsApp';
import { NavLink } from '@/components/ui/UnifiedTypography';
import { mainNavItems } from '@/data/navigationLinks';

// 🔧 KOMPLETNY ROUTING - Navigation Links
const navLinks = [
  { href: '/', label: 'Home' },
  { href: '/o-mnie', label: 'O mnie' },
  { href: '/retreaty', label: 'Retreaty' },
  { href: '/zajecia-online', label: 'Zajęcia online' },
  { href: '/blog', label: 'Blog' },
  { href: '/galeria', label: 'Galeria' },
  { href: '/kontakt', label: 'Kontakt' },
];

export default function PerfectNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const pathname = usePathname();

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;

    // Always keep navbar visible (sticky behavior)
    setIsVisible(true);

    // Close dropdown when scrolling for better UX
    if (Math.abs(currentScrollY - lastScrollY) > 5) {
      setActiveDropdown(null);
    }

    setScrolled(currentScrollY > 20);
    setLastScrollY(currentScrollY);
  }, [lastScrollY]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Zamknij menu mobilne przy zmianie ścieżki
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  const isActiveLink = useCallback(
    href => {
      if (href === '/') return pathname === '/';
      return pathname.startsWith(href);
    },
    [pathname]
  );

  const handleDropdownToggle = useCallback(
    index => {
      setActiveDropdown(activeDropdown === index ? null : index);
    },
    [activeDropdown]
  );

  const handleMouseEnter = useCallback(index => {
    if (window.innerWidth >= 1024) {
      // md breakpoint
      setActiveDropdown(index);
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (window.innerWidth >= 1024) {
      setActiveDropdown(null);
    }
  }, []);

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-out transform translate-y-0 ${
          scrolled ? 'navbar-scrolled' : 'navbar-transparent'
        }`}
      >
        <div className='mx-auto px-6 md:px-12 lg:px-[60px]'>
          <div className='flex items-center justify-between h-16 md:h-20 lg:h-[85px]'>
            {/* Logo - Enhanced contrast */}
            <Link
              href='/'
              className='navbar-logo group relative font-cormorant font-light transition-all duration-700'
              style={{
                fontSize: '24px',
                letterSpacing: '2px',
                fontWeight: '300',
              }}
            >
              BAKASANA
              <span className='absolute -bottom-1 left-0 w-0 h-[1px] bg-current transition-all duration-500 group-hover:w-full opacity-60'></span>
            </Link>

            {/* Desktop Menu - Enhanced visibility and contrast */}
            <div className='hidden md:flex items-center space-x-12'>
              {navLinks.slice(1).map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className='relative group py-2'
                >
                  {/* Desktop-specific NavLink with enhanced contrast */}
                  <span
                    className={`
                      desktop-nav-link font-inter text-xs uppercase tracking-[1.2px]
                      relative transition-all duration-300 hover:scale-105
                      ${isActiveLink(item.href) ? 'active' : ''}
                      ${
                        item.href === '/zajecia-online'
                          ? 'highlight bg-terra/10 px-3 py-1 rounded-md hover:bg-terra/20'
                          : ''
                      }
                    `}
                  >
                    {item.label}
                    {/* Underline animation */}
                    <span className='nav-underline'></span>
                  </span>
                </Link>
              ))}
            </div>

            {/* Contact Icon - Desktop */}
            <div className='hidden md:block'>
              <PerformantWhatsApp
                variant='navbar-contact'
                iconType='mail'
                className='hover:scale-105 transition-transform duration-300'
              />
            </div>

            {/* Mobile Menu Button - Elegancki hamburger */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className='md:hidden p-3 text-enterprise-brown hover:text-enterprise-brown/80 transition-all duration-300'
              aria-label='Toggle menu'
            >
              <div className='w-6 h-6 flex flex-col justify-center items-center space-y-1'>
                <span
                  className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                    isMenuOpen ? 'rotate-45 translate-y-[4px]' : ''
                  }`}
                ></span>
                <span
                  className={`w-6 h-[1px] bg-current transition-all duration-300 ${
                    isMenuOpen ? 'opacity-0' : ''
                  }`}
                ></span>
                <span
                  className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                    isMenuOpen ? '-rotate-45 -translate-y-[4px]' : ''
                  }`}
                ></span>
              </div>
            </button>
          </div>
        </div>

        {/* Enhanced Mobile Menu */}
        <div
          className={`md:hidden absolute top-full left-0 right-0
          navbar-scrolled
          transition-all duration-500 ${
            isMenuOpen
              ? 'opacity-100 translate-y-0 max-h-screen'
              : 'opacity-0 -translate-y-4 max-h-0 pointer-events-none overflow-hidden'
          }`}
        >
          <div className='px-6 md:px-12 lg:px-[60px] py-8 space-y-lg'>
            {navLinks.slice(1).map((item, index) => (
              <div
                key={item.href}
                className='relative'
                style={{
                  animation: isMenuOpen
                    ? `fadeInUp 0.6s ease-out ${index * 100}ms both`
                    : 'none',
                }}
              >
                <Link
                  href={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className='block relative group'
                >
                  <span
                    className={`
                      mobile-nav-link text-lg transition-all duration-300
                      ${isActiveLink(item.href) ? 'active' : ''}
                      ${
                        item.href === '/zajecia-online'
                          ? 'highlight bg-terra/10 px-4 py-2 inline-block rounded-md'
                          : 'block py-2'
                      }
                    `}
                  >
                    {item.label}
                    {isActiveLink(item.href) &&
                      item.href !== '/zajecia-online' && (
                        <span className='inline-block ml-3 w-2 h-2 bg-current opacity-80 rounded-full animate-pulse'></span>
                      )}
                  </span>

                  {/* Mobile active indicator */}
                  {isActiveLink(item.href) &&
                    item.href !== '/zajecia-online' && (
                      <div className='absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-1 h-6 bg-current'></div>
                    )}
                </Link>
              </div>
            ))}

            {/* WhatsApp Button - Mobile */}
            <div
              className='pt-8 border-t border-enterprise-brown/10'
              style={{
                animation: isMenuOpen
                  ? `fadeInUp 0.6s ease-out ${navLinks.length * 100}ms both`
                  : 'none',
              }}
            >
              <PerformantWhatsApp
                size='md'
                variant='button'
                className='w-full justify-center text-base font-light tracking-[1px] py-4 px-6 md:px-12 lg:px-[60px] hover:scale-105 transition-all duration-300'
              />
            </div>
          </div>
        </div>
      </nav>

      {/* Enhanced Mobile Menu Overlay */}
      {isMenuOpen && (
        <div
          className='fixed inset-0 bg-black/10 backdrop-blur-sm z-40 md:hidden transition-opacity duration-300'
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* Navbar spacing */}
      <div className='h-16 md:h-20 lg:h-[85px]'></div>

      {/* Enhanced Styles */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes pulseGentle {
          0%,
          100% {
            opacity: 0.8;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.02);
          }
        }

        .animate-pulse-gentle {
          animation: pulseGentle 2s ease-in-out infinite;
        }

        /* Additional component-specific styles */
        .navbar-logo {
          color: #3a3633;
        }

        .navbar-logo:hover {
          color: #d4af37;
        }

        /* Smooth scrollbar for dropdown */
        .dropdown-content::-webkit-scrollbar {
          width: 4px;
        }

        .dropdown-content::-webkit-scrollbar-track {
          background: rgba(139, 115, 85, 0.1);
        }

        .dropdown-content::-webkit-scrollbar-thumb {
          background: rgba(139, 115, 85, 0.3);
        }

        .dropdown-content::-webkit-scrollbar-thumb:hover {
          background: rgba(139, 115, 85, 0.5);
        }

        /* Enhanced hover effects */
        .nav-item-hover {
          position: relative;
          overflow: hidden;
        }

        .nav-item-hover::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(139, 115, 85, 0.1),
            transparent
          );
          transition: left 0.5s;
        }

        .nav-item-hover:hover::before {
          left: 100%;
        }
      `}</style>
    </>
  );
}
