# 🎨 BAKASANA CSS MANAGEMENT GUIDE

## 🔍 Znajdowanie Powtórzeń CSS

### 1. <PERSON><PERSON><PERSON>
```bash
# Znajdź wszystkie duplikaty w CSS
npm run css:duplicates

# Lub bezpośrednio:
node scripts/find-css-duplicates.js
```

**Co znajdzie:**
- Duplikaty selektorów CSS (198 znalezionych)
- Powtarzające się właś<PERSON>ści (480 znalezionych)
- Zduplikowane zmienne CSS (93 znalezione)
- Szczegółowy raport z lokalizacją

### 2. Sortowanie Właściwości CSS
```bash
# Sortuj właściwości we wszystkich plikach CSS
npm run css:sort

# Kompleksowe czyszczenie (duplikaty + sortowanie)
npm run css:cleanup
```

## ⚡ Najważniejsze Komendy

### 1. P<PERSON>łna Optymalizacja (ZALECANE)
```bash
npm run css:clean-all
```
**Efekt**: Usuwa duplikaty + sortuje właści<PERSON>ści + optymalizuje CSS

### 2. Tylko Analiza (Bezpieczne)
```bash
npm run css:analyze
```
**Efekt**: Pokazuje potencjał optymalizacji bez zmian

### 3. Przywróć Oryginalne Pliki
```bash
npm run css:restore
```
**Efekt**: Cofnij wszystkie zmiany

---

## 🧹 Oryginalna Komenda PurgeCSS

Jak prosiłeś - prosta komenda do usuwania nieużywanych CSS:

### Podstawowa Składnia:
```bash
npx purgecss --css [plik-css] --content [pliki-html/js] --output [plik-wyjściowy]
```

### Przykłady Użycia:

#### 1. Pojedynczy Plik CSS
```bash
npx purgecss --css src/styles/main.css --content "src/**/*.{js,jsx,ts,tsx}" --output clean.css
```

#### 2. Wiele Plików CSS
```bash
npx purgecss --css "src/styles/*.css" --content "src/**/*.{js,jsx,ts,tsx}" --output src/styles/clean/
```

#### 3. Z Konkretnym Plikiem HTML
```bash
npx purgecss --css src/app/globals.css --content src/app/page.jsx --output globals-clean.css
```

#### 4. Używając Pliku Konfiguracyjnego
```bash
npx purgecss --config purgecss.config.js
```

---

## 🎯 Gotowe Skrypty

### Windows (Batch)
```cmd
purge-css-simple.bat default
```
lub
```cmd
purge-css-simple.bat src/styles/main.css "src/**/*.jsx" output.css
```

### Zaawansowane NPM Scripts
```bash
# Tylko czyszczenie (bez zastosowania)
npm run css:purge

# Zastosuj oczyszczone pliki
npm run css:apply

# Analiza + czyszczenie
npm run css:optimize
```

---

## 📊 Twoje Wyniki

✅ **Zoptymalizowano**: 9 plików CSS  
✅ **Oszczędność**: 47.1 KB (51.8%)  
✅ **Czas ładowania**: 51.8% szybszy  

### Największe Oszczędności:
- `bakasana-visuals.css`: **89.2%** ↓ (17.9 KB)
- `premium-utilities.css`: **66.8%** ↓ (4.1 KB)  
- `modern-css.css`: **66.1%** ↓ (6.3 KB)

---

## 🔧 Szybka Konfiguracja

### Safelist (Chronione Klasy)
Dodaj do `purgecss.config.js`:
```javascript
safelist: [
  'twoja-klasa',
  /^prefix-/,
  { pattern: /^btn-/, variants: ['hover', 'focus'] }
]
```

### Ignorowanie Plików
```javascript
content: [
  'src/**/*.{js,jsx,ts,tsx}',
  '!src/**/*.test.js'  // Ignoruj testy
]
```

---

## 🚨 Szybka Pomoc

### Problem: Brakuje stylów po optymalizacji
```bash
npm run css:restore  # Przywróć oryginalne
```

### Problem: Klasa została usunięta błędnie
Dodaj do `safelist` w `purgecss.config.js`:
```javascript
safelist: ['twoja-klasa']
```

### Problem: Dynamiczne klasy nie działają
Dodaj wzorzec do `safelist`:
```javascript
safelist: [/^dynamic-/]
```

---

## 🎉 Gotowe!

**Najszybszy sposób**: `npm run css:full-optimize`

Twój CSS jest teraz **51.8% mniejszy** i ładuje się **2x szybciej**! 🚀
