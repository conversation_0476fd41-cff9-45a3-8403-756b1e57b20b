'use client';
// src/components/ClientLayout.jsx
import { Suspense } from 'react';
import dynamic from 'next/dynamic';

import ErrorBoundary from './ErrorBoundary';

export function ClientLayout({ children }) {
  const Navbar = dynamic(() => import('@/components/Navbar'), {
    ssr: false,
    loading: () => <div className='h-16 md:h-20 lg:h-[85px]' />,
  });

  const Footer = dynamic(() => import('@/components/Footer'), {
    ssr: true,
    loading: () => <div className='h-20' />,
  });

  return (
    <ErrorBoundary>
      <Suspense fallback={<div className='h-16 md:h-20 lg:h-[85px]' />}>
        <Navbar />
      </Suspense>
      <main role='main' className='flex-grow mb-lg md:mb-xl'>
        {children}
      </main>
      <Suspense fallback={<div className='h-20' />}>
        <Footer />
      </Suspense>
    </ErrorBoundary>
  );
}
