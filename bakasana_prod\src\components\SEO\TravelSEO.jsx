import Head from 'next/head';
import Script from 'next/script';

const TravelSEO = ({
  title,
  description,
  canonical,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image',
  retreat,
  destination,
  author,
  publishDate,
  modifiedDate,
  schema,
  breadcrumbs,
  faq,
  className = '',
}) => {
  const siteUrl = 'https://bakasanaretreat.com';
  const siteName = 'Bakasana Retreat';
  const defaultOgImage = `${siteUrl}/images/og-default.jpg`;

  // Generate structured data for travel/retreat
  const generateTravelSchema = () => {
    const schemas = [];

    // Main Organization Schema
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'TravelAgency',
      name: siteName,
      url: siteUrl,
      logo: `${siteUrl}/images/logo.png`,
      description:
        'Premium yoga retreats in Bali and Sri Lanka with <PERSON>',
      founder: {
        '@type': 'Person',
        name: '<PERSON>',
        description: 'Certified yoga instructor and physiotherapist',
        image: `${siteUrl}/images/julia-profile.jpg`,
      },
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'PL',
        addressLocality: 'Poland',
      },
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+48-xxx-xxx-xxx',
        contactType: 'customer service',
        availableLanguage: ['Polish', 'English'],
      },
      sameAs: [
        'https://instagram.com/bakasanaretreat',
        'https://facebook.com/bakasanaretreat',
      ],
    });

    // Retreat Event Schema
    if (retreat) {
      schemas.push({
        '@context': 'https://schema.org',
        '@type': 'Event',
        name: retreat.title,
        description: retreat.description,
        startDate: retreat.startDate,
        endDate: retreat.endDate,
        location: {
          '@type': 'Place',
          name: retreat.location?.name,
          address: {
            '@type': 'PostalAddress',
            addressCountry: retreat.location?.country,
            addressLocality: retreat.location?.city,
          },
          geo: retreat.location?.coordinates && {
            '@type': 'GeoCoordinates',
            latitude: retreat.location.coordinates.lat,
            longitude: retreat.location.coordinates.lng,
          },
        },
        organizer: {
          '@type': 'Person',
          name: 'Julia Jakubowicz',
        },
        offers: {
          '@type': 'Offer',
          price: retreat.price,
          priceCurrency: 'EUR',
          availability: retreat.available
            ? 'https://schema.org/InStock'
            : 'https://schema.org/OutOfStock',
          validFrom: new Date().toISOString(),
          url: `${siteUrl}/retreats/${retreat.slug}`,
        },
        image: retreat.image && `${siteUrl}${retreat.image}`,
        eventAttendanceMode: 'https://schema.org/OfflineEventAttendanceMode',
        eventStatus: 'https://schema.org/EventScheduled',
        performer: {
          '@type': 'Person',
          name: 'Julia Jakubowicz',
        },
      });
    }

    // Destination Schema
    if (destination) {
      schemas.push({
        '@context': 'https://schema.org',
        '@type': 'TouristDestination',
        name: destination.name,
        description: destination.description,
        image: destination.image && `${siteUrl}${destination.image}`,
        address: {
          '@type': 'PostalAddress',
          addressCountry: destination.country,
          addressLocality: destination.city,
        },
        geo: destination.coordinates && {
          '@type': 'GeoCoordinates',
          latitude: destination.coordinates.lat,
          longitude: destination.coordinates.lng,
        },
        touristType: 'Yoga and wellness enthusiasts',
        availableLanguage: ['English', 'Indonesian', 'Sinhala'],
        currenciesAccepted: destination.currency || ['USD', 'EUR'],
        isAccessibleForFree: false,
      });
    }

    // Article Schema for blog posts
    if (author && publishDate) {
      schemas.push({
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: title,
        description: description,
        author: {
          '@type': 'Person',
          name: author.name,
          image: author.image && `${siteUrl}${author.image}`,
          description: author.bio,
        },
        publisher: {
          '@type': 'Organization',
          name: siteName,
          logo: {
            '@type': 'ImageObject',
            url: `${siteUrl}/images/logo.png`,
          },
        },
        datePublished: publishDate,
        dateModified: modifiedDate || publishDate,
        image: ogImage || defaultOgImage,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': canonical || siteUrl,
        },
      });
    }

    // FAQ Schema
    if (faq && faq.length > 0) {
      schemas.push({
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: faq.map(item => ({
          '@type': 'Question',
          name: item.question,
          acceptedAnswer: {
            '@type': 'Answer',
            text: item.answer,
          },
        })),
      });
    }

    // Breadcrumb Schema
    if (breadcrumbs && breadcrumbs.length > 0) {
      schemas.push({
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: breadcrumbs.map((crumb, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: crumb.name,
          item: `${siteUrl}${crumb.url}`,
        })),
      });
    }

    // Custom schema
    if (schema) {
      schemas.push(schema);
    }

    return schemas;
  };

  const schemas = generateTravelSchema();

  return (
    <>
      <Head>
        {/* Primary Meta Tags */}
        <title>{title}</title>
        <meta name='description' content={description} />
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <link rel='canonical' href={canonical || siteUrl} />

        {/* Open Graph */}
        <meta property='og:type' content={ogType} />
        <meta property='og:title' content={title} />
        <meta property='og:description' content={description} />
        <meta property='og:image' content={ogImage || defaultOgImage} />
        <meta property='og:url' content={canonical || siteUrl} />
        <meta property='og:site_name' content={siteName} />
        <meta property='og:locale' content='pl_PL' />
        <meta property='og:locale:alternate' content='en_US' />

        {/* Twitter Card */}
        <meta name='twitter:card' content={twitterCard} />
        <meta name='twitter:title' content={title} />
        <meta name='twitter:description' content={description} />
        <meta name='twitter:image' content={ogImage || defaultOgImage} />
        <meta name='twitter:site' content='@bakasanaretreat' />
        <meta name='twitter:creator' content='@bakasanaretreat' />

        {/* Additional SEO */}
        <meta
          name='robots'
          content='index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'
        />
        <meta name='theme-color' content='#C4A575' />
        <meta name='msapplication-TileColor' content='#C4A575' />
        <meta name='apple-mobile-web-app-capable' content='yes' />
        <meta
          name='apple-mobile-web-app-status-bar-style'
          content='black-translucent'
        />

        {/* Travel-specific meta tags */}
        {destination && (
          <>
            <meta name='geo.region' content={destination.country} />
            <meta name='geo.placename' content={destination.name} />
            {destination.coordinates && (
              <>
                <meta
                  name='geo.position'
                  content={`${destination.coordinates.lat};${destination.coordinates.lng}`}
                />
                <meta
                  name='ICBM'
                  content={`${destination.coordinates.lat}, ${destination.coordinates.lng}`}
                />
              </>
            )}
          </>
        )}

        {/* Preload critical resources */}
        <link
          rel='preload'
          href='/fonts/CormorantGaramond-Regular.woff2'
          as='font'
          type='font/woff2'
          crossOrigin='anonymous'
        />
        <link
          rel='preload'
          href='/fonts/Inter-Regular.woff2'
          as='font'
          type='font/woff2'
          crossOrigin='anonymous'
        />

        {/* DNS prefetch for external resources */}
        <link rel='dns-prefetch' href='//fonts.googleapis.com' />
        <link rel='dns-prefetch' href='//www.google-analytics.com' />
        <link rel='dns-prefetch' href='//vercel.live' />

        {/* Favicons */}
        <link
          rel='apple-touch-icon'
          sizes='180x180'
          href='/favicons/apple-touch-icon.png'
        />
        <link
          rel='icon'
          type='image/png'
          sizes='32x32'
          href='/favicons/favicon-32x32.png'
        />
        <link
          rel='icon'
          type='image/png'
          sizes='16x16'
          href='/favicons/favicon-16x16.png'
        />
        <link rel='manifest' href='/manifest.json' />
        <link
          rel='mask-icon'
          href='/favicons/safari-pinned-tab.svg'
          color='#C4A575'
        />
        <link rel='shortcut icon' href='/favicons/favicon.ico' />

        {/* Additional travel-specific metadata */}
        {retreat && (
          <>
            <meta name='event:start_date' content={retreat.startDate} />
            <meta name='event:end_date' content={retreat.endDate} />
            <meta name='event:location' content={retreat.location?.name} />
            <meta name='price:amount' content={retreat.price} />
            <meta name='price:currency' content='EUR' />
          </>
        )}

        {/* Article metadata */}
        {author && publishDate && (
          <>
            <meta name='author' content={author.name} />
            <meta name='article:published_time' content={publishDate} />
            <meta
              name='article:modified_time'
              content={modifiedDate || publishDate}
            />
            <meta name='article:author' content={author.name} />
            <meta name='article:section' content='Travel' />
            <meta
              name='article:tag'
              content='yoga, retreat, bali, sri lanka, wellness, travel'
            />
          </>
        )}
      </Head>

      {/* Structured Data */}
      {schemas.map((schema, index) => (
        <Script
          key={index}
          id={`schema-${index}`}
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema),
          }}
        />
      ))}

      {/* Google Analytics 4 */}
      <Script
        src='https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'
        strategy='afterInteractive'
      />
      <Script id='google-analytics' strategy='afterInteractive'>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'GA_MEASUREMENT_ID', {
            page_title: '${title}',
            page_location: '${canonical || siteUrl}',
            content_group1: '${ogType}',
            content_group2: '${destination?.country || 'General'}',
            custom_map: {
              'custom_dimension_1': 'retreat_type',
              'custom_dimension_2': 'destination_country'
            }
          });
        `}
      </Script>

      {/* Enhanced Ecommerce for Retreats */}
      {retreat && (
        <Script id='ecommerce-tracking' strategy='afterInteractive'>
          {`
            gtag('event', 'view_item', {
              currency: 'EUR',
              value: ${retreat.price},
              items: [{
                item_id: '${retreat.id}',
                item_name: '${retreat.title}',
                item_category: 'Retreat',
                item_category2: '${destination?.country || 'Unknown'}',
                item_variant: '${retreat.duration || 'Standard'}',
                price: ${retreat.price},
                quantity: 1
              }]
            });
          `}
        </Script>
      )}

      {/* Hotjar Tracking */}
      <Script id='hotjar' strategy='afterInteractive'>
        {`
          (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:HOTJAR_ID,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
          })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        `}
      </Script>

      {/* Schema.org Rich Snippets for Travel */}
      <Script id='travel-rich-snippets' strategy='afterInteractive'>
        {`
          // Travel-specific tracking
          if (window.gtag) {
            gtag('event', 'page_view', {
              page_title: '${title}',
              page_location: window.location.href,
              page_path: window.location.pathname,
              content_group1: '${ogType}',
              content_group2: '${destination?.country || 'General'}',
              user_properties: {
                travel_interest: 'yoga_retreat',
                destination_preference: '${destination?.country || 'unknown'}'
              }
            });
          }
        `}
      </Script>
    </>
  );
};

export default TravelSEO;
