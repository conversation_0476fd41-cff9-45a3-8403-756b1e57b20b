'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 🏗️ ResponsiveGrid - Advanced Grid System for Perfect 10/10 Responsiveness
 *
 * Features:
 * - Auto-fit/auto-fill grid layouts
 * - Container queries support
 * - Responsive gap system
 * - Accessibility compliant
 * - Performance optimized
 */

const gridVariants = {
  // Auto-fit: Columns expand to fill space
  'auto-fit': 'grid-auto-fit',

  // Auto-fill: Maintains empty columns
  'auto-fill': 'grid-auto-fill',

  // Specialized layouts
  cards: 'grid-responsive-cards',
  services: 'grid-responsive-services',
  testimonials: 'grid-responsive-testimonials',
  gallery: 'grid-responsive-gallery',
  stats: 'grid-stats',
  hero: 'grid-hero',
  masonry: 'grid-masonry',

  // Container query responsive
  container: 'grid-container-responsive',

  // Semantic grid
  semantic: 'grid-semantic',
};

const gapVariants = {
  sm: 'gap-responsive-sm',
  md: 'gap-responsive-md',
  lg: 'gap-responsive-lg',
  xl: 'gap-responsive-xl',
};

const alignmentVariants = {
  items: {
    start: 'grid-items-start',
    center: 'grid-items-center',
    end: 'grid-items-end',
    stretch: 'grid-items-stretch',
  },
  content: {
    start: 'grid-content-start',
    center: 'grid-content-center',
    end: 'grid-content-end',
    between: 'grid-content-between',
  },
};

export default function ResponsiveGrid({
  children,
  variant = 'auto-fit',
  gap = 'md',
  minColumnWidth = '280px',
  alignItems = 'start',
  justifyContent = 'start',
  className = '',
  containerQuery = false,
  style = {},
  ...props
}) {
  const gridClass = gridVariants[variant] || gridVariants['auto-fit'];
  const gapClass = gapVariants[gap] || gapVariants['md'];
  const alignItemsClass = alignmentVariants.items[alignItems] || '';
  const justifyContentClass = alignmentVariants.content[justifyContent] || '';

  const gridStyle = {
    '--min-column-width': minColumnWidth,
    ...style,
  };

  return (
    <div
      className={cn(
        gridClass,
        gapClass,
        alignItemsClass,
        justifyContentClass,
        containerQuery && 'grid-container',
        className
      )}
      style={gridStyle}
      {...props}
    >
      {children}
    </div>
  );
}

// Specialized grid components for common use cases

export function ServicesGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='services'
      gap='lg'
      minColumnWidth='300px'
      alignItems='stretch'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

export function TestimonialsGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='testimonials'
      gap='md'
      minColumnWidth='320px'
      alignItems='start'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

export function GalleryGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='gallery'
      gap='sm'
      minColumnWidth='250px'
      alignItems='center'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

export function StatsGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='stats'
      gap='lg'
      alignItems='center'
      justifyContent='center'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

export function CardsGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='cards'
      gap='lg'
      minColumnWidth='300px'
      alignItems='start'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

// Container Query Grid - for component-level responsiveness
export function ContainerGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='container'
      gap='md'
      containerQuery={true}
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

// Masonry Grid - for Pinterest-style layouts
export function MasonryGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='masonry'
      gap='md'
      minColumnWidth='300px'
      alignItems='start'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

// Hero Grid - for hero sections with content and image
export function HeroGrid({ children, className = '', ...props }) {
  return (
    <ResponsiveGrid
      variant='hero'
      gap='xl'
      alignItems='center'
      className={className}
      {...props}
    >
      {children}
    </ResponsiveGrid>
  );
}

// Utility function to create custom grids
export function createCustomGrid(config) {
  return function CustomGrid({ children, className = '', ...props }) {
    return (
      <ResponsiveGrid {...config} className={className} {...props}>
        {children}
      </ResponsiveGrid>
    );
  };
}

// Hook for responsive grid utilities
export function useResponsiveGrid() {
  const [containerWidth, setContainerWidth] = React.useState(0);
  const containerRef = React.useRef(null);

  React.useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  const getOptimalColumns = (minWidth = 280) => {
    return Math.floor(containerWidth / minWidth) || 1;
  };

  const getOptimalGap = () => {
    if (containerWidth < 480) return 'sm';
    if (containerWidth < 768) return 'md';
    if (containerWidth < 1024) return 'lg';
    return 'xl';
  };

  return {
    containerRef,
    containerWidth,
    getOptimalColumns,
    getOptimalGap,
  };
}

// Grid context for nested components
const GridContext = React.createContext({
  columns: 1,
  gap: 'md',
  containerWidth: 0,
});

export function GridProvider({ children, ...gridProps }) {
  const { containerRef, containerWidth, getOptimalColumns, getOptimalGap } =
    useResponsiveGrid();

  const contextValue = {
    columns: getOptimalColumns(gridProps.minColumnWidth),
    gap: getOptimalGap(),
    containerWidth,
  };

  return (
    <GridContext.Provider value={contextValue}>
      <div ref={containerRef}>
        <ResponsiveGrid {...gridProps}>{children}</ResponsiveGrid>
      </div>
    </GridContext.Provider>
  );
}

export function useGridContext() {
  const context = React.useContext(GridContext);
  if (!context) {
    throw new Error('useGridContext must be used within a GridProvider');
  }
  return context;
}
