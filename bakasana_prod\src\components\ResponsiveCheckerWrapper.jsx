import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

export default function ResponsiveCheckerWrapper() {
  'use client';

  const ResponsiveChecker = dynamic(
    () => import('@/components/ResponsiveChecker'),
    {
      loading: () => null,
      ssr: false,
    }
  );

  const [isDevelopment, setIsDevelopment] = useState(false);

  useEffect(() => {
    // Sprawdź czy jesteśmy w development mode po stronie klienta
    setIsDevelopment(process.env.NODE_ENV === 'development');
  }, []);

  // Nie renderuj nic jeśli nie jesteśmy w development mode
  if (!isDevelopment) {
    return null;
  }

  return <ResponsiveChecker />;
}
