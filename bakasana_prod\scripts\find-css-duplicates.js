#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🔍 BAKASANA CSS DUPLICATE FINDER - Znajdowanie powtórzeń w CSS...\n');

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');

// Znajdź wszystkie pliki CSS
const cssFiles = [
  ...glob.sync(path.join(stylesDir, '*.css').replace(/\\/g, '/')),
  ...glob.sync(path.join(appDir, '*.css').replace(/\\/g, '/')),
].filter(file => !file.includes('node_modules') && !file.includes('.backup') && !file.includes('clean'));

console.log(`📁 Znaleziono ${cssFiles.length} plików CSS do analizy:`);
cssFiles.forEach(file => console.log(`   - ${path.relative(process.cwd(), file)}`));
console.log();

// Struktury do przechowywania danych
const duplicates = {
  selectors: new Map(),
  properties: new Map(),
  rules: new Map(),
  variables: new Map()
};

// Funkcja do parsowania CSS i znajdowania duplikatów
function findDuplicates() {
  cssFiles.forEach(filePath => {
    const fileName = path.basename(filePath);
    const content = fs.readFileSync(filePath, 'utf8');
    
    console.log(`🔍 Analizuję: ${fileName}`);
    
    // Znajdź selektory CSS
    const selectorRegex = /([^{}]+)\s*\{([^{}]*)\}/g;
    let match;
    
    while ((match = selectorRegex.exec(content)) !== null) {
      const selector = match[1].trim();
      const properties = match[2].trim();
      
      // Pomiń komentarze i @import
      if (selector.startsWith('/*') || selector.startsWith('@import') || selector.startsWith('@media')) {
        continue;
      }
      
      // Zapisz selektor
      if (!duplicates.selectors.has(selector)) {
        duplicates.selectors.set(selector, []);
      }
      duplicates.selectors.get(selector).push({
        file: fileName,
        properties: properties
      });
      
      // Znajdź właściwości CSS
      const propRegex = /([^:;]+):\s*([^;]+);?/g;
      let propMatch;
      
      while ((propMatch = propRegex.exec(properties)) !== null) {
        const property = propMatch[1].trim();
        const value = propMatch[2].trim();
        const fullProp = `${property}: ${value}`;
        
        if (!duplicates.properties.has(fullProp)) {
          duplicates.properties.set(fullProp, []);
        }
        duplicates.properties.get(fullProp).push({
          file: fileName,
          selector: selector
        });
      }
    }
    
    // Znajdź zmienne CSS
    const variableRegex = /(--[^:]+):\s*([^;]+);/g;
    while ((match = variableRegex.exec(content)) !== null) {
      const variable = match[1].trim();
      const value = match[2].trim();
      const fullVar = `${variable}: ${value}`;
      
      if (!duplicates.variables.has(fullVar)) {
        duplicates.variables.set(fullVar, []);
      }
      duplicates.variables.get(fullVar).push({
        file: fileName,
        value: value
      });
    }
  });
}

// Funkcja do raportowania duplikatów
function reportDuplicates() {
  console.log('\n📊 RAPORT DUPLIKATÓW CSS:\n');
  
  // Duplikaty selektorów
  console.log('🎯 DUPLIKATY SELEKTORÓW:');
  let selectorDuplicates = 0;
  duplicates.selectors.forEach((occurrences, selector) => {
    if (occurrences.length > 1) {
      selectorDuplicates++;
      console.log(`\n   Selektor: ${selector}`);
      console.log(`   Wystąpienia: ${occurrences.length}`);
      occurrences.forEach(occ => {
        console.log(`     - ${occ.file}`);
      });
    }
  });
  
  if (selectorDuplicates === 0) {
    console.log('   ✅ Brak duplikatów selektorów');
  }
  
  // Duplikaty właściwości
  console.log('\n🔧 DUPLIKATY WŁAŚCIWOŚCI:');
  let propertyDuplicates = 0;
  duplicates.properties.forEach((occurrences, property) => {
    if (occurrences.length > 1) {
      propertyDuplicates++;
      console.log(`\n   Właściwość: ${property}`);
      console.log(`   Wystąpienia: ${occurrences.length}`);
      occurrences.forEach(occ => {
        console.log(`     - ${occ.file} (${occ.selector})`);
      });
    }
  });
  
  if (propertyDuplicates === 0) {
    console.log('   ✅ Brak duplikatów właściwości');
  }
  
  // Duplikaty zmiennych
  console.log('\n🎨 DUPLIKATY ZMIENNYCH CSS:');
  let variableDuplicates = 0;
  duplicates.variables.forEach((occurrences, variable) => {
    if (occurrences.length > 1) {
      variableDuplicates++;
      console.log(`\n   Zmienna: ${variable}`);
      console.log(`   Wystąpienia: ${occurrences.length}`);
      occurrences.forEach(occ => {
        console.log(`     - ${occ.file}`);
      });
    }
  });
  
  if (variableDuplicates === 0) {
    console.log('   ✅ Brak duplikatów zmiennych');
  }
  
  // Podsumowanie
  console.log('\n📈 PODSUMOWANIE:');
  console.log(`   🎯 Duplikaty selektorów: ${selectorDuplicates}`);
  console.log(`   🔧 Duplikaty właściwości: ${propertyDuplicates}`);
  console.log(`   🎨 Duplikaty zmiennych: ${variableDuplicates}`);
  
  const totalDuplicates = selectorDuplicates + propertyDuplicates + variableDuplicates;
  if (totalDuplicates > 0) {
    console.log(`\n⚠️  Znaleziono ${totalDuplicates} typów duplikatów do optymalizacji`);
  } else {
    console.log('\n✨ Świetnie! Nie znaleziono duplikatów w CSS');
  }
}

// Uruchom analizę
try {
  findDuplicates();
  reportDuplicates();
  console.log('\n✅ Analiza duplikatów CSS zakończona!');
} catch (error) {
  console.error('❌ Błąd podczas analizy:', error.message);
  process.exit(1);
}
