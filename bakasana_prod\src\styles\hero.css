/* =============================================
   🏛️ BAKASANA HERO STYLES - OPTIMIZED & CLEAN
   ============================================= */

/* ===== CORE HERO SECTION ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding-top: 120px;
}.hero-bg {
  position: absolute;
  z-index: 0;
  background-color: #FCF6EE;
  background-image: url('/images/background/bali-hero.webp');
  background-size: cover;
  background-position: center;
  background-attachment: scroll;
  background-repeat: no-repeat;
  inset: 0;
}.hero-parallax-layer {
  position: absolute;
  z-index: 0;
  background-image: url('/images/background/bali-hero.webp');
  background-size: 120%;
  background-attachment: fixed;
  background-repeat: no-repeat;
  inset: 0;
  will-change: transform, background-position;
}

/* ===== HERO OVERLAYS ===== */
.hero-gradient-overlay {
  position: absolute;
  inset: 0;
  z-index: 10;
  background: 
    radial-gradient(circle at 77.3% 95.9%, 
      rgba(252,246,238,0.3) 0%, 
      rgba(255,255,255,0.6) 40%, 
      rgba(255,255,255,0.9) 100%
    ),
    linear-gradient(
      180deg,
      rgba(252,246,238,0.4) 0%,
      rgba(255,255,255,0.7) 60%,
      rgba(255,255,255,0.9) 100%
    );
  transition: background 0.3s ease-out;
}.hero-gradient-overlay--interactive {
  background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), 
      rgba(252,246,238,0.3) 0%, 
      rgba(255,255,255,0.6) 40%, 
      rgba(255,255,255,0.9) 100%
    ),
    linear-gradient(
      180deg,
      rgba(252,246,238,0.4) 0%,
      rgba(255,255,255,0.7) 60%,
      rgba(255,255,255,0.9) 100%
    );
}.hero-texture-overlay {
  position: absolute;
  z-index: 10;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.2;
  inset: 0;
  mix-blend-mode: multiply;
}

/* ===== HERO CONTENT ===== */
.hero-content {
  position: relative;
  z-index: 20;
  text-align: center;
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1.5rem;
}.hero-badge {
  display: inline-block;
  margin-bottom: 1.5rem;
  color: var(--enterprise-brown);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 3.5px;
}.hero-title {
  margin-top: -10px;
  margin-bottom: 1rem;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(100px, 15vw, 200px);
  font-weight: 300;
  line-height: 0.95;
  letter-spacing: 0.15em;
}.hero-subtitle {
  margin-bottom: 2rem;
  color: var(--enterprise-brown);
  font-family: 'Cormorant Garamond', serif;
  font-size: 21px;
  font-weight: 300;
  font-style: italic;
  opacity: 0.8;
}.hero-description {
  max-width: 620px;
  margin: 0 auto 4rem auto;
  color: var(--charcoal-light);
  font-size: 17px;
  font-weight: 400;
  line-height: 1.85;
}

/* ===== HERO STATS ===== */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem 5rem;
  max-width: 64rem;
  margin: 0 auto 4rem auto;
}

@media (min-width: 768px) {.hero-stats {
  grid-template-columns: repeat(4, 1fr);
}
}.hero-stat {
  text-align: center;
}.hero-stat-number {
  margin-bottom: 0.5rem;
  color: var(--enterprise-brown);
  font-size: 42px;
  font-weight: 100;
}.hero-stat-label {
  color: var(--sage);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* ===== HERO BUTTONS ===== */
.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

@media (min-width: 640px) {.hero-buttons {
  flex-direction: row;
  gap: 1.5rem;
}
}.hero-button {
  display: inline-flex;
  align-items: center;
  padding: 16px 48px;
  border: 1px solid var(--enterprise-brown);
  background: transparent;
  color: var(--enterprise-brown);
  font-size: 13px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  letter-spacing: 2px;
}.hero-button:hover {
  background: var(--enterprise-brown);
  color: white;
  transform: translateY(-2px);
}.hero-button:focus {
  opacity: 0.7;
  outline: none;
}.hero-button--whatsapp {
  border-color: #25D366;
  color: #25D366;
}.hero-button--whatsapp:hover {
  background: #25D366;
  color: white;
}

/* ===== HERO SIDE FORM ===== */
.hero-side-form {
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  background: var(--pearl);
  backdrop-filter: blur(12px);
  padding: 2rem;
  max-width: 20rem;
  width: 320px;
  border: 1px solid rgba(193, 155, 104, 0.1);
  box-shadow: 0 10px 40px rgba(0,0,0,0.06);
}

@media (max-width: 1279px) {.hero-side-form {
  display: none;
}
}.hero-side-form h3 {
  margin-bottom: 0.5rem;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.25rem;
}.hero-side-form p {
  margin-bottom: 1.5rem;
  color: var(--sage);
  font-size: 0.875rem;
}.hero-side-form input {
  width: 100%;
  padding: 0.75rem 0;
  border: 0;
  border-bottom: 1px solid var(--ash);
  background: transparent;
  font-size: 0.875rem;
  transition: border-color 0.3s ease;
}.hero-side-form input:focus {
  border-bottom-color: var(--enterprise-brown);
  outline: none;
}.hero-side-form .form-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  background: var(--enterprise-brown);
  color: white;
  font-size: 13px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.3s ease;
  letter-spacing: 2px;
}.hero-side-form .form-button:hover {
  background: var(--terra);
}.hero-side-form .form-button:focus {
  opacity: 0.8;
  outline: none;
}

/* ===== FLOATING ELEMENTS ===== */
.hero-floating-elements {
  position: absolute;
  inset: 0;
  z-index: 10;
  pointer-events: none;
}.hero-floating-dot {
  position: absolute;
  background: var(--temple-gold);
  animation: gentle-pulse 3s ease-in-out infinite;
}.hero-floating-dot:nth-child(1) {
  top: 25%;
  left: 25%;
  width: 8px;
  height: 8px;
  opacity: 0.3;
}.hero-floating-dot:nth-child(2) {
  top: 75%;
  right: 25%;
  width: 4px;
  height: 4px;
  opacity: 0.4;
  animation-delay: 1s;
}.hero-floating-dot:nth-child(3) {
  top: 50%;
  left: 75%;
  width: 12px;
  height: 12px;
  opacity: 0.2;
  animation-delay: 2s;
}

/* ===== ANIMATIONS ===== */
@keyframes gentle-pulse {0%, 100% {
  transform: scale(1);
  opacity: 0.3;
}50% {
  transform: scale(1.1);
  opacity: 0.8;
}
}.hero-fade-in {
  transform: translateY(20px);
  animation: hero-fade-in 0.8s ease-out forwards;
  opacity: 0;
}

@keyframes hero-fade-in {from {
  transform: translateY(20px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}

/* Staggered animations */
.hero-fade-in--delay-1 { animation-delay: 0.2s; }.hero-fade-in--delay-2 {
  animation-delay: 0.3s;
}.hero-fade-in--delay-3 {
  animation-delay: 0.4s;
}.hero-fade-in--delay-4 {
  animation-delay: 0.5s;
}.hero-fade-in--delay-5 {
  animation-delay: 0.6s;
}.hero-fade-in--delay-6 {
  animation-delay: 0.7s;
}.hero-fade-in--delay-7 {
  animation-delay: 0.8s;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 768px) {.hero-section {
  padding-top: 80px;
}
}

@media (max-width: 480px) {
}

/* ===== ACCESSIBILITY & MOTION ===== */
@media (prefers-reduced-motion: reduce) {.hero-gradient-overlay {
  transition: none;
}
}

/* ===== SELECTION STYLING ===== */
::selection {
  background-color: var(--enterprise-brown);
  color: white;
}::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}