/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.jsx","import":"Cormorant_Garamond","arguments":[{"subsets":["latin"],"weight":["300","400"],"display":"swap","variable":"--font-cormorant","preload":true}],"variableName":"cormorant"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/a2bfe7f39b1eebf5-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/5676475b14971f9e-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/eb6885ee7e3f5299-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/0d293583de0bf52f-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/fc6b86356f45d8cd-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/a2bfe7f39b1eebf5-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/5676475b14971f9e-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/eb6885ee7e3f5299-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/0d293583de0bf52f-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/fc6b86356f45d8cd-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Cormorant Garamond Fallback';src: local("Times New Roman");ascent-override: 95.27%;descent-override: 29.59%;line-gap-override: 0.00%;size-adjust: 96.98%
}.__className_37cd3e {font-family: 'Cormorant Garamond', 'Cormorant Garamond Fallback';font-style: normal
}.__variable_37cd3e {--font-cormorant: 'Cormorant Garamond', 'Cormorant Garamond Fallback'
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.jsx","import":"Inter","arguments":[{"subsets":["latin"],"weight":["300","400"],"display":"swap","variable":"--font-inter","preload":true}],"variableName":"inter"} ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_8bf340 {font-family: 'Inter', 'Inter Fallback';font-style: normal
}.__variable_8bf340 {--font-inter: 'Inter', 'Inter Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/hero.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🏛️ BAKASANA HERO STYLES - OPTIMIZED & CLEAN
   ============================================= */

/* ===== CORE HERO SECTION ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding-top: 120px;
}.hero-bg {
  position: absolute;
  z-index: 0;
  background-color: #FCF6EE;
  background-image: url('/images/background/bali-hero.webp');
  background-size: cover;
  background-position: center;
  background-attachment: scroll;
  background-repeat: no-repeat;
  inset: 0;
}.hero-parallax-layer {
  position: absolute;
  z-index: 0;
  background-image: url('/images/background/bali-hero.webp');
  background-size: 120%;
  background-attachment: fixed;
  background-repeat: no-repeat;
  inset: 0;
  will-change: transform, background-position;
}

/* ===== HERO OVERLAYS ===== */
.hero-gradient-overlay {
  position: absolute;
  inset: 0;
  z-index: 10;
  background: 
    radial-gradient(circle at 77.3% 95.9%, 
      rgba(252,246,238,0.3) 0%, 
      rgba(255,255,255,0.6) 40%, 
      rgba(255,255,255,0.9) 100%
    ),
    linear-gradient(
      180deg,
      rgba(252,246,238,0.4) 0%,
      rgba(255,255,255,0.7) 60%,
      rgba(255,255,255,0.9) 100%
    );
  transition: background 0.3s ease-out;
}.hero-gradient-overlay--interactive {
  background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), 
      rgba(252,246,238,0.3) 0%, 
      rgba(255,255,255,0.6) 40%, 
      rgba(255,255,255,0.9) 100%
    ),
    linear-gradient(
      180deg,
      rgba(252,246,238,0.4) 0%,
      rgba(255,255,255,0.7) 60%,
      rgba(255,255,255,0.9) 100%
    );
}.hero-texture-overlay {
  position: absolute;
  z-index: 10;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.2;
  inset: 0;
  mix-blend-mode: multiply;
}

/* ===== HERO CONTENT ===== */
.hero-content {
  position: relative;
  z-index: 20;
  text-align: center;
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1.5rem;
}.hero-badge {
  display: inline-block;
  margin-bottom: 1.5rem;
  color: var(--enterprise-brown);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 3.5px;
}.hero-title {
  margin-top: -10px;
  margin-bottom: 1rem;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(100px, 15vw, 200px);
  font-weight: 300;
  line-height: 0.95;
  letter-spacing: 0.15em;
}.hero-subtitle {
  margin-bottom: 2rem;
  color: var(--enterprise-brown);
  font-family: 'Cormorant Garamond', serif;
  font-size: 21px;
  font-weight: 300;
  font-style: italic;
  opacity: 0.8;
}.hero-description {
  max-width: 620px;
  margin: 0 auto 4rem auto;
  color: var(--charcoal-light);
  font-size: 17px;
  font-weight: 400;
  line-height: 1.85;
}

/* ===== HERO STATS ===== */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem 5rem;
  max-width: 64rem;
  margin: 0 auto 4rem auto;
}

@media (min-width: 768px) {.hero-stats {
  grid-template-columns: repeat(4, 1fr);
}
}.hero-stat {
  text-align: center;
}.hero-stat-number {
  margin-bottom: 0.5rem;
  color: var(--enterprise-brown);
  font-size: 42px;
  font-weight: 100;
}.hero-stat-label {
  color: var(--sage);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* ===== HERO BUTTONS ===== */
.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

@media (min-width: 640px) {.hero-buttons {
  flex-direction: row;
  gap: 1.5rem;
}
}.hero-button {
  display: inline-flex;
  align-items: center;
  padding: 16px 48px;
  border: 1px solid var(--enterprise-brown);
  background: transparent;
  color: var(--enterprise-brown);
  font-size: 13px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  letter-spacing: 2px;
}.hero-button:hover {
  background: var(--enterprise-brown);
  color: white;
  transform: translateY(-2px);
}.hero-button:focus {
  opacity: 0.7;
  outline: none;
}.hero-button--whatsapp {
  border-color: #25D366;
  color: #25D366;
}.hero-button--whatsapp:hover {
  background: #25D366;
  color: white;
}

/* ===== HERO SIDE FORM ===== */
.hero-side-form {
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  background: var(--pearl);
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  padding: 2rem;
  max-width: 20rem;
  width: 320px;
  border: 1px solid rgba(193, 155, 104, 0.1);
  box-shadow: 0 10px 40px rgba(0,0,0,0.06);
}

@media (max-width: 1279px) {.hero-side-form {
  display: none;
}
}.hero-side-form h3 {
  margin-bottom: 0.5rem;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.25rem;
}.hero-side-form p {
  margin-bottom: 1.5rem;
  color: var(--sage);
  font-size: 0.875rem;
}.hero-side-form input {
  width: 100%;
  padding: 0.75rem 0;
  border: 0;
  border-bottom: 1px solid var(--ash);
  background: transparent;
  font-size: 0.875rem;
  transition: border-color 0.3s ease;
}.hero-side-form input:focus {
  border-bottom-color: var(--enterprise-brown);
  outline: none;
}.hero-side-form .form-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  background: var(--enterprise-brown);
  color: white;
  font-size: 13px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.3s ease;
  letter-spacing: 2px;
}.hero-side-form .form-button:hover {
  background: var(--terra);
}.hero-side-form .form-button:focus {
  opacity: 0.8;
  outline: none;
}

/* ===== FLOATING ELEMENTS ===== */
.hero-floating-elements {
  position: absolute;
  inset: 0;
  z-index: 10;
  pointer-events: none;
}.hero-floating-dot {
  position: absolute;
  background: var(--temple-gold);
  animation: gentle-pulse 3s ease-in-out infinite;
}.hero-floating-dot:nth-child(1) {
  top: 25%;
  left: 25%;
  width: 8px;
  height: 8px;
  opacity: 0.3;
}.hero-floating-dot:nth-child(2) {
  top: 75%;
  right: 25%;
  width: 4px;
  height: 4px;
  opacity: 0.4;
  animation-delay: 1s;
}.hero-floating-dot:nth-child(3) {
  top: 50%;
  left: 75%;
  width: 12px;
  height: 12px;
  opacity: 0.2;
  animation-delay: 2s;
}

/* ===== ANIMATIONS ===== */
@keyframes gentle-pulse {0%, 100% {
  transform: scale(1);
  opacity: 0.3;
}50% {
  transform: scale(1.1);
  opacity: 0.8;
}
}.hero-fade-in {
  transform: translateY(20px);
  animation: hero-fade-in 0.8s ease-out forwards;
  opacity: 0;
}

@keyframes hero-fade-in {from {
  transform: translateY(20px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}

/* Staggered animations */
.hero-fade-in--delay-1 { animation-delay: 0.2s; }.hero-fade-in--delay-2 {
  animation-delay: 0.3s;
}.hero-fade-in--delay-3 {
  animation-delay: 0.4s;
}.hero-fade-in--delay-4 {
  animation-delay: 0.5s;
}.hero-fade-in--delay-5 {
  animation-delay: 0.6s;
}.hero-fade-in--delay-6 {
  animation-delay: 0.7s;
}.hero-fade-in--delay-7 {
  animation-delay: 0.8s;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 768px) {.hero-section {
  padding-top: 80px;
}
}

@media (max-width: 480px) {
}

/* ===== ACCESSIBILITY & MOTION ===== */
@media (prefers-reduced-motion: reduce) {.hero-gradient-overlay {
  transition: none;
}
}

/* ===== SELECTION STYLING ===== */
::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}
::selection {
  background-color: var(--enterprise-brown);
  color: white;
}::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/sections.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🏛️ BAKASANA SECTION STYLES - OPTIMIZED & REUSABLE
   ============================================= */

/* ===== BASE SECTION STYLES ===== */
.section {
  padding: 5rem 0;
}.section--large {
  padding: 8rem 0;
}.section--medium {
  padding: 4rem 0;
}.section--small {
  padding: 3rem 0;
}.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}.section-content {
  max-width: 64rem;
  margin: 0 auto;
  text-align: center;
}

/* ===== SECTION HEADERS ===== */
.section-header {
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(2.25rem, 5vw, 3.5rem);
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.3;
  color: var(--charcoal);
  margin-bottom: 2rem;
}.section-subtitle {
  max-width: 32rem;
  margin: 0 auto 4rem auto;
  color: var(--sage);
  font-size: 1.125rem;
  line-height: 1.6;
}

/* ===== BACKGROUND VARIANTS ===== */
.section--linen {
  background-color: var(--linen);
}.section--sanctuary {
  background-color: var(--sanctuary);
}.section--whisper {
  background-color: var(--whisper);
}.section--pearl {
  background-color: var(--pearl);
}.section--silk {
  background-color: var(--silk);
}

/* ===== GRID LAYOUTS ===== */
.section-grid {
  display: grid;
  gap: 2rem;
  margin-top: 4rem;
}.section-grid--2 {
  grid-template-columns: 1fr;
}.section-grid--3 {
  grid-template-columns: 1fr;
}.section-grid--4 {
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 768px) {
}

/* ===== CARD COMPONENTS ===== */
.section-card {
  background: var(--silk);
  padding: 2rem;
  border: 1px solid rgba(139, 134, 128, 0.08);
  transition: all 0.5s ease;
  text-align: center;
  group: hover;
}.section-card:hover {
  background: var(--whisper);
  box-shadow: 0 8px 25px rgba(26, 24, 22, 0.08);
  transform: translateY(-2px);
}.section-card-title {
  margin-bottom: 1rem;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.75rem;
  font-weight: 400;
  transition: color 0.3s ease;
}.section-card:hover .section-card-title {
  color: var(--enterprise-brown);
}.section-card-text {
  margin-bottom: 1.5rem;
  color: var(--sage);
  line-height: 1.6;
}.section-card-link {
  display: inline-flex;
  align-items: center;
  color: var(--enterprise-brown);
  font-weight: 300;
  text-decoration: none;
  transition: all 0.3s ease;
  letter-spacing: 0.125rem;
}.section-card-link:hover {
  color: var(--terra);
}.section-card-link .arrow {
  margin-left: 0.5rem;
  transform: translateX(0);
  transition: transform 0.3s ease;
}.section-card:hover .section-card-link .arrow {
  transform: translateX(4px);
}

/* ===== TESTIMONIAL CARDS ===== */
.testimonial-card {
  background: var(--sanctuary);
  padding: 2rem;
  border: 1px solid rgba(139, 134, 128, 0.05);
  box-shadow: 0 2px 10px rgba(26, 24, 22, 0.04);
}.testimonial-rating {
  display: flex;
  margin-bottom: 1rem;
  color: var(--enterprise-brown);
  font-size: 1.25rem;
}.testimonial-text {
  margin-bottom: 1.5rem;
  color: var(--charcoal-light);
  font-style: italic;
  line-height: 1.6;
}.testimonial-author {
  color: var(--sage);
  font-size: 0.875rem;
}.testimonial-author strong {
  color: var(--charcoal);
}

/* ===== FAQ SECTION ===== */
.faq-item {
  background: var(--sanctuary);
  padding: 1.5rem;
  border: 1px solid rgba(139, 134, 128, 0.05);
  margin-bottom: 1.5rem;
}.faq-question {
  margin-bottom: 1rem;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.25rem;
  font-weight: 400;
}.faq-answer {
  color: var(--charcoal-light);
  line-height: 1.6;
}

/* ===== STATS SECTION ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 48rem;
  margin: 0 auto;
}

@media (min-width: 768px) {.stats-grid {
  grid-template-columns: repeat(4, 1fr);
}
}.stat-item {
  text-align: center;
}.stat-number {
  margin-bottom: 0.5rem;
  color: var(--enterprise-brown);
  font-size: 2.625rem;
  font-weight: 100;
}.stat-label {
  color: var(--sage);
  font-size: 0.625rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.125rem;
}

/* ===== BUTTONS & LINKS ===== */
.section-button {
  display: inline-flex;
  align-items: center;
  padding: 1rem 3rem;
  border: 1px solid var(--enterprise-brown);
  color: var(--enterprise-brown);
  background: transparent;
  font-weight: 300;
  letter-spacing: 0.125rem;
  text-transform: uppercase;
  text-decoration: none;
  font-size: 0.8125rem;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}.section-button:hover {
  background: var(--enterprise-brown);
  color: white;
  transform: translateY(-2px);
}.section-button:focus {
  opacity: 0.7;
  outline: none;
}.section-button--primary {
  background: var(--enterprise-brown);
  color: white;
}.section-button--primary:hover {
  background: var(--terra);
}.section-button--secondary {
  border-color: var(--terra);
  color: var(--terra);
}.section-button--secondary:hover {
  background: var(--terra);
  color: white;
}

/* ===== SPACING UTILITIES ===== */
.spacing-micro { margin-bottom: 0.5rem; }.spacing-small {
  margin-bottom: 1rem;
}.spacing-medium {
  margin-bottom: 2rem;
}.spacing-large {
  margin-bottom: 4rem;
}.spacing-xlarge {
  margin-bottom: 6rem;
}

/* ===== ANIMATIONS ===== */
@keyframes section-fade-in {from {
  transform: translateY(30px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}.section-fade-in {
  animation: section-fade-in 0.8s ease-out forwards;
}.section-fade-in--delay-1 {
  animation-delay: 0.1s;
}.section-fade-in--delay-2 {
  animation-delay: 0.2s;
}.section-fade-in--delay-3 {
  animation-delay: 0.3s;
}.section-fade-in--delay-4 {
  animation-delay: 0.4s;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {.section {
  padding: 3rem 0;
}.section-header {
  font-size: clamp(2rem, 8vw, 2.5rem);
}.section-card {
  padding: 1.5rem;
}.section-button {
  padding: 0.875rem 2rem;
  font-size: 0.75rem;
}
}

@media (max-width: 480px) {
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {.section-card,
  .section-button,
  .section-card-title,
  .section-card-link {
  transition: none;
}
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
}
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/modern-css.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🚀 BAKASANA MODERN CSS FEATURES
   Cutting-edge CSS for Perfect 10/10 Experience
   ============================================= */

/* ===== CSS CONTAINER QUERIES ===== */
@supports (container-type: inline-size) {
  /* Container query contexts */
  .container-query {
    container-type: inline-size;
    container-name: main-container;
  }.card-container {
  container-name: card;
  container-type: inline-size;
}.sidebar-container {
  container-name: sidebar;
  container-type: inline-size;
}
  
  /* Responsive typography based on container size */
  @container main-container (max-width: 400px) {.container-responsive-text {
  font-size: clamp(0.875rem, 4vw, 1rem);
  line-height: 1.4;
}.container-responsive-heading {
  font-size: clamp(1.5rem, 6vw, 2rem);
  line-height: 1.2;
}
  }
  
  @container main-container (min-width: 401px) and (max-width: 800px) {
  }
  
  @container main-container (min-width: 801px) {
  }
  
  /* Card layout based on container size */
  @container card (max-width: 300px) {.card-content {
  flex-direction: column;
  padding: 1rem;
}.card-image {
  width: 100%;
  height: 150px;
}
  }
  
  @container card (min-width: 301px) and (max-width: 500px) {
  }
  
  @container card (min-width: 501px) {
  }
  
  /* Sidebar responsive behavior */
  @container sidebar (max-width: 200px) {.sidebar-nav {
  flex-direction: column;
  gap: 0.5rem;
}.sidebar-nav-item {
  padding: 0.5rem;
  font-size: 0.875rem;
}
  }
  
  @container sidebar (min-width: 201px) {
  }
}

/* ===== CSS GRID SUBGRID ===== */
@supports (grid-template-rows: subgrid) {.grid-parent {
  display: grid;
  grid-template-rows: repeat(3, auto);
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}.grid-child-subgrid {
  display: grid;
  grid-template-rows: subgrid;
  grid-template-columns: subgrid;
  gap: inherit;
  grid-column: span 2;
}
  
  /* Card grid with subgrid alignment */
  .card-grid-parent {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }.card-with-subgrid {
  display: grid;
  grid-template-rows: auto 1fr auto;
}.card-grid-container {
  display: grid;
  grid-template-rows: subgrid;
  grid-row: span 3;
}
}

/* ===== ADVANCED VIEWPORT UNITS ===== */
@supports (height: 100dvh) {
  /* Dynamic viewport height - accounts for mobile browser UI */
  .full-height-dynamic {
    height: 100dvh;
    min-height: 100dvh;
  }.hero-dynamic {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100dvh;
}
}

@supports (height: 100lvh) {
  /* Large viewport height - maximum possible viewport */
  .full-height-large {
    height: 100lvh;
    min-height: 100lvh;
  }
}

@supports (height: 100svh) {
  /* Small viewport height - minimum possible viewport */
  .full-height-small {
    height: 100svh;
    min-height: 100svh;
  }
}

/* Fallback for browsers without new viewport units */
@supports not (height: 100dvh) {.full-height-dynamic,
  .hero-dynamic {
  height: 100vh;
  min-height: 100vh;
}
  
  /* Mobile viewport fix */
  @media (max-width: 768px) {.full-height-dynamic,
    .hero-dynamic {
  height: calc(100vh - env(safe-area-inset-bottom));
  min-height: calc(100vh - env(safe-area-inset-bottom));
}
  }
}

/* ===== CSS LOGICAL PROPERTIES ===== */
.logical-spacing {
  margin-block: 2rem;
  margin-inline: 1rem;
  padding-block: 1rem;
  padding-inline: 1.5rem;
  border-inline-start: 3px solid var(--enterprise-brown);
}.logical-text {
  text-align: start;
  border-inline-end: 1px solid var(--stone);
  padding-inline-end: 1rem;
}

/* ===== CSS NESTING ===== */
@supports (selector(&)) {
  .nested-component {
    background: var(--sanctuary);
    padding: 2rem;
  }
    
    .nested-component .nested-title {color: var(--charcoal);
      margin-block-end: 1rem;
    }
      
      .nested-component .nested-title + .nested-subtitle {
  color: var(--stone);
  font-size: 0.875rem;
}
    
    .nested-component .nested-content {
      line-height: 1.6;
    }
      
      .nested-component .nested-content p {margin-block-end: 1rem;
      }
        
        .nested-component .nested-content p + p {
  margin-block-start: 0.5rem;
}
    
    .nested-component:hover {background: var(--whisper);
    }
      
      .nested-component:hover .nested-title {
  color: var(--enterprise-brown);
}
}

/* ===== CSS CUSTOM PROPERTIES WITH FALLBACKS ===== */
.advanced-custom-properties {
  /* Space-separated values */
  --shadow-layers: 
    0 1px 3px rgba(0,0,0,0.1),
    0 4px 6px rgba(0,0,0,0.05),
    0 10px 20px rgba(0,0,0,0.03);
  
  /* Complex gradients */
  --gradient-complex: 
    linear-gradient(135deg, 
      var(--sanctuary) 0%, 
      var(--whisper) 25%, 
      var(--linen) 50%, 
      var(--sanctuary) 100%);
  
  /* Animation sequences */
  --animation-sequence: 
    fadeIn 0.6s ease-out,
    slideUp 0.4s ease-out 0.2s,
    scaleIn 0.3s ease-out 0.4s;
  
  box-shadow: var(--shadow-layers);
  background: var(--gradient-complex);
  animation: var(--animation-sequence);
}

/* ===== CSS SCROLL-DRIVEN ANIMATIONS ===== */
@supports (animation-timeline: scroll()) {.scroll-driven-animation {
  animation: fadeInOnScroll linear;
  animation-range: entry 0% entry 100%;
  animation-timeline: scroll();
}
  
  @keyframes fadeInOnScroll {from {
  transform: translateY(50px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
  }.parallax-scroll {
  animation: parallaxMove linear;
  animation-range: entry 0% exit 100%;
  animation-timeline: scroll();
}
  
  @keyframes parallaxMove {
  }
}

/* ===== CSS ANCHOR POSITIONING ===== */
@supports (anchor-name: --anchor) {.anchor-element {
  anchor-name: --tooltip-anchor;
}.anchored-tooltip {
  position: absolute;
  bottom: anchor(top);
  left: anchor(center);
  z-index: 1000;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  background: var(--charcoal);
  color: var(--sanctuary);
  font-size: 0.875rem;
  transform: translateX(-50%);
  position-anchor: --tooltip-anchor;
  white-space: nowrap;
}
}

/* ===== CSS MASONRY LAYOUT ===== */
@supports (grid-template-rows: masonry) {.masonry-grid {
  display: grid;
  grid-template-rows: masonry;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  align-tracks: start;
  gap: 2rem;
}.masonry-item {
  margin-bottom: 0;
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}
}

/* Fallback for browsers without masonry */
@supports not (grid-template-rows: masonry) {
}

/* ===== MODERN CSS COMPONENTS ===== */

/* Base styles for modern CSS features */
.modern-css-base * {
  box-sizing: border-box;
}.modern-css-base body {
  margin: 0;
  background: var(--sanctuary);
  color: var(--charcoal);
  font-family: var(--font-secondary);
  line-height: 1.6;
}

/* Component styles */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}.modern-card {
  padding: 2rem;
  border: 1px solid var(--stone);
  background: var(--sanctuary);
  box-shadow: var(--shadow-subtle);
}

/* Utility styles */
.modern-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}.modern-focus-visible:focus-visible {
  outline: 2px solid var(--enterprise-brown);
  outline-offset: 2px;
}

/* ===== PROGRESSIVE ENHANCEMENT ===== */
@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {.glass-effect {
  background: rgba(253, 252, 248, 0.8);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}
}

@supports (color: color(display-p3 1 0 0)) {.wide-gamut-colors {
  background: color(display-p3 0.98 0.96 0.94);
  color: color(display-p3 0.8 0.4 0.2);
}
}

@supports (font-variation-settings: normal) {.variable-font {
  font-variation-settings: "wght" 400,
      "slnt" 0;
}.variable-font-bold {
  font-variation-settings: "wght" 700,
      "slnt" 0;
}
}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/main.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🏛️ BAKASANA OPTIMIZED STYLES - MAIN ENTRY POINT
   Clean, organized, and maintainable CSS architecture
   ============================================= */

/* Import optimized component styles */

/* Core variables and base styles */
:root {
  /* ===== COLOR PALETTE ===== */
  --sanctuary: #FDFCF8;
  --whisper: #F9F7F3;
  --rice: #FAF8F4;
  --linen: #F6F2E8;
  --pearl: #F8F5F0;
  --silk: #F4F0E8;
  
  --charcoal: #2A2724;
  --charcoal-light: #4A453F;
  --stone: #8B8680;
  --stone-light: #B5B0A8;
  --sage: #A8B5A8;
  --ash: #D2CDC6;
  
  --temple-gold: #B8935C;
  --enterprise-brown: #8B7355;
  --terra: #A0845C;
  --golden-amber: #D4AF37;
  
  --pure-white: #FFFFFF;
  --soft-black: #1A1816;
  
  /* ===== TYPOGRAPHY ===== */
  --font-primary: 'Cormorant Garamond', 'Crimson Text', serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* ===== SPACING SYSTEM ===== */
  --container-max: 1200px;
  --section-padding: 5rem 0;
  --element-breathing: 8%;
  --card-internal: 2rem;
  --micro-spacing: 1rem;
  
  /* ===== SHADOWS ===== */
  --shadow-subtle: 0 2px 10px rgba(26, 24, 22, 0.04);
  --shadow-elevated: 0 8px 25px rgba(26, 24, 22, 0.08);
  --shadow-premium: 0 10px 40px rgba(0, 0, 0, 0.06);
  
  /* ===== TRANSITIONS ===== */
  --transition-base: 0.3s ease;
  --transition-smooth: 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
   /* BAKASANA RULE: Zero border-radius */
}html {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  scroll-behavior: smooth;
  text-rendering: optimizeLegibility;
}body {
  overflow-x: hidden;
  background: var(--sanctuary);
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-weight: 300;
  line-height: 1.8;
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: top 0.3s ease;
}.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== FOCUS STATES ===== */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

/* ===== SELECTION STYLING ===== */
::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}
::selection {
  background-color: var(--enterprise-brown);
  color: white;
}::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}

/* ===== CURSOR STYLING ===== */
* {
  cursor: default;
}a, 
button, 
[role="button"], 
input[type="submit"],
input[type="button"] {
  cursor: pointer;
}

/* ===== TYPOGRAPHY UTILITIES ===== */
.font-primary {
  font-family: var(--font-primary);
}.font-secondary {
  font-family: var(--font-secondary);
}.font-cormorant {
  font-family: 'Cormorant Garamond', serif;
}.font-inter {
  font-family: 'Inter', sans-serif;
}

/* ===== RESPONSIVE IMAGES ===== */
img {
  max-width: 100%;
  height: auto;
}

/* ===== FORM ELEMENTS ===== */
input,
textarea,
select {
  background: var(--sanctuary);
  border: 1px solid var(--stone-light);
  color: var(--charcoal);
  font-family: var(--font-secondary);
  padding: 0.75rem 1rem;
  transition: border-color 0.3s ease;
}input:focus,
textarea:focus,
select:focus {
  border-color: var(--temple-gold);
  outline: none;
}

/* ===== BUTTON RESET ===== */
button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* ===== LINK RESET ===== */
a {
  color: inherit;
  text-decoration: none;
}

/* ===== UTILITY CLASSES ===== */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}.text-center {
  text-align: center;
}.text-left {
  text-align: left;
}.text-right {
  text-align: right;
}.uppercase {
  text-transform: uppercase;
}.lowercase {
  text-transform: lowercase;
}.capitalize {
  text-transform: capitalize;
}

/* ===== MOTION PREFERENCES ===== */
@media (prefers-reduced-motion: reduce) {*,
  *::before,
  *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}
}

/* ===== PRINT STYLES ===== */
@media print {* {
  background: transparent !important;
  box-shadow: none !important;
  color: black !important;
  text-shadow: none !important;
}a,
  a:visited {
  text-decoration: underline;
}img {
  page-break-inside: avoid;
}h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
  page-break-after: avoid;
}
}

/* ===== CONTAINER QUERIES SUPPORT ===== */
@supports (container-type: inline-size) {.container-query {
  container-type: inline-size;
}
}

/* ===== MODERN CSS FEATURES ===== */
@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {.backdrop-blur {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
}

@supports (display: grid) {.grid-fallback {
  display: grid;
}
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {:root {
  --charcoal: #FDFCF8;
  --charcoal-light: #F9F7F3;
  --sage: #A8B5A8;
  --sanctuary: #1A1816;
  --stone: #B5B0A8;
  --whisper: #2A2724;
}
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}.will-change-opacity {
  will-change: opacity;
}.contain-layout {
  contain: layout;
}.contain-paint {
  contain: paint;
}.contain-strict {
  contain: strict;
}
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/enhanced-globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* Enhanced BAKASANA Design System - World-Class Visual Identity */
/* Fonts loaded via Next.js font optimization system */
/* @import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,200;0,300;0,400;0,500;0,600;1,200;1,300;1,400;1,500&family=Inter:wght@100;200;300;400;500;600;700&display=swap'); */

:root {
  /* Enhanced Brand Colors - Spiritual & Sophisticated */
  --golden-lotus: #C9A575;
  --deep-golden: #B8956A;
  --light-golden: #D4B685;
  --sanctuary: #FDFCF8;
  --soft-sanctuary: #F9F7F2;
  --warm-sanctuary: #F5F3EF;
  --charcoal: #3A3A3A;
  --soft-charcoal: #5A5A5A;
  --light-charcoal: #8A8A8A;
  --mist: #E8E6E2;
  --soft-mist: #F0EFEB;
  --sage: #8B9A8C;
  --soft-sage: #A8B4A9;
  --temple: #D4AF37;
  --soft-temple: #E6C65B;
  
  /* Enhanced Typography */
  --font-primary: 'Cormorant Garamond', serif;
  --font-secondary: 'Inter', sans-serif;
  --font-accent: 'Playfair Display', serif;
  
  /* Enhanced Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;
  
  /* Enhanced Shadows */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-soft: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-medium: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-golden: 0 8px 25px rgba(201, 165, 117, 0.3);
  
  /* Enhanced Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Enhanced Borders */
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;
}

/* Enhanced Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}html {
  font-size: 16px;
  scroll-behavior: smooth;
}body {
  overflow-x: hidden;
  background: var(--sanctuary);
  color: var(--charcoal);
  font-family: var(--font-secondary);
  line-height: 1.6;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

/* Enhanced Typography System */
.text-brand-primary {
  font-family: var(--font-primary);
  font-weight: 400;
  letter-spacing: 0.025em;
}.text-brand-secondary {
  font-family: var(--font-secondary);
  font-weight: 400;
}.text-brand-accent {
  font-family: var(--font-accent);
  font-weight: 400;
}

/* Enhanced Heading System */
.heading-hero {
  font-family: var(--font-primary);
  font-size: clamp(3rem, 8vw, 7.5rem);
  font-weight: 200;
  letter-spacing: 0.25em;
  line-height: 0.9;
  color: var(--sanctuary);
  text-transform: uppercase;
  margin-bottom: var(--space-lg);
}.heading-section {
  margin-bottom: var(--space-xl);
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: 0.1em;
}.heading-subsection {
  margin-bottom: var(--space-lg);
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 400;
  line-height: 1.3;
  letter-spacing: 0.05em;
}.heading-card {
  margin-bottom: var(--space-md);
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: clamp(1.25rem, 2vw, 1.75rem);
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0.025em;
}

/* Enhanced Button System */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-2xl);
  background: var(--golden-lotus);
  color: var(--sanctuary);
  border: none;
  
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.025em;
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  box-shadow: var(--shadow-soft);
  position: relative;
  overflow: hidden;
}.btn-primary:hover {
  background: var(--deep-golden);
  box-shadow: var(--shadow-golden);
  transform: translateY(-2px);
}.btn-primary::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
  content: '';
}.btn-primary:hover::before {
  left: 100%;
}.btn-secondary {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: var(--space-md) var(--space-2xl);
  border: 2px solid var(--golden-lotus);
  background: transparent;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  letter-spacing: 0.025em;
}.btn-secondary:hover {
  border-color: var(--deep-golden);
  box-shadow: var(--shadow-soft);
  color: var(--sanctuary);
  transform: translateY(-2px);
}.btn-secondary::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 0;
  height: 100%;
  background: var(--golden-lotus);
  transition: width var(--transition-normal);
  content: '';
}.btn-secondary:hover::before {
  width: 100%;
}.btn-ghost {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-sm) var(--space-lg);
  border: 1px solid transparent;
  background: transparent;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 0.875rem;
  font-weight: 400;
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  letter-spacing: 0.025em;
}.btn-ghost:hover {
  border-color: var(--golden-lotus);
  color: var(--golden-lotus);
}.btn-ghost::after {
  margin-left: var(--space-sm);
  transition: transform var(--transition-normal);
  content: '→';
}.btn-ghost:hover::after {
  transform: translateX(4px);
}

/* Enhanced Card System */
.card-elevated {
  background: var(--sanctuary);
  
  padding: var(--space-2xl);
  box-shadow: var(--shadow-medium);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}.card-elevated:hover {
  box-shadow: var(--shadow-large);
  transform: translateY(-8px);
}.card-elevated::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--golden-lotus), var(--temple));
  transition: opacity var(--transition-normal);
  opacity: 0;
  content: '';
}.card-elevated:hover::before {
  opacity: 1;
}.card-minimal {
  padding: var(--space-xl);
  border: 1px solid var(--mist);
  background: var(--soft-sanctuary);
  transition: all var(--transition-normal);
}.card-minimal:hover {
  border-color: var(--golden-lotus);
  background: var(--sanctuary);
  box-shadow: var(--shadow-soft);
  transform: translateY(-2px);
}

/* Enhanced Section Dividers */
.section-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--space-5xl) 0;
  position: relative;
}.section-divider::before {
  position: absolute;
  right: 0;
  left: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--mist), transparent);
  content: '';
}.section-divider-diamond {
  position: relative;
  z-index: 1;
  width: 20px;
  height: 20px;
  background: var(--golden-lotus);
  transform: rotate(45deg);
}.section-divider-diamond::before {
  position: absolute;
  top: 4px;
  right: 4px;
  bottom: 4px;
  left: 4px;
  background: var(--sanctuary);
  content: '';
}

/* Enhanced Animations */
@keyframes fadeInUp {from {
  transform: translateY(30px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}

@keyframes fadeInScale {
}

@keyframes float {0%, 100% {
  transform: translateY(0px);
}50% {
  transform: translateY(-10px);
}
}

@keyframes shimmer {0% {
  background-position: -1000px 0;
}100% {
  background-position: 1000px 0;
}
}.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}.animate-float {
  animation: float 3s ease-in-out infinite;
}.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {.heading-hero {
  font-size: clamp(2rem, 12vw, 4rem);
  letter-spacing: 0.15em;
}.btn-primary,
  .btn-secondary {
  padding: var(--space-sm) var(--space-lg);
  font-size: 0.875rem;
}.card-elevated {
  padding: var(--space-lg);
}.section-divider {
  margin: var(--space-3xl) 0;
}
}

/* Enhanced Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: 9999;
  padding: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  text-decoration: none;
  transition: top var(--transition-normal);
}.skip-link:focus {
  top: 6px;
}

/* Enhanced Focus States */
*:focus {
  outline: 2px solid var(--golden-lotus);
  outline-offset: 2px;
}button:focus,
a:focus {
  outline: 2px solid var(--golden-lotus);
  outline-offset: 2px;
}

/* Enhanced Print Styles */
@media print {* {
  background: transparent !important;
  box-shadow: none !important;
  color: black !important;
  text-shadow: none !important;
}.btn-primary,
  .btn-secondary,
  .btn-ghost {
  border: 1px solid black;
  background: transparent;
  color: black;
}
}

/* Enhanced Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}.will-change-transform {
  will-change: transform;
}.will-change-opacity {
  will-change: opacity;
}

/* Enhanced Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--mist) 25%, var(--soft-mist) 50%, var(--mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  
}.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
}

/* Enhanced Scroll Behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}.scroll-padding {
  scroll-padding-top: 80px;
}
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/bakasana-visuals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🎯 BAKASANA OPTIMIZED - ULTRA MINIMALIST & COHESIVE
   Perfect for yoga retreats in Bali & Sri Lanka
   Subtle black & white accents as requested
   ============================================= */

/* =============================================
   1. PROFESSIONAL HERO SECTION - RETREAT STYLE
   ============================================= */

/* Enhanced professional hero styles matching retreat page */
.professional-hero {
  background: linear-gradient(135deg, 
    rgba(253, 252, 248, 0.95) 0%,
    rgba(249, 247, 242, 0.98) 50%,
    rgba(245, 243, 239, 0.95) 100%
  );
}.professional-hero-badge {
  border: 1px solid rgba(193, 155, 104, 0.15);
  box-shadow: 0 4px 20px rgba(193, 155, 104, 0.08);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}.professional-hero-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}.professional-hero-button::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
  content: '';
}.professional-hero-button:hover::before {
  left: 100%;
}.professional-hero-stats {
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
}.professional-hero-stats .stat-value {
  background: linear-gradient(135deg, #C19B68 0%, #D1A46E 100%);
  font-weight: 500;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}.professional-hero-scroll {
  animation: gentleFloat 3s ease-in-out infinite;
}

@keyframes gentleFloat {0%, 100% {
  transform: translateY(0px);
  opacity: 0.4;
}50% {
  transform: translateY(10px);
  opacity: 0.8;
}
}

/* =============================================
   1. ORIGINAL HERO SECTION - FULL IMPLEMENTATION (BACKUP)
   ============================================= */

.bakasana-hero {
  position: relative;
  height: 100vh;
  width: 100%;
  background: linear-gradient(
    135deg,
    rgba(44, 41, 40, 0.4) 0%,
    rgba(44, 41, 40, 0.6) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}.bakasana-hero::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  background-image: url('/images/background/bali-hero-low-res.webp'),
    url('/images/background/bali-hero-1200.avif'),
    url('/images/background/bali-hero.webp');
  background-position: center 30%;
  /* Parallax effect */
  transition: filter 0.3s ease;
  /* Progressive loading: low-res → AVIF → WebP */
  background-size: cover;
  /* Slightly higher focus for better composition */
  background-repeat: no-repeat;
  background-attachment: fixed;
  content: '';
}.bakasana-hero-content {
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  color: white;
  text-align: center;
}.bakasana-hero-title {
  margin: 0 0 40px 0;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  font-size: 120px;
  font-weight: 200;
  line-height: 1.1;
  text-transform: uppercase;
  letter-spacing: 0.25em;
}.bakasana-hero-subtitle {
  margin: 0 0 20px 0;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 20px;
  font-weight: 400;
  opacity: 0.9;
}.bakasana-hero-quote {
  margin: 0 0 60px 0;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  font-size: 16px;
  font-style: italic;
  opacity: 0.8;
}.bakasana-scroll-hint {
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 300;
  animation: pulse 3s infinite;
  opacity: 0.7;
}

@keyframes pulse {
}

/* Responsive hero optimizations */
@media (max-width: 768px) {
}

@media (max-width: 480px) {
}

/* =============================================
   2. INTRO SECTION - NEW IMPLEMENTATION
   ============================================= */

.bakasana-intro {
  background: var(--sanctuary);
  padding: 100px 0;
  text-align: center;
}.bakasana-intro-divider {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 0 50px 0;
}.bakasana-intro-divider::before,
.bakasana-intro-divider::after {
  flex: 1;
  max-width: 80px;
  height: 1px;
  background: var(--stone-light);
  content: '';
}.bakasana-intro-divider::before {
  margin-right: 24px;
}.bakasana-intro-divider::after {
  margin-left: 24px;
}.bakasana-intro-diamond {
  width: 6px;
  height: 6px;
  background: var(--temple-gold);
  transform: rotate(45deg);
}.bakasana-intro-quote {
  max-width: 500px;
  margin: 0 0 50px 0;
  margin-right: auto;
  margin-left: auto;
  color: var(--temple-gold);
  font-family: 'Cormorant Garamond', serif;
  font-size: 33px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.3;
}.bakasana-intro-text {
  max-width: 550px;
  margin: 0 auto 50px auto;
  color: var(--charcoal);
  font-family: 'Inter', sans-serif;
  font-size: 17px;
  font-weight: 300;
  line-height: 1.7;
}.bakasana-intro-button {
  display: inline-block;
  padding: 18px 40px;
  border: 1px solid var(--soft-black);
  background: transparent;
  color: var(--soft-black);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 400;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.4s ease;
  letter-spacing: 0.5px;
}.bakasana-intro-button:hover {
  background: var(--soft-black);
  box-shadow: 0 4px 12px rgba(26, 24, 22, 0.15);
  color: var(--pure-white);
  transform: translateY(-1px);
}

/* =============================================
   3. THREE PATHS SECTION - TRZECH ŚCIEŻEK
   ============================================= */

.bakasana-paths {
  background: var(--whisper);
  padding: 100px 0;
  text-align: center;
  letter-spacing: 0.02em;
}.bakasana-paths-title {
  margin: 0 0 30px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 42px;
  font-weight: 300;
  letter-spacing: 0.02em;
}.bakasana-paths-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  max-width: 1100px;
  margin: 80px auto 0 auto;
  padding: 0 20px;
  gap: 30px;
}.bakasana-path-card {
  display: block;
  overflow: hidden;
  border: 1px solid rgba(139, 134, 128, 0.08);
  background: var(--pure-white);
  text-decoration: none;
  transition: all 0.4s ease;
}.bakasana-path-card:hover {
  border-color: var(--temple-gold);
  box-shadow: 0 11px 40px rgba(26, 24, 22, 0.08);
  transform: translateY(-3px);
}.bakasana-path-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 220px;
  background: linear-gradient(135deg, var(--stone-light) 0%, var(--whisper) 100%);
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 300;
}.bakasana-path-image::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(45deg, transparent 30%, var(--temple-gold) 50%, transparent 70%);
  transition: opacity 0.4s ease;
  opacity: 0;
  content: '';
}.bakasana-path-card:hover .bakasana-path-image::after {
  opacity: 0.03;
}.bakasana-path-content {
  padding: 35px 25px;
}.bakasana-path-category {
  margin: 0 0 12px 0;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.15em;
}.bakasana-path-title {
  margin: 0 0 15px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 22px;
  font-weight: 400;
  line-height: 1.3;
}.bakasana-path-description {
  margin: 0 0 20px 0;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  line-height: 1.6;
}.bakasana-path-price {
  color: var(--temple-gold);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* =============================================
   4. UPCOMING RETREATS - NAJBLIŻSZE TERMINY
   ============================================= */

.bakasana-retreats {
  background: var(--sanctuary);
  padding: 100px 0;
  text-align: center;
}.bakasana-retreats-title {
  margin: 0 0 30px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 42px;
  font-weight: 300;
  letter-spacing: 0.02em;
}.bakasana-retreats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  max-width: 1000px;
  margin: 80px auto 0 auto;
  padding: 0 20px;
  gap: 50px;
}.bakasana-retreat-card {
  overflow: hidden;
  padding: 0;
  border: 1px solid rgba(139, 134, 128, 0.06);
  background: var(--pure-white);
  text-align: left;
  transition: all 0.4s ease;
}.bakasana-retreat-card:hover {
  border-color: var(--temple-gold);
  box-shadow: 0 15px 45px rgba(26, 24, 22, 0.1);
  transform: translateY(-4px);
}.bakasana-retreat-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 260px;
  margin: 0;
  background: linear-gradient(135deg, var(--stone-light) 0%, var(--whisper) 100%);
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 300;
}.bakasana-retreat-image::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(45deg, transparent 30%, var(--temple-gold) 50%, transparent 70%);
  transition: opacity 0.4s ease;
  opacity: 0;
  content: '';
}.bakasana-retreat-card:hover .bakasana-retreat-image::after {
  opacity: 0.04;
}.bakasana-retreat-content {
  padding: 30px 25px;
}.bakasana-retreat-date {
  margin: 0 0 12px 0;
  color: var(--temple-gold);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.12em;
}.bakasana-retreat-title {
  margin: 0 0 8px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 26px;
  font-weight: 400;
  line-height: 1.3;
}.bakasana-retreat-location {
  margin: 0 0 20px 0;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 300;
}.bakasana-retreat-divider {
  width: 100%;
  height: 1px;
  margin: 20px 0;
  background: rgba(139, 134, 128, 0.12);
}.bakasana-retreat-features {
  margin: 0 0 25px 0;
  padding: 0;
  list-style: none;
}.bakasana-retreat-features li {
  position: relative;
  margin: 0 0 6px 0;
  padding-left: 12px;
  color: var(--charcoal);
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 300;
  line-height: 1.5;
}.bakasana-retreat-features li::before {
  position: absolute;
  left: 0;
  color: var(--temple-gold);
  font-size: 16px;
  line-height: 1.2;
  content: '·';
}.bakasana-retreat-price {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 28px;
  font-weight: 400;
  letter-spacing: 0.01em;
}.bakasana-retreat-cta {
  display: inline-block;
  padding: 14px 28px;
  background: var(--soft-black);
  color: var(--pure-white);
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.4s ease;
  letter-spacing: 0.05em;
}.bakasana-retreat-cta:hover {
  background: var(--charcoal);
  box-shadow: 0 4px 12px rgba(26, 24, 22, 0.2);
  transform: translateY(-1px);
}

/* =============================================
   5. ABOUT JULIA - CIEPLEJSZA WERSJA
   ============================================= */

.bakasana-about-julia {
  background: var(--whisper);
  padding: 100px 0;
}.bakasana-julia-container {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 70px;
}.bakasana-julia-text {
  order: 1;
}.bakasana-julia-image-wrapper {
  order: 2;
}.bakasana-julia-quote {
  margin: 0 0 30px 0;
  color: #C9A575;
  font-family: 'Cormorant Garamond', serif;
  font-size: 32px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.3;
}.bakasana-julia-signature {
  margin: 0 0 40px 0;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 20px;
}.bakasana-julia-bio {
  margin: 0 0 40px 0;
  color: #2C2928;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 1.7;
}.bakasana-julia-stats {
  margin: 0 0 40px 0;
  padding: 0;
  list-style: none;
}.bakasana-julia-stats li {
  position: relative;
  margin: 0 0 15px 0;
  padding-left: 25px;
  color: #2C2928;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}.bakasana-julia-stats li::before {
  position: absolute;
  left: 0;
  color: #C9A575;
  font-size: 16px;
}.bakasana-julia-stats li:nth-child(1)::before {
  content: '∞';
}.bakasana-julia-stats li:nth-child(2)::before {
  content: '♡';
}.bakasana-julia-stats li:nth-child(3)::before {
  content: 'ॐ';
}.bakasana-julia-cta {
  display: inline-block;
  padding: 16px 40px;
  border: 1px solid #C9A575;
  background: transparent;
  color: #C9A575;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.3s ease;
}.bakasana-julia-cta:hover {
  background: #C9A575;
  color: white;
}.bakasana-julia-photo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 500px;
  background: #f0f0f0;
  color: #999;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}

/* =============================================
   6. SOCIAL PROOF - INSTAGRAM INTEGRATION
   ============================================= */

.bakasana-social {
  background: white;
  padding: 120px 0;
  text-align: center;
}.bakasana-social-handle {
  margin: 0 0 60px 0;
  color: #2C2928;
  font-family: 'Inter', sans-serif;
  font-size: 24px;
  font-weight: 400;
}.bakasana-instagram-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  max-width: 800px;
  margin: 0 auto 60px auto;
  padding: 0 20px;
  gap: 20px;
}.bakasana-instagram-item {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0f0f0;
  color: #999;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  transition: transform 0.3s ease;
  aspect-ratio: 1;
}.bakasana-instagram-item:hover {
  transform: scale(1.05);
}.bakasana-social-cta {
  display: inline-block;
  padding: 16px 40px;
  background: #C9A575;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  text-decoration: none;
  transition: background 0.3s ease;
}.bakasana-social-cta:hover {
  background: #B8935C;
}

/* =============================================
   7. CONTACT - UPROSZCZONY
   ============================================= */

.bakasana-contact {
  background: linear-gradient(135deg, #FDF9F3 0%, #F7F4F0 100%);
  padding: 120px 0;
  text-align: center;
}.bakasana-contact-title {
  margin: 0 0 20px 0;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 400;
}.bakasana-contact-subtitle {
  max-width: 600px;
  margin: 0 0 60px 0;
  margin-right: auto;
  margin-left: auto;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 18px;
}.bakasana-contact-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 40px;
}.bakasana-contact-option {
  padding: 40px 30px;
  background: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
  text-decoration: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}.bakasana-contact-option:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}.bakasana-contact-option h3 {
  margin: 0 0 15px 0;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 24px;
  font-weight: 500;
}.bakasana-contact-option p {
  margin: 0;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
}.bakasana-contact-om {
  margin: 60px 0 0 0;
  color: #C9A575;
  font-size: 32px;
}

/* =============================================
   8. FOOTER - MINIMALISTYCZNY
   ============================================= */

.bakasana-footer {
  background: #2C2928;
  color: white;
  padding: 80px 0;
  text-align: center;
}.bakasana-footer-logo {
  margin: 0 0 10px 0;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  font-size: 36px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.bakasana-footer-subtitle {
  margin: 0 0 40px 0;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
}.bakasana-footer-social {
  display: flex;
  justify-content: center;
  margin: 0 0 40px 0;
  gap: 30px;
}.bakasana-footer-social a {
  color: #9B9592;
  font-size: 24px;
  transition: color 0.3s ease;
}.bakasana-footer-social a:hover {
  color: #C9A575;
}.bakasana-footer-copyright {
  margin: 0;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}

/* =============================================
   ONLINE CLASSES SECTION - MINIMALISTIC ICONS
   ============================================= */

.online-section .feature-icon {
  font-size: 24px;
  color: #C9A575;
  margin-bottom: 20px;
  display: block;
  text-align: center;
}.online-section .feature-title {
  margin-bottom: 15px;
  color: #2C2928;
  font-family: 'Cormorant Garamond', serif;
  font-size: 22px;
  font-weight: 500;
  text-align: center;
}.online-section .feature-description {
  max-width: 280px;
  margin: 0 auto;
  color: #9B9592;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  text-align: center;
}.online-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 60px;
}.online-feature {
  padding: 40px 20px;
  text-align: center;
  transition: transform 0.3s ease;
}.online-feature:hover {
  transform: translateY(-3px);
}.online-feature:hover .feature-icon {
  color: #B8935C;
}

/* =============================================
   RESPONSIVE DESIGN
   ============================================= */

@media (max-width: 768px) {.bakasana-paths-grid,
  .bakasana-retreats-grid {
  grid-template-columns: 1fr;
}
}

@media (max-width: 480px) {.bakasana-paths-title,
  .bakasana-retreats-title,
  .bakasana-contact-title {
  font-size: 36px;
}
}
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/unified-system.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/**
 * BAKASANA UNIFIED DESIGN SYSTEM
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

/* =============================================
   GLOBAL WARM VARIABLES
   ============================================= */
:root {
  /* Warm Color Palette */
  --warm-sanctuary: #FDFCF8;
  --warm-charcoal: #2A2724;
  --warm-enterprise: #8B7355;
  --warm-terra: #B8935C;
  --warm-sage: #8B8680;
  --warm-stone: #A8A39E;
  
  /* Organic Spacing */
  --organic-xs: 0.5rem;
  --organic-sm: 1rem;
  --organic-md: 1.5rem;
  --organic-lg: 2rem;
  --organic-xl: 3rem;
  --organic-2xl: 4rem;
  
  /* Warm Shadows */
  --shadow-warm-subtle: 0 2px 8px rgba(26, 24, 22, 0.06);
  --shadow-warm-elegant: 0 4px 16px rgba(139, 115, 85, 0.08);
  --shadow-warm-premium: 0 8px 32px rgba(139, 115, 85, 0.12);
  
  /* Organic Transitions */
  --transition-organic: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-warm: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* =============================================
   UNIFIED BASE STYLES
   ============================================= */

/* Smooth scrolling with organic feel */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

/* Body with warm background */
body {
  background-color: var(--warm-sanctuary);
  color: var(--warm-charcoal);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.7;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Selection with warm colors */
::-moz-selection {
  background-color: var(--warm-enterprise);
  color: var(--warm-sanctuary);
}
::selection {
  background-color: var(--warm-enterprise);
  color: var(--warm-sanctuary);
}::-moz-selection {
  background-color: var(--warm-enterprise);
  color: var(--warm-sanctuary);
}

/* =============================================
   UNIFIED TYPOGRAPHY SYSTEM
   ============================================= */

/* Headings with organic spacing */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cormorant Garamond', Georgia, serif;
  font-weight: 300;
  color: var(--warm-charcoal);
  line-height: 1.2;
  margin-bottom: var(--organic-md);
}

/* Paragraphs with warm spacing */
p {
  margin-bottom: var(--organic-md);
  color: var(--warm-charcoal);
  opacity: 0.9;
}

/* Links with warm hover */
a {
  color: var(--warm-enterprise);
  text-decoration: none;
  transition: var(--transition-organic);
}a:hover {
  color: var(--warm-terra);
  transform: translateY(-1px);
}

/* =============================================
   UNIFIED COMPONENT STYLES
   ============================================= */

/* Buttons with organic feel */
.btn-unified {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: var(--transition-warm);
  cursor: pointer;
  border: none;
  outline: none;
}.btn-unified:hover {
  box-shadow: var(--shadow-warm-elegant);
  transform: translateY(-2px);
}.btn-unified:active {
  transform: translateY(0);
}

/* Cards with warm shadows */
.card-unified {
  background: var(--warm-sanctuary);
  
  box-shadow: var(--shadow-warm-subtle);
  transition: var(--transition-warm);
  overflow: hidden;
}.card-unified:hover {
  box-shadow: var(--shadow-warm-elegant);
  transform: translateY(-4px);
}

/* Inputs with organic styling */
.input-unified {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  background: var(--warm-sanctuary);
  border: 1px solid rgba(139, 115, 85, 0.2);
  color: var(--warm-charcoal);
  transition: var(--transition-organic);
  outline: none;
}.input-unified:focus {
  border-color: var(--warm-enterprise);
  box-shadow: 0 0 0 3px rgba(139, 115, 85, 0.1);
}.input-unified::-moz-placeholder {
  color: var(--warm-sage);
  font-weight: 300;
}.input-unified::placeholder {
  color: var(--warm-sage);
  font-weight: 300;
}

/* =============================================
   ORGANIC ANIMATIONS
   ============================================= */

/* Fade in with organic timing */
@keyframes fadeInOrganic {from {
  transform: translateY(20px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}.animate-fade-in-organic {
  animation: fadeInOrganic 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Staggered animations */
.animate-stagger-1 { animation-delay: 0.1s; }.animate-stagger-2 {
  animation-delay: 0.2s;
}.animate-stagger-3 {
  animation-delay: 0.3s;
}.animate-stagger-4 {
  animation-delay: 0.4s;
}

/* Warm pulse animation */
@keyframes warmPulse {0%, 100% {
  transform: scale(1);
  opacity: 0.8;
}50% {
  transform: scale(1.02);
  opacity: 1;
}
}.animate-warm-pulse {
  animation: warmPulse 3s ease-in-out infinite;
}

/* =============================================
   RESPONSIVE ORGANIC SPACING
   ============================================= */

/* Mobile-first organic spacing */
.section-organic {
  padding: var(--organic-2xl) var(--organic-md);
}

@media (min-width: 768px) {.section-organic {
  padding: calc(var(--organic-2xl) * 1.5) var(--organic-lg);
}
}

@media (min-width: 1024px) {
}

/* Container with organic max-width */
.container-organic {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--organic-md);
}

@media (min-width: 768px) {.container-organic {
  padding: 0 var(--organic-lg);
}
}

/* =============================================
   ACCESSIBILITY IMPROVEMENTS
   ============================================= */

/* Focus styles with warm colors */
*:focus-visible {
  outline: 2px solid var(--warm-enterprise);
  outline-offset: 2px;
  
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {*,
  *::before,
  *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}
}

/* High contrast support */
@media (prefers-contrast: high) {:root {
  --warm-charcoal: #000000;
  --warm-enterprise: #8B7355;
  --warm-sanctuary: #FFFFFF;
}
}

/* =============================================
   UTILITY CLASSES
   ============================================= */

/* Warm text colors */
.text-warm-primary { color: var(--warm-charcoal); }.text-warm-secondary {
  color: var(--warm-sage);
}.text-warm-accent {
  color: var(--warm-enterprise);
}.text-warm-muted {
  color: var(--warm-stone);
}

/* Warm backgrounds */
.bg-warm-primary { background-color: var(--warm-sanctuary); }.bg-warm-accent {
  background-color: var(--warm-enterprise);
}.bg-warm-muted {
  background-color: rgba(139, 115, 85, 0.05);
}

/* Organic spacing utilities */
.space-organic-xs { margin: var(--organic-xs); }.space-organic-sm {
  margin: var(--organic-sm);
}.space-organic-md {
  margin: var(--organic-md);
}.space-organic-lg {
  margin: var(--organic-lg);
}.space-organic-xl {
  margin: var(--organic-xl);
}

/* Warm shadows */
.shadow-warm-subtle { box-shadow: var(--shadow-warm-subtle); }.shadow-warm-elegant {
  box-shadow: var(--shadow-warm-elegant);
}.shadow-warm-premium {
  box-shadow: var(--shadow-warm-premium);
}

/* Organic borders */
.border-warm { border-color: rgba(139, 115, 85, 0.2); }.border-warm-strong {
  border-color: var(--warm-enterprise);
}

/* Smooth transitions */
.transition-organic { transition: var(--transition-organic); }.transition-warm {
  transition: var(--transition-warm);
}
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/color-migration.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/**
 * BAKASANA COLOR MIGRATION SYSTEM
 * Temporary aliases for smooth transition from legacy to unified colors
 * This file will be removed after full migration
 */

:root {
  /* =============================================
     LEGACY COLOR ALIASES - FOR MIGRATION ONLY
     These map old color names to new unified system
     ============================================= */
  
  /* Legacy temple colors → New charcoal system */
  --temple: var(--warm-charcoal);
  --temple-light: var(--charcoal-light);
  --temple-dark: var(--soft-black);
  
  /* Legacy golden colors → New enterprise system */
  --temple-gold: var(--warm-enterprise);
  --golden: var(--warm-terra);
  --golden-lotus: var(--warm-enterprise);
  --golden-amber: var(--warm-terra);
  
  /* Legacy wood colors → New charcoal variants */
  --wood-light: var(--charcoal-light);
  --wood-dark: var(--warm-charcoal);
  
  /* Legacy rice/mist → New sanctuary variants */
  --rice: var(--warm-sanctuary);
  --mist: var(--warm-linen);
  --shell: var(--warm-silk);
  
  /* System colors for blog posts - using brand colors */
  --blog-info: var(--warm-enterprise);
  --blog-info-bg: rgba(139, 115, 85, 0.05);
  --blog-info-border: rgba(139, 115, 85, 0.2);
  
  --blog-warning: var(--warm-terra);
  --blog-warning-bg: rgba(184, 147, 92, 0.05);
  --blog-warning-border: rgba(184, 147, 92, 0.2);
  
  --blog-success: var(--warm-enterprise);
  --blog-success-bg: rgba(139, 115, 85, 0.05);
  --blog-success-border: rgba(139, 115, 85, 0.2);
  
  --blog-error: #B85450;
  --blog-error-bg: rgba(184, 84, 80, 0.05);
  --blog-error-border: rgba(184, 84, 80, 0.2);
}

/* =============================================
   UTILITY CLASSES FOR MIGRATION
   ============================================= */

/* Legacy color classes that map to new system */
.text-charcoal { color: var(--warm-charcoal) !important; }.text-charcoal-gold {
  color: var(--warm-enterprise) !important;
}.text-sand {
  color: var(--warm-terra) !important;
}.text-sand-lotus {
  color: var(--warm-enterprise) !important;
}.text-charcoal-light {
  color: var(--charcoal-light) !important;
}.bg-charcoal {
  background-color: var(--warm-charcoal) !important;
}.bg-charcoal-gold {
  background-color: var(--warm-enterprise) !important;
}.bg-sand {
  background-color: var(--warm-terra) !important;
}.bg-sanctuary {
  background-color: var(--warm-sanctuary) !important;
}.bg-whisper {
  background-color: var(--warm-linen) !important;
}.bg-silk {
  background-color: var(--warm-silk) !important;
}.border-charcoal {
  border-color: var(--warm-charcoal) !important;
}.border-charcoal-gold {
  border-color: var(--warm-enterprise) !important;
}.border-sand {
  border-color: var(--warm-terra) !important;
}

/* Blog post color system using brand colors */
.bg-yellow-50 { background-color: var(--blog-warning-bg) !important; }.border-yellow-200 {
  border-color: var(--blog-warning-border) !important;
}.text-yellow-800 {
  color: var(--warm-terra) !important;
}.text-yellow-700 {
  color: var(--warm-terra) !important;
}.bg-red-50 {
  background-color: var(--blog-error-bg) !important;
}.border-red-200 {
  border-color: var(--blog-error-border) !important;
}.text-red-800 {
  color: var(--blog-error) !important;
}.text-red-700 {
  color: var(--blog-error) !important;
}.bg-blue-50 {
  background-color: var(--blog-info-bg) !important;
}.border-blue-200 {
  border-color: var(--blog-info-border) !important;
}.text-blue-800 {
  color: var(--warm-enterprise) !important;
}.text-blue-700 {
  color: var(--warm-enterprise) !important;
}.bg-green-50 {
  background-color: var(--blog-success-bg) !important;
}.border-green-200 {
  border-color: var(--blog-success-border) !important;
}.text-green-800 {
  color: var(--warm-enterprise) !important;
}.text-green-700 {
  color: var(--warm-enterprise) !important;
}.bg-purple-50 {
  background-color: var(--blog-info-bg) !important;
}.border-purple-200 {
  border-color: var(--blog-info-border) !important;
}.text-purple-800 {
  color: var(--warm-enterprise) !important;
}.text-purple-700 {
  color: var(--warm-enterprise) !important;
}

/* =============================================
   HOVER STATES WITH BRAND COLORS
   ============================================= */

.hover\:bg-charcoal\/10:hover { 
  background-color: rgba(42, 39, 36, 0.1) !important; 
}.hover\:bg-charcoal-gold\/10:hover {
  background-color: rgba(139, 115, 85, 0.1) !important;
}.hover\:text-charcoal:hover {
  color: var(--warm-charcoal) !important;
}.hover\:text-sand:hover {
  color: var(--warm-terra) !important;
}

/* =============================================
   PROSE STYLES FOR BLOG POSTS
   ============================================= */

.prose-headings\:text-charcoal .prose h1,
.prose-headings\:text-charcoal .prose h2,
.prose-headings\:text-charcoal .prose h3,
.prose-headings\:text-charcoal .prose h4,
.prose-headings\:text-charcoal .prose h5,
.prose-headings\:text-charcoal .prose h6 {
  color: var(--warm-charcoal) !important;
}.prose-p\:text-charcoal-light .prose p {
  color: var(--charcoal-light) !important;
}.prose-a\:text-charcoal .prose a {
  color: var(--warm-enterprise) !important;
}.hover\:prose-a\:text-sand .prose a:hover {
  color: var(--warm-terra) !important;
}.prose-strong\:text-charcoal .prose strong {
  color: var(--warm-charcoal) !important;
}.prose-blockquote\:border-l-temple .prose blockquote {
  border-left-color: var(--warm-enterprise) !important;
}.prose-blockquote\:bg-charcoal\/5 .prose blockquote {
  background-color: rgba(139, 115, 85, 0.05) !important;
}.prose-blockquote\:text-charcoal-light .prose blockquote {
  color: var(--charcoal-light) !important;
}.prose-ul\:text-charcoal-light .prose ul {
  color: var(--charcoal-light) !important;
}.prose-ol\:text-charcoal-light .prose ol {
  color: var(--charcoal-light) !important;
}

/* =============================================
   MIGRATION NOTES
   ============================================= */

/*
  TODO: After migration is complete, remove this file and update:
  
  1. All components to use new color names directly
  2. Remove legacy color definitions from tailwind.config.js
  3. Update all className attributes to use new color system
  4. Test all pages for visual consistency
  
  Migration mapping:
  - temple → charcoal
  - temple-gold → enterprise-brown  
  - golden → terra
  - wood-light → charcoal-light
  - rice → sanctuary
  - mist → linen
  - shell → silk
*/
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.\!container {
  width: 100% !important;
  margin-right: auto !important;
  margin-left: auto !important;
  padding-right: clamp(1rem, 4vw, 1.5rem) !important;
  padding-left: clamp(1rem, 4vw, 1.5rem) !important;
}
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: clamp(1rem, 4vw, 1.5rem);
  padding-left: clamp(1rem, 4vw, 1.5rem);
}
@media (min-width: 480px) {

  .\!container {
    max-width: 480px !important;
    padding-right: clamp(1rem, 4vw, 1.5rem) !important;
    padding-left: clamp(1rem, 4vw, 1.5rem) !important;
  }

  .container {
    max-width: 480px;
    padding-right: clamp(1rem, 4vw, 1.5rem);
    padding-left: clamp(1rem, 4vw, 1.5rem);
  }
}
@media (min-width: 768px) {

  .\!container {
    max-width: 768px !important;
    padding-right: clamp(1.5rem, 5vw, 3rem) !important;
    padding-left: clamp(1.5rem, 5vw, 3rem) !important;
  }

  .container {
    max-width: 768px;
    padding-right: clamp(1.5rem, 5vw, 3rem);
    padding-left: clamp(1.5rem, 5vw, 3rem);
  }
}
@media (min-width: 1024px) {

  .\!container {
    max-width: 1024px !important;
    padding-right: clamp(2rem, 6vw, 4rem) !important;
    padding-left: clamp(2rem, 6vw, 4rem) !important;
  }

  .container {
    max-width: 1024px;
    padding-right: clamp(2rem, 6vw, 4rem);
    padding-left: clamp(2rem, 6vw, 4rem);
  }
}
@media (min-width: 1440px) {

  .\!container {
    max-width: 1440px !important;
    padding-right: clamp(3rem, 7vw, 5rem) !important;
    padding-left: clamp(3rem, 7vw, 5rem) !important;
  }

  .container {
    max-width: 1440px;
    padding-right: clamp(3rem, 7vw, 5rem);
    padding-left: clamp(3rem, 7vw, 5rem);
  }
}
@media (min-width: 1920px) {

  .\!container {
    max-width: 1920px !important;
    padding-right: clamp(4rem, 8vw, 6rem) !important;
    padding-left: clamp(4rem, 8vw, 6rem) !important;
  }

  .container {
    max-width: 1920px;
    padding-right: clamp(4rem, 8vw, 6rem);
    padding-left: clamp(4rem, 8vw, 6rem);
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-2 {
  inset: 0.5rem;
}
.-bottom-0\.5 {
  bottom: -0.125rem;
}
.-bottom-1 {
  bottom: -0.25rem;
}
.-bottom-10 {
  bottom: -2.5rem;
}
.-bottom-2 {
  bottom: -0.5rem;
}
.-bottom-4 {
  bottom: -1rem;
}
.-bottom-6 {
  bottom: -1.5rem;
}
.-bottom-8 {
  bottom: -2rem;
}
.-left-10 {
  left: -2.5rem;
}
.-left-2 {
  left: -0.5rem;
}
.-left-4 {
  left: -1rem;
}
.-left-8 {
  left: -2rem;
}
.-right-1 {
  right: -0.25rem;
}
.-right-10 {
  right: -2.5rem;
}
.-right-2 {
  right: -0.5rem;
}
.-right-4 {
  right: -1rem;
}
.-right-5 {
  right: -1.25rem;
}
.-right-6 {
  right: -1.5rem;
}
.-right-8 {
  right: -2rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-10 {
  top: -2.5rem;
}
.-top-2 {
  top: -0.5rem;
}
.-top-3 {
  top: -0.75rem;
}
.-top-4 {
  top: -1rem;
}
.-top-5 {
  top: -1.25rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1\/3 {
  bottom: 33.333333%;
}
.bottom-1\/4 {
  bottom: 25%;
}
.bottom-10 {
  bottom: 2.5rem;
}
.bottom-12 {
  bottom: 3rem;
}
.bottom-16 {
  bottom: 4rem;
}
.bottom-20 {
  bottom: 5rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-8 {
  bottom: 2rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-0\.5 {
  left: 0.125rem;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/3 {
  left: 33.333333%;
}
.left-1\/4 {
  left: 25%;
}
.left-3\/4 {
  left: 75%;
}
.left-4 {
  left: 1rem;
}
.left-6 {
  left: 1.5rem;
}
.left-8 {
  left: 2rem;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-1\/3 {
  right: 33.333333%;
}
.right-1\/4 {
  right: 25%;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-6 {
  right: 1.5rem;
}
.right-8 {
  right: 2rem;
}
.right-full {
  right: 100%;
}
.top-0 {
  top: 0px;
}
.top-0\.5 {
  top: 0.125rem;
}
.top-1\/2 {
  top: 50%;
}
.top-1\/3 {
  top: 33.333333%;
}
.top-1\/4 {
  top: 25%;
}
.top-2\/3 {
  top: 66.666667%;
}
.top-3\/4 {
  top: 75%;
}
.top-4 {
  top: 1rem;
}
.top-6 {
  top: 1.5rem;
}
.top-8 {
  top: 2rem;
}
.top-full {
  top: 100%;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[1\] {
  z-index: 1;
}
.z-\[2\] {
  z-index: 2;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-section {
  margin-top: 120px;
  margin-bottom: 120px;
}
.-mb-1 {
  margin-bottom: -0.25rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-mr-1 {
  margin-right: -0.25rem;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.-mt-2 {
  margin-top: -0.5rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-2xl {
  margin-bottom: 64px;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-lg {
  margin-bottom: 32px;
}
.mb-md {
  margin-bottom: 24px;
}
.mb-sm {
  margin-bottom: 16px;
}
.mb-xl {
  margin-bottom: 48px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-2xl {
  margin-top: 64px;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-lg {
  margin-top: 32px;
}
.mt-md {
  margin-top: 24px;
}
.mt-sm {
  margin-top: 16px;
}
.mt-xl {
  margin-top: 48px;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-\[3\/4\] {
  aspect-ratio: 3/4;
}
.aspect-\[4\/3\] {
  aspect-ratio: 4/3;
}
.aspect-\[4\/5\] {
  aspect-ratio: 4/5;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-1\/3 {
  height: 33.333333%;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-2\/3 {
  height: 66.666667%;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[0\.5px\] {
  height: 0.5px;
}
.h-\[1\.5px\] {
  height: 1.5px;
}
.h-\[18px\] {
  height: 18px;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[40vh\] {
  height: 40vh;
}
.h-\[500px\] {
  height: 500px;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[60vh\] {
  height: 60vh;
}
.h-\[70vh\] {
  height: 70vh;
}
.h-full {
  height: 100%;
}
.h-nav-height {
  height: 80px;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-0 {
  max-height: 0px;
}
.max-h-\[2000px\] {
  max-height: 2000px;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.max-h-full {
  max-height: 100%;
}
.max-h-screen {
  max-height: 100vh;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[44px\] {
  min-height: 44px;
}
.min-h-\[48px\] {
  min-height: 48px;
}
.min-h-\[52px\] {
  min-height: 52px;
}
.min-h-\[56px\] {
  min-height: 56px;
}
.min-h-\[60px\] {
  min-height: 60px;
}
.min-h-\[60vh\] {
  min-height: 60vh;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-4\/5 {
  width: 80%;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-5\/6 {
  width: 83.333333%;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[18px\] {
  width: 18px;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.min-w-\[140px\] {
  min-width: 140px;
}
.min-w-\[280px\] {
  min-width: 280px;
}
.min-w-\[300px\] {
  min-width: 300px;
}
.min-w-\[48px\] {
  min-width: 48px;
}
.min-w-full {
  min-width: 100%;
}
.max-w-20 {
  max-width: 5rem;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-48 {
  max-width: 12rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[620px\] {
  max-width: 620px;
}
.max-w-\[85vw\] {
  max-width: 85vw;
}
.max-w-\[calc\(100vw-2rem\)\] {
  max-width: calc(100vw - 2rem);
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-left {
  transform-origin: left;
}
.-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-4 {
  --tw-translate-x: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-2 {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-\[1px\] {
  --tw-translate-y: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-\[4px\] {
  --tw-translate-y: -4px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-6 {
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-8 {
  --tw-translate-x: 2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-12 {
  --tw-translate-y: 3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-16 {
  --tw-translate-y: 4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2 {
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-20 {
  --tw-translate-y: 5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-6 {
  --tw-translate-y: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-8 {
  --tw-translate-y: 2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[1px\] {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[4px\] {
  --tw-translate-y: 4px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-skew-x-12 {
  --tw-skew-x: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-0 {
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
@keyframes fadeIn {

  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}
.animate-none {
  animation: none;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes pulseGentle {

  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}
.animate-pulse-gentle {
  animation: pulseGentle 2s ease-in-out infinite;
}
@keyframes shimmer {

  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}
.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-wait {
  cursor: wait;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-0 {
  gap: 0px;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-20 {
  gap: 5rem;
}
.gap-2xl {
  gap: 64px;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-lg {
  gap: 32px;
}
.gap-md {
  gap: 24px;
}
.gap-sm {
  gap: 16px;
}
.gap-xl {
  gap: 48px;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2.5rem * var(--tw-space-x-reverse));
  margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(3rem * var(--tw-space-x-reverse));
  margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}
.space-y-16 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-y-lg > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(32px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(32px * var(--tw-space-y-reverse));
}
.space-y-md > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(24px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(24px * var(--tw-space-y-reverse));
}
.space-y-sm > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(16px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(16px * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-temple\/10 > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(42 39 36 / 0.1);
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-balance {
  text-wrap: balance;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-4 {
  border-right-width: 4px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-solid {
  border-style: solid;
}
.border-accent\/10 {
  border-color: rgb(139 115 85 / 0.1);
}
.border-accent\/20 {
  border-color: rgb(139 115 85 / 0.2);
}
.border-accent\/30 {
  border-color: rgb(139 115 85 / 0.3);
}
.border-accent\/5 {
  border-color: rgb(139 115 85 / 0.05);
}
.border-ash {
  --tw-border-opacity: 1;
  border-color: rgb(107 101 96 / var(--tw-border-opacity, 1));
}
.border-ash\/20 {
  border-color: rgb(107 101 96 / 0.2);
}
.border-ash\/30 {
  border-color: rgb(107 101 96 / 0.3);
}
.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-charcoal {
  --tw-border-opacity: 1;
  border-color: rgb(42 39 36 / var(--tw-border-opacity, 1));
}
.border-charcoal-light\/10 {
  border-color: rgb(74 69 63 / 0.1);
}
.border-charcoal\/10 {
  border-color: rgb(42 39 36 / 0.1);
}
.border-charcoal\/20 {
  border-color: rgb(42 39 36 / 0.2);
}
.border-charcoal\/30 {
  border-color: rgb(42 39 36 / 0.3);
}
.border-current {
  border-color: currentColor;
}
.border-enterprise-brown {
  --tw-border-opacity: 1;
  border-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}
.border-enterprise-brown\/10 {
  border-color: rgb(139 115 85 / 0.1);
}
.border-enterprise-brown\/20 {
  border-color: rgb(139 115 85 / 0.2);
}
.border-enterprise-brown\/30 {
  border-color: rgb(139 115 85 / 0.3);
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-sage\/10 {
  border-color: rgb(139 134 128 / 0.1);
}
.border-sage\/15 {
  border-color: rgb(139 134 128 / 0.15);
}
.border-sage\/20 {
  border-color: rgb(139 134 128 / 0.2);
}
.border-sage\/30 {
  border-color: rgb(139 134 128 / 0.3);
}
.border-sanctuary {
  --tw-border-opacity: 1;
  border-color: rgb(253 252 248 / var(--tw-border-opacity, 1));
}
.border-sanctuary\/20 {
  border-color: rgb(253 252 248 / 0.2);
}
.border-sanctuary\/30 {
  border-color: rgb(253 252 248 / 0.3);
}
.border-sanctuary\/60 {
  border-color: rgb(253 252 248 / 0.6);
}
.border-sanctuary\/70 {
  border-color: rgb(253 252 248 / 0.7);
}
.border-sand {
  --tw-border-opacity: 1;
  border-color: rgb(212 175 122 / var(--tw-border-opacity, 1));
}
.border-silk {
  --tw-border-opacity: 1;
  border-color: rgb(240 237 232 / var(--tw-border-opacity, 1));
}
.border-stone {
  --tw-border-opacity: 1;
  border-color: rgb(168 163 158 / var(--tw-border-opacity, 1));
}
.border-stone-light {
  --tw-border-opacity: 1;
  border-color: rgb(196 191 184 / var(--tw-border-opacity, 1));
}
.border-stone-light\/20 {
  border-color: rgb(196 191 184 / 0.2);
}
.border-stone-light\/30 {
  border-color: rgb(196 191 184 / 0.3);
}
.border-stone-light\/50 {
  border-color: rgb(196 191 184 / 0.5);
}
.border-stone\/10 {
  border-color: rgb(168 163 158 / 0.1);
}
.border-stone\/20 {
  border-color: rgb(168 163 158 / 0.2);
}
.border-stone\/30 {
  border-color: rgb(168 163 158 / 0.3);
}
.border-terra {
  --tw-border-opacity: 1;
  border-color: rgb(184 147 92 / var(--tw-border-opacity, 1));
}
.border-terra\/15 {
  border-color: rgb(184 147 92 / 0.15);
}
.border-transparent {
  border-color: transparent;
}
.border-warm-gold\/20 {
  border-color: rgb(193 155 104 / 0.2);
}
.border-whisper {
  --tw-border-opacity: 1;
  border-color: rgb(245 242 237 / var(--tw-border-opacity, 1));
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-white\/50 {
  border-color: rgb(255 255 255 / 0.5);
}
.border-r-ash {
  --tw-border-opacity: 1;
  border-right-color: rgb(107 101 96 / var(--tw-border-opacity, 1));
}
.border-r-terra {
  --tw-border-opacity: 1;
  border-right-color: rgb(184 147 92 / var(--tw-border-opacity, 1));
}
.border-t-accent {
  --tw-border-opacity: 1;
  border-top-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}
.border-t-charcoal {
  --tw-border-opacity: 1;
  border-top-color: rgb(42 39 36 / var(--tw-border-opacity, 1));
}
.border-t-enterprise-brown {
  --tw-border-opacity: 1;
  border-top-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}
.border-t-gray-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-accent\/10 {
  background-color: rgb(139 115 85 / 0.1);
}
.bg-accent\/20 {
  background-color: rgb(139 115 85 / 0.2);
}
.bg-accent\/40 {
  background-color: rgb(139 115 85 / 0.4);
}
.bg-accent\/5 {
  background-color: rgb(139 115 85 / 0.05);
}
.bg-accent\/60 {
  background-color: rgb(139 115 85 / 0.6);
}
.bg-accent\/90 {
  background-color: rgb(139 115 85 / 0.9);
}
.bg-ash\/10 {
  background-color: rgb(107 101 96 / 0.1);
}
.bg-background {
  --tw-bg-opacity: 1;
  background-color: rgb(253 252 248 / var(--tw-bg-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}
.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/5 {
  background-color: rgb(0 0 0 / 0.05);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/90 {
  background-color: rgb(0 0 0 / 0.9);
}
.bg-black\/95 {
  background-color: rgb(0 0 0 / 0.95);
}
.bg-black\/\[0\.02\] {
  background-color: rgb(0 0 0 / 0.02);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/5 {
  background-color: rgb(59 130 246 / 0.05);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-charcoal {
  --tw-bg-opacity: 1;
  background-color: rgb(42 39 36 / var(--tw-bg-opacity, 1));
}
.bg-charcoal\/10 {
  background-color: rgb(42 39 36 / 0.1);
}
.bg-charcoal\/15 {
  background-color: rgb(42 39 36 / 0.15);
}
.bg-charcoal\/20 {
  background-color: rgb(42 39 36 / 0.2);
}
.bg-charcoal\/30 {
  background-color: rgb(42 39 36 / 0.3);
}
.bg-charcoal\/5 {
  background-color: rgb(42 39 36 / 0.05);
}
.bg-charcoal\/80 {
  background-color: rgb(42 39 36 / 0.8);
}
.bg-charcoal\/90 {
  background-color: rgb(42 39 36 / 0.9);
}
.bg-current {
  background-color: currentColor;
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-emerald-500\/5 {
  background-color: rgb(16 185 129 / 0.05);
}
.bg-enterprise-brown {
  --tw-bg-opacity: 1;
  background-color: rgb(139 115 85 / var(--tw-bg-opacity, 1));
}
.bg-enterprise-brown\/10 {
  background-color: rgb(139 115 85 / 0.1);
}
.bg-enterprise-brown\/20 {
  background-color: rgb(139 115 85 / 0.2);
}
.bg-enterprise-brown\/40 {
  background-color: rgb(139 115 85 / 0.4);
}
.bg-enterprise-brown\/5 {
  background-color: rgb(139 115 85 / 0.05);
}
.bg-enterprise-brown\/50 {
  background-color: rgb(139 115 85 / 0.5);
}
.bg-glass-nav {
  background-color: rgba(253, 252, 248, 0.95);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-400\/30 {
  background-color: rgb(74 222 128 / 0.3);
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-linen {
  --tw-bg-opacity: 1;
  background-color: rgb(249 246 241 / var(--tw-bg-opacity, 1));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-pink-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(42 39 36 / var(--tw-bg-opacity, 1));
}
.bg-pure-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-sage {
  --tw-bg-opacity: 1;
  background-color: rgb(139 134 128 / var(--tw-bg-opacity, 1));
}
.bg-sage\/10 {
  background-color: rgb(139 134 128 / 0.1);
}
.bg-sage\/5 {
  background-color: rgb(139 134 128 / 0.05);
}
.bg-sanctuary {
  --tw-bg-opacity: 1;
  background-color: rgb(253 252 248 / var(--tw-bg-opacity, 1));
}
.bg-sanctuary\/10 {
  background-color: rgb(253 252 248 / 0.1);
}
.bg-sanctuary\/30 {
  background-color: rgb(253 252 248 / 0.3);
}
.bg-sanctuary\/5 {
  background-color: rgb(253 252 248 / 0.05);
}
.bg-sanctuary\/50 {
  background-color: rgb(253 252 248 / 0.5);
}
.bg-sanctuary\/60 {
  background-color: rgb(253 252 248 / 0.6);
}
.bg-sanctuary\/70 {
  background-color: rgb(253 252 248 / 0.7);
}
.bg-sanctuary\/80 {
  background-color: rgb(253 252 248 / 0.8);
}
.bg-sanctuary\/90 {
  background-color: rgb(253 252 248 / 0.9);
}
.bg-sanctuary\/95 {
  background-color: rgb(253 252 248 / 0.95);
}
.bg-sand {
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 122 / var(--tw-bg-opacity, 1));
}
.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(107 101 96 / var(--tw-bg-opacity, 1));
}
.bg-secondary\/40 {
  background-color: rgb(107 101 96 / 0.4);
}
.bg-secondary\/60 {
  background-color: rgb(107 101 96 / 0.6);
}
.bg-silk {
  --tw-bg-opacity: 1;
  background-color: rgb(240 237 232 / var(--tw-bg-opacity, 1));
}
.bg-silk\/20 {
  background-color: rgb(240 237 232 / 0.2);
}
.bg-silk\/30 {
  background-color: rgb(240 237 232 / 0.3);
}
.bg-silk\/40 {
  background-color: rgb(240 237 232 / 0.4);
}
.bg-silk\/60 {
  background-color: rgb(240 237 232 / 0.6);
}
.bg-soft-black {
  --tw-bg-opacity: 1;
  background-color: rgb(26 24 22 / var(--tw-bg-opacity, 1));
}
.bg-stone-light {
  --tw-bg-opacity: 1;
  background-color: rgb(196 191 184 / var(--tw-bg-opacity, 1));
}
.bg-stone-light\/20 {
  background-color: rgb(196 191 184 / 0.2);
}
.bg-stone-light\/30 {
  background-color: rgb(196 191 184 / 0.3);
}
.bg-stone\/20 {
  background-color: rgb(168 163 158 / 0.2);
}
.bg-stone\/30 {
  background-color: rgb(168 163 158 / 0.3);
}
.bg-terra {
  --tw-bg-opacity: 1;
  background-color: rgb(184 147 92 / var(--tw-bg-opacity, 1));
}
.bg-terra\/10 {
  background-color: rgb(184 147 92 / 0.1);
}
.bg-terra\/20 {
  background-color: rgb(184 147 92 / 0.2);
}
.bg-terra\/25 {
  background-color: rgb(184 147 92 / 0.25);
}
.bg-terra\/5 {
  background-color: rgb(184 147 92 / 0.05);
}
.bg-transparent {
  background-color: transparent;
}
.bg-warm-gold\/5 {
  background-color: rgb(193 155 104 / 0.05);
}
.bg-whisper {
  --tw-bg-opacity: 1;
  background-color: rgb(245 242 237 / var(--tw-bg-opacity, 1));
}
.bg-whisper\/30 {
  background-color: rgb(245 242 237 / 0.3);
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}
.bg-white\/40 {
  background-color: rgb(255 255 255 / 0.4);
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/85 {
  background-color: rgb(255 255 255 / 0.85);
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-opacity-0 {
  --tw-bg-opacity: 0;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}
.bg-\[url\(\'\/images\/background\/map-pattern\.svg\'\)\] {
  background-image: url('/images/background/map-pattern.svg');
}
.bg-\[url\(\'data\:image\/svg\+xml\;base64\2c PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iIzZCNkI2QiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K\'\)\] {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iIzZCNkI2QiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-black\/10 {
  --tw-gradient-from: rgb(0 0 0 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/30 {
  --tw-gradient-from: rgb(0 0 0 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/60 {
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-charcoal {
  --tw-gradient-from: #2A2724 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-charcoal\/10 {
  --tw-gradient-from: rgb(42 39 36 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-charcoal\/20 {
  --tw-gradient-from: rgb(42 39 36 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-charcoal\/5 {
  --tw-gradient-from: rgb(42 39 36 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-charcoal\/60 {
  --tw-gradient-from: rgb(42 39 36 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-charcoal\/70 {
  --tw-gradient-from: rgb(42 39 36 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-clay {
  --tw-gradient-from: #9B7B5F var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(155 123 95 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-enterprise-brown {
  --tw-gradient-from: #8B7355 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-enterprise-brown\/10 {
  --tw-gradient-from: rgb(139 115 85 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-enterprise-brown\/20 {
  --tw-gradient-from: rgb(139 115 85 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-enterprise-brown\/30 {
  --tw-gradient-from: rgb(139 115 85 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-enterprise-brown\/5 {
  --tw-gradient-from: rgb(139 115 85 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-golden-lotus {
  --tw-gradient-from: #8B7355 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-golden-lotus\/10 {
  --tw-gradient-from: rgb(139 115 85 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-golden-lotus\/5 {
  --tw-gradient-from: rgb(139 115 85 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-hero-overlay {
  --tw-gradient-from: rgba(252, 246, 238, 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(252, 246, 238, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-linen {
  --tw-gradient-from: #F9F6F1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 246 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-linen\/50 {
  --tw-gradient-from: rgb(249 246 241 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 246 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-parchment {
  --tw-gradient-from: #FCF6EE var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(252 246 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sage\/5 {
  --tw-gradient-from: rgb(139 134 128 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 134 128 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary {
  --tw-gradient-from: #FDFCF8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary\/40 {
  --tw-gradient-from: rgb(253 252 248 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary\/50 {
  --tw-gradient-from: rgb(253 252 248 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary\/60 {
  --tw-gradient-from: rgb(253 252 248 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary\/70 {
  --tw-gradient-from: rgb(253 252 248 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary\/80 {
  --tw-gradient-from: rgb(253 252 248 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sanctuary\/90 {
  --tw-gradient-from: rgb(253 252 248 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 252 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-soft-black\/10 {
  --tw-gradient-from: rgb(26 24 22 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 24 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-soft-black\/20 {
  --tw-gradient-from: rgb(26 24 22 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 24 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-soft-black\/40 {
  --tw-gradient-from: rgb(26 24 22 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 24 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-stone {
  --tw-gradient-from: #A8A39E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 163 158 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-whisper {
  --tw-gradient-from: #F5F2ED var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-whisper\/30 {
  --tw-gradient-from: rgb(245 242 237 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/5 {
  --tw-gradient-from: rgb(255 255 255 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-accent\/30 {
  --tw-gradient-to: rgb(139 115 85 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(139 115 85 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/20 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-enterprise-brown {
  --tw-gradient-to: rgb(139 115 85 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #8B7355 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-enterprise-brown\/5 {
  --tw-gradient-to: rgb(139 115 85 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(139 115 85 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-golden-amber\/20 {
  --tw-gradient-to: rgb(184 147 92 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(184 147 92 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-golden\/5 {
  --tw-gradient-to: rgb(212 175 122 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(212 175 122 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-orange-50 {
  --tw-gradient-to: rgb(255 247 237 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff7ed var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-sanctuary {
  --tw-gradient-to: rgb(253 252 248 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #FDFCF8 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-sanctuary\/40 {
  --tw-gradient-to: rgb(253 252 248 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(253 252 248 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-sanctuary\/90 {
  --tw-gradient-to: rgb(253 252 248 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(253 252 248 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-sand\/15 {
  --tw-gradient-to: rgb(212 175 122 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(212 175 122 / 0.15) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-soft-black\/25 {
  --tw-gradient-to: rgb(26 24 22 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(26 24 22 / 0.25) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-temple\/20 {
  --tw-gradient-to: rgb(42 39 36 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(42 39 36 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-terra {
  --tw-gradient-to: rgb(184 147 92 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #B8935C var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-terra\/30 {
  --tw-gradient-to: rgb(184 147 92 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(184 147 92 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-warm-gold\/10 {
  --tw-gradient-to: rgb(193 155 104 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(193 155 104 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-whisper {
  --tw-gradient-to: rgb(245 242 237 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F5F2ED var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/20 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/70 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.7) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-ash {
  --tw-gradient-to: #6B6560 var(--tw-gradient-to-position);
}
.to-black\/40 {
  --tw-gradient-to: rgb(0 0 0 / 0.4) var(--tw-gradient-to-position);
}
.to-charcoal\/20 {
  --tw-gradient-to: rgb(42 39 36 / 0.2) var(--tw-gradient-to-position);
}
.to-enterprise-brown {
  --tw-gradient-to: #8B7355 var(--tw-gradient-to-position);
}
.to-enterprise-brown\/10 {
  --tw-gradient-to: rgb(139 115 85 / 0.1) var(--tw-gradient-to-position);
}
.to-enterprise-brown\/20 {
  --tw-gradient-to: rgb(139 115 85 / 0.2) var(--tw-gradient-to-position);
}
.to-golden {
  --tw-gradient-to: #D4AF7A var(--tw-gradient-to-position);
}
.to-golden-amber {
  --tw-gradient-to: #B8935C var(--tw-gradient-to-position);
}
.to-golden-lotus\/5 {
  --tw-gradient-to: rgb(139 115 85 / 0.05) var(--tw-gradient-to-position);
}
.to-golden\/10 {
  --tw-gradient-to: rgb(212 175 122 / 0.1) var(--tw-gradient-to-position);
}
.to-golden\/20 {
  --tw-gradient-to: rgb(212 175 122 / 0.2) var(--tw-gradient-to-position);
}
.to-golden\/5 {
  --tw-gradient-to: rgb(212 175 122 / 0.05) var(--tw-gradient-to-position);
}
.to-green-600 {
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}
.to-orange-50\/50 {
  --tw-gradient-to: rgb(255 247 237 / 0.5) var(--tw-gradient-to-position);
}
.to-sage\/10 {
  --tw-gradient-to: rgb(139 134 128 / 0.1) var(--tw-gradient-to-position);
}
.to-sanctuary {
  --tw-gradient-to: #FDFCF8 var(--tw-gradient-to-position);
}
.to-sanctuary\/60 {
  --tw-gradient-to: rgb(253 252 248 / 0.6) var(--tw-gradient-to-position);
}
.to-sanctuary\/70 {
  --tw-gradient-to: rgb(253 252 248 / 0.7) var(--tw-gradient-to-position);
}
.to-sanctuary\/80 {
  --tw-gradient-to: rgb(253 252 248 / 0.8) var(--tw-gradient-to-position);
}
.to-sand {
  --tw-gradient-to: #D4AF7A var(--tw-gradient-to-position);
}
.to-sand\/30 {
  --tw-gradient-to: rgb(212 175 122 / 0.3) var(--tw-gradient-to-position);
}
.to-silk {
  --tw-gradient-to: #F0EDE8 var(--tw-gradient-to-position);
}
.to-soft-black\/50 {
  --tw-gradient-to: rgb(26 24 22 / 0.5) var(--tw-gradient-to-position);
}
.to-soft-bronze {
  --tw-gradient-to: #A67C52 var(--tw-gradient-to-position);
}
.to-temple {
  --tw-gradient-to: #2A2724 var(--tw-gradient-to-position);
}
.to-temple\/10 {
  --tw-gradient-to: rgb(42 39 36 / 0.1) var(--tw-gradient-to-position);
}
.to-temple\/5 {
  --tw-gradient-to: rgb(42 39 36 / 0.05) var(--tw-gradient-to-position);
}
.to-terra {
  --tw-gradient-to: #B8935C var(--tw-gradient-to-position);
}
.to-terra\/10 {
  --tw-gradient-to: rgb(184 147 92 / 0.1) var(--tw-gradient-to-position);
}
.to-terra\/20 {
  --tw-gradient-to: rgb(184 147 92 / 0.2) var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-whisper {
  --tw-gradient-to: #F5F2ED var(--tw-gradient-to-position);
}
.to-whisper\/30 {
  --tw-gradient-to: rgb(245 242 237 / 0.3) var(--tw-gradient-to-position);
}
.to-whisper\/40 {
  --tw-gradient-to: rgb(245 242 237 / 0.4) var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.to-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0.1) var(--tw-gradient-to-position);
}
.to-white\/90 {
  --tw-gradient-to: rgb(255 255 255 / 0.9) var(--tw-gradient-to-position);
}
.to-yellow-50 {
  --tw-gradient-to: #fefce8 var(--tw-gradient-to-position);
}
.bg-\[length\:16px_16px\] {
  background-size: 16px 16px;
}
.bg-\[length\:200\%_100\%\] {
  background-size: 200% 100%;
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.bg-right {
  background-position: right;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.fill-golden {
  fill: #D4AF7A;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.object-center {
  -o-object-position: center;
     object-position: center;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-md {
  padding: 24px;
}
.p-xl {
  padding: 48px;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-hero-padding {
  padding-left: 24px;
  padding-right: 24px;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-36 {
  padding-top: 9rem;
  padding-bottom: 9rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-section {
  padding-top: 120px;
  padding-bottom: 120px;
}
.py-sm {
  padding-top: 16px;
  padding-bottom: 16px;
}
.py-xl {
  padding-top: 48px;
  padding-bottom: 48px;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-24 {
  padding-top: 6rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-48 {
  padding-top: 12rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.pt-lg {
  padding-top: 32px;
}
.pt-md {
  padding-top: 24px;
}
.pt-nav-height {
  padding-top: 80px;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-cormorant {
  font-family: Cormorant Garamond, Didot, Bodoni MT, Playfair Display, serif;
}
.font-inter {
  font-family: Inter, Helvetica Neue, -apple-system, BlinkMacSystemFont, sans-serif;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-playfair {
  font-family: var(--font-playfair), Playfair Display, serif;
}
.font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}
.text-8xl {
  font-size: 6rem;
  line-height: 1;
}
.text-\[11px\] {
  font-size: 11px;
}
.text-\[13px\] {
  font-size: 13px;
}
.text-\[22px\] {
  font-size: 22px;
}
.text-\[28px\] {
  font-size: 28px;
}
.text-\[42px\] {
  font-size: 42px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-body {
  font-size: 1rem;
  line-height: 1.8;
  font-weight: 400;
}
.text-body-lg {
  font-size: 1.0625rem;
  line-height: 1.85;
  font-weight: 400;
}
.text-caption {
  font-size: 0.875rem;
  line-height: 1.6;
  letter-spacing: 0.05em;
  font-weight: 400;
}
.text-display-xl {
  font-size: 3rem;
  line-height: 1.1;
  letter-spacing: 0.05em;
  font-weight: 300;
}
.text-heading {
  font-size: 1.5rem;
  line-height: 1.3;
  letter-spacing: 0.02em;
  font-weight: 400;
}
.text-hero-massive {
  font-size: clamp(100px, 15vw, 200px);
  line-height: 0.95;
  letter-spacing: 0.15em;
  font-weight: 300;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-micro {
  font-size: 0.6875rem;
  line-height: 1.4;
  letter-spacing: 0.1em;
  font-weight: 500;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-small {
  font-size: 0.8125rem;
  line-height: 1.5;
  letter-spacing: 0.02em;
  font-weight: 400;
}
.text-subtitle {
  font-size: 1.3125rem;
  line-height: 1.4;
  letter-spacing: 0.01em;
  font-weight: 300;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extralight {
  font-weight: 200;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.not-italic {
  font-style: normal;
}
.leading-\[0\.85\] {
  line-height: 0.85;
}
.leading-\[0\.95\] {
  line-height: 0.95;
}
.leading-\[0\.9\] {
  line-height: 0.9;
}
.leading-\[1\.85\] {
  line-height: 1.85;
}
.leading-loose {
  line-height: 2;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-\[0\.02em\] {
  letter-spacing: 0.02em;
}
.tracking-\[0\.05em\] {
  letter-spacing: 0.05em;
}
.tracking-\[0\.08em\] {
  letter-spacing: 0.08em;
}
.tracking-\[0\.12em\] {
  letter-spacing: 0.12em;
}
.tracking-\[0\.15em\] {
  letter-spacing: 0.15em;
}
.tracking-\[0\.25em\] {
  letter-spacing: 0.25em;
}
.tracking-\[0\.2em\] {
  letter-spacing: 0.2em;
}
.tracking-\[0\.3em\] {
  letter-spacing: 0.3em;
}
.tracking-\[0\.5px\] {
  letter-spacing: 0.5px;
}
.tracking-\[1\.2px\] {
  letter-spacing: 1.2px;
}
.tracking-\[1\.5px\] {
  letter-spacing: 1.5px;
}
.tracking-\[1px\] {
  letter-spacing: 1px;
}
.tracking-\[2px\] {
  letter-spacing: 2px;
}
.tracking-\[3\.5px\] {
  letter-spacing: 3.5px;
}
.tracking-\[3px\] {
  letter-spacing: 3px;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-\[\#B8956F\] {
  --tw-text-opacity: 1;
  color: rgb(184 149 111 / var(--tw-text-opacity, 1));
}
.text-\[\.\.\.\] {
  color: ...;
}
.text-accent {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}
.text-accent\/10 {
  color: rgb(139 115 85 / 0.1);
}
.text-accent\/30 {
  color: rgb(139 115 85 / 0.3);
}
.text-accent\/60 {
  color: rgb(139 115 85 / 0.6);
}
.text-ash {
  --tw-text-opacity: 1;
  color: rgb(107 101 96 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-charcoal {
  --tw-text-opacity: 1;
  color: rgb(42 39 36 / var(--tw-text-opacity, 1));
}
.text-charcoal-light {
  --tw-text-opacity: 1;
  color: rgb(74 69 63 / var(--tw-text-opacity, 1));
}
.text-charcoal-light\/40 {
  color: rgb(74 69 63 / 0.4);
}
.text-charcoal-light\/60 {
  color: rgb(74 69 63 / 0.6);
}
.text-charcoal-light\/70 {
  color: rgb(74 69 63 / 0.7);
}
.text-charcoal-light\/80 {
  color: rgb(74 69 63 / 0.8);
}
.text-charcoal\/40 {
  color: rgb(42 39 36 / 0.4);
}
.text-charcoal\/50 {
  color: rgb(42 39 36 / 0.5);
}
.text-charcoal\/60 {
  color: rgb(42 39 36 / 0.6);
}
.text-charcoal\/70 {
  color: rgb(42 39 36 / 0.7);
}
.text-charcoal\/80 {
  color: rgb(42 39 36 / 0.8);
}
.text-charcoal\/90 {
  color: rgb(42 39 36 / 0.9);
}
.text-current {
  color: currentColor;
}
.text-enterprise-brown {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}
.text-enterprise-brown\/20 {
  color: rgb(139 115 85 / 0.2);
}
.text-enterprise-brown\/30 {
  color: rgb(139 115 85 / 0.3);
}
.text-enterprise-brown\/40 {
  color: rgb(139 115 85 / 0.4);
}
.text-enterprise-brown\/60 {
  color: rgb(139 115 85 / 0.6);
}
.text-enterprise-brown\/70 {
  color: rgb(139 115 85 / 0.7);
}
.text-enterprise-brown\/80 {
  color: rgb(139 115 85 / 0.8);
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: rgb(42 39 36 / var(--tw-text-opacity, 1));
}
.text-primary\/40 {
  color: rgb(42 39 36 / 0.4);
}
.text-primary\/60 {
  color: rgb(42 39 36 / 0.6);
}
.text-primary\/85 {
  color: rgb(42 39 36 / 0.85);
}
.text-pure-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-sage {
  --tw-text-opacity: 1;
  color: rgb(139 134 128 / var(--tw-text-opacity, 1));
}
.text-sanctuary {
  --tw-text-opacity: 1;
  color: rgb(253 252 248 / var(--tw-text-opacity, 1));
}
.text-sanctuary\/70 {
  color: rgb(253 252 248 / 0.7);
}
.text-sanctuary\/75 {
  color: rgb(253 252 248 / 0.75);
}
.text-sanctuary\/80 {
  color: rgb(253 252 248 / 0.8);
}
.text-sanctuary\/85 {
  color: rgb(253 252 248 / 0.85);
}
.text-sanctuary\/90 {
  color: rgb(253 252 248 / 0.9);
}
.text-sanctuary\/95 {
  color: rgb(253 252 248 / 0.95);
}
.text-sand {
  --tw-text-opacity: 1;
  color: rgb(212 175 122 / var(--tw-text-opacity, 1));
}
.text-secondary {
  --tw-text-opacity: 1;
  color: rgb(107 101 96 / var(--tw-text-opacity, 1));
}
.text-silk {
  --tw-text-opacity: 1;
  color: rgb(240 237 232 / var(--tw-text-opacity, 1));
}
.text-silk\/80 {
  color: rgb(240 237 232 / 0.8);
}
.text-stone {
  --tw-text-opacity: 1;
  color: rgb(168 163 158 / var(--tw-text-opacity, 1));
}
.text-stone-light {
  --tw-text-opacity: 1;
  color: rgb(196 191 184 / var(--tw-text-opacity, 1));
}
.text-stone\/40 {
  color: rgb(168 163 158 / 0.4);
}
.text-stone\/50 {
  color: rgb(168 163 158 / 0.5);
}
.text-stone\/60 {
  color: rgb(168 163 158 / 0.6);
}
.text-terra {
  --tw-text-opacity: 1;
  color: rgb(184 147 92 / var(--tw-text-opacity, 1));
}
.text-warm-gold {
  --tw-text-opacity: 1;
  color: rgb(193 155 104 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.decoration-1 {
  text-decoration-thickness: 1px;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-charcoal\/60::-moz-placeholder {
  color: rgb(42 39 36 / 0.6);
}
.placeholder-charcoal\/60::placeholder {
  color: rgb(42 39 36 / 0.6);
}
.placeholder-sage::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(139 134 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-sage::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(139 134 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-stone\/50::-moz-placeholder {
  color: rgb(168 163 158 / 0.5);
}
.placeholder-stone\/50::placeholder {
  color: rgb(168 163 158 / 0.5);
}
.placeholder-white\/60::-moz-placeholder {
  color: rgb(255 255 255 / 0.6);
}
.placeholder-white\/60::placeholder {
  color: rgb(255 255 255 / 0.6);
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-40 {
  opacity: 0.4;
}
.opacity-5 {
  opacity: 0.05;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.opacity-\[0\.015\] {
  opacity: 0.015;
}
.opacity-\[0\.02\] {
  opacity: 0.02;
}
.opacity-\[0\.03\] {
  opacity: 0.03;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_1px_20px_rgba\(0\2c 0\2c 0\2c 0\.03\)\] {
  --tw-shadow: 0 1px 20px rgba(0,0,0,0.03);
  --tw-shadow-colored: 0 1px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_1px_20px_rgba\(139\2c 115\2c 85\2c 0\.03\)\] {
  --tw-shadow: 0 1px 20px rgba(139,115,85,0.03);
  --tw-shadow-colored: 0 1px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-elegant {
  --tw-shadow: 0 4px 16px rgba(139, 115, 85, 0.08);
  --tw-shadow-colored: 0 4px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-premium-shadow {
  --tw-shadow: 0 8px 32px rgba(139, 115, 85, 0.12);
  --tw-shadow-colored: 0 8px 32px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-subtle {
  --tw-shadow: 0 2px 8px rgba(26, 24, 22, 0.06);
  --tw-shadow-colored: 0 2px 8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-premium-shadow {
  --tw-shadow-color: rgba(139, 115, 85, 0.15);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-sanctuary\/20 {
  --tw-shadow-color: rgb(253 252 248 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-sanctuary\/30 {
  --tw-shadow-color: rgb(253 252 248 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-soft-black\/10 {
  --tw-shadow-color: rgb(26 24 22 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-subtle-shadow {
  --tw-shadow-color: rgba(26, 24, 22, 0.06);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline {
  outline-style: solid;
}
.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-enterprise-brown\/10 {
  --tw-ring-color: rgb(139 115 85 / 0.1);
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-2xl {
  --tw-drop-shadow: drop-shadow(0 25px 25px rgb(0 0 0 / 0.15));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-sm {
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale-\[0\.1\] {
  --tw-grayscale: grayscale(0.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale-\[20\%\] {
  --tw-grayscale: grayscale(20%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-\[10px\] {
  --tw-backdrop-blur: blur(10px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[30px\] {
  --tw-backdrop-blur: blur(30px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-1000 {
  transition-delay: 1000ms;
}
.delay-200 {
  transition-delay: 200ms;
}
.delay-300 {
  transition-delay: 300ms;
}
.delay-500 {
  transition-delay: 500ms;
}
.delay-700 {
  transition-delay: 700ms;
}
.duration-1000 {
  transition-duration: 1000ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.duration-\[10s\] {
  transition-duration: 10s;
}
.duration-\[12s\] {
  transition-duration: 12s;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.will-change-transform {
  will-change: transform;
}
.container-query {
  container-type: inline-size;
}

/* Import main styles with modern CSS features */

/* Import enhanced design system */

/* Import nowych stylów Bakasana */

/* Import ujednoliconego systemu designu */

/* Import color migration system for smooth transition */

/* ===== ACCESSIBILITY - SKIP LINKS ===== */

.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 500;
   /* BAKASANA RULE: Zero border-radius */
  z-index: 1000;
  transition: top var(--duration-quick) var(--ease-premium);
}.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== OLD MONEY ANIMATIONS ===== */
@keyframes fade-in {from {
  transform: translateY(20px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

/* ===== WHATSAPP BUTTON CRITICAL CSS ===== */
.whatsapp-elegant {
  background-color: #8B7355;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}.whatsapp-elegant:hover {
  background-color: rgba(139, 115, 85, 0.9);
  transform: scale(1.05);
}.whatsapp-float {
  position: fixed;
  right: 2.5rem;
  bottom: 2.5rem;
  z-index: 998;
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

@media (max-width: 768px) {
}

/* Ensure WhatsApp button doesn't conflict with cookie banner */
.whatsapp-float {
  margin-bottom: env(safe-area-inset-bottom, 0);
}.whatsapp-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

/* Enhanced WhatsApp button animations */
@keyframes whatsapp-pulse {0%, 100% {
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  transform: scale(1);
}50% {
  box-shadow: 0 12px 35px rgba(34, 197, 94, 0.4);
  transform: scale(1.05);
}
}

@keyframes whatsapp-bounce {0%, 20%, 53%, 80%, 100% {
  transform: translate3d(0, 0, 0);
}40%, 43% {
  transform: translate3d(0, -8px, 0);
}70% {
  transform: translate3d(0, -4px, 0);
}90% {
  transform: translate3d(0, -2px, 0);
}
}.whatsapp-enhanced {
  animation: whatsapp-pulse 3s ease-in-out infinite;
}.whatsapp-enhanced:hover {
  animation: whatsapp-bounce 1s ease-in-out;
}

/* =============================================
   🏛️ BAKASANA - ENTERPRISE 11/10 DESIGN SYSTEM
   Ultra-minimalist perfection meets spiritual elegance
   ============================================= */

/* OPTIMIZED FONT LOADING - Fonts loaded via Next.js font optimization system */
/* @import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400&family=Inter:wght@300;400&display=swap'); */

:root {
  /* ===== MASTER GRID & SPACING SYSTEM - ULTRA BREATHING ===== */
  --container-max: 1200px;
  --section-padding: 120px 0;      /* BAKASANA STANDARD: 120px section padding */
  --element-breathing: 8%;          /* BAKASANA STANDARD: 8% side margins */
  --card-internal: 60px;            /* Increased from 48px */
  --micro-spacing: 32px;            /* Increased from 24px */
  --nano-spacing: 12px;             /* Increased from 8px */
  --hero-spacing: 120px;            /* New variable for hero section spacing */
  --whisper-spacing: 180px;         /* New variable for ultra-minimal sections */

  /* ===== RESPONSIVE CONTAINER PADDING SYSTEM ===== */
  --container-padding-mobile: clamp(1rem, 4vw, 1.5rem);    /* 16px-24px */
  --container-padding-tablet: clamp(1.5rem, 5vw, 3rem);    /* 24px-48px */
  --container-padding-desktop: clamp(2rem, 6vw, 4rem);     /* 32px-64px */
  --container-padding-large: clamp(3rem, 7vw, 5rem);       /* 48px-80px */
  --container-padding-ultra: clamp(4rem, 8vw, 6rem);       /* 64px-96px */

  /* ===== RESPONSIVE BREAKPOINTS ===== */
  --mobile: 375px;
  --tablet: 768px;
  --desktop: 1024px;
  --large: 1440px;
  --ultra: 1920px;

  /* ===== SIMPLIFIED COLOR PALETTE - WARM MINIMALISM ===== */
  /* Core neutral colors - warm and human */
  --sanctuary: #FDFCF8;         /* Main background - warm cream */
  --charcoal: #2A2724;          /* Main text - warm dark brown */
  --stone: #8B8680;             /* Secondary text - warm stone */
  --whisper: #F9F7F3;           /* Subtle backgrounds */
  --rice: #FAF8F4;              /* Very subtle background for headers */
  
  /* Single spiritual accent */
  --temple-gold: #B8935C;       /* Primary accent - muted gold */
  --golden-amber: #D4AF37;      /* Rich golden amber for gradients */
  
  /* Subtle black & white accents */
  --pure-white: #FFFFFF;        /* Clean white for contrast */
  --soft-black: #1A1816;        /* Soft black for emphasis */
  --charcoal-light: #4A453F;    /* Lighter charcoal */
  --stone-light: #B5B0A8;       /* Lighter stone */
  
  /* Nature touches - very subtle */
  --sage-whisper: #A8B5A8;      /* Barely visible green */
  --ocean-mist: #B8C5D1;        /* Barely visible blue */

  /* Transparencies */
  --glass-nav: rgba(253, 252, 248, 0.95);
  --subtle-shadow: rgba(26, 24, 22, 0.04);
  --hover-overlay: rgba(26, 24, 22, 0.08);

  /* ===== TYPOGRAPHY SCALE - WARM MINIMAL HIERARCHY ===== */
  /* Font families - z ciepłem i ludzkością */
  --font-primary: 'Cormorant Garamond', 'Crimson Text', serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-accent: 'Crimson Text', 'Cormorant Garamond', serif;

  /* Typography scale - Expanded for whisper aestheticies */
  --text-xs: 10px;              /* Ultra-small for whisper elements */
  --text-sm: 12px;              /* Small details */
  --text-base: 14px;            /* Body text - smaller for elegance */
  --text-lg: 16px;              /* Slightly larger body */
  --text-xl: 20px;              /* Subtitles */
  --text-2xl: 28px;             /* Section headers */
  --text-3xl: 36px;             /* Page headers */
  --text-4xl: 42px;             /* Large headers */
  --text-5xl: 56px;             /* Hero subtitles */
  --text-6xl: 72px;             /* Standard hero */
  --text-7xl: 96px;             /* Large hero */
  --text-8xl: 140px;            /* Ultra hero - BAKASANA title */

  /* Ultra-light font weights for whisper aesthetic */
  --font-ultra-light: 100;
  --font-whisper: 200;          /* Primary weight for hero */
  --font-light: 300;
  --font-normal: 400;

  /* Opacity levels for whisper effect */
  --opacity-whisper: 0.4;       /* Ultra-subtle elements */
  --opacity-subtle: 0.6;        /* Subtle elements */
  --opacity-soft: 0.7;          /* Soft hover states */
  --opacity-visible: 0.85;      /* Main hero title */

  /* Aliasy dla kompatybilności */
  --primary: var(--charcoal);
  --secondary: var(--stone);
  --background: var(--sanctuary);
  --accent: var(--temple-gold);
}

/* ===== BAKASANA RECTANGULAR DESIGN SYSTEM ===== */
/* Perfect zero-radius components for ultra-minimalist aesthetic */

.rectangular {
  
  box-shadow: 0 1px 3px rgba(26, 24, 22, 0.04), 0 1px 2px rgba(26, 24, 22, 0.06);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}.rectangular:hover {
  box-shadow: 0 4px 6px rgba(26, 24, 22, 0.07), 0 2px 4px rgba(26, 24, 22, 0.06);
  transform: translateY(-1px);
}.rectangular-subtle {
  box-shadow: 0 1px 2px rgba(26, 24, 22, 0.03);
}.rectangular-elevated {
  box-shadow: 0 10px 25px rgba(26, 24, 22, 0.08), 0 4px 6px rgba(26, 24, 22, 0.04);
}

/* Rectangular buttons with minimalist elegance */
.rectangular-button {
  
  border: 1px solid var(--charcoal);
  background: transparent;
  padding: 0.75rem 2rem;
  font-family: var(--font-secondary);
  font-size: 0.75rem;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  transition: all 0.3s ease;
}.rectangular-button:hover {
  transform: translateY(-1px);
  opacity: 0.7;
}.rectangular-button:focus {
  box-shadow: 0 0 0 2px var(--temple-gold);
  outline: none;
}

/* Rectangular form controls */
.rectangular-input {
  
  border: 1px solid var(--stone-light);
  background: var(--sanctuary);
  padding: 0.75rem 1rem;
  font-family: var(--font-secondary);
  font-size: 0.875rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}.rectangular-input:focus {
  border-color: var(--temple-gold);
  box-shadow: 0 0 0 2px rgba(184, 147, 92, 0.1);
  outline: none;
}.rectangular-input:error {
  border-color: var(--temple-gold);
}

/* Rectangular cards with perfect spacing */
.rectangular-card {
  
  background: var(--sanctuary);
  border: 1px solid var(--stone-light);
  padding: 2rem;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}.rectangular-card:hover {
  box-shadow: 0 4px 12px rgba(26, 24, 22, 0.08);
  transform: translateY(-2px);
}

/* ===== GLOBAL RULES FOR ULTRA-MINIMALISM ===== */
* {
  box-sizing: border-box;
   /* Zero rounded corners globally - BAKASANA RULE */
}

/* ENFORCE ZERO BORDER-RADIUS ON ALL ELEMENTS */
*,
*::before,
*::after,
button,
input,
textarea,
select,
.btn,
.button,
.card,
.badge,
.rectangular,
.rectangular-subtle,
.elegant-border {
  
}html {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-kerning: normal;
  font-variant-ligatures: common-ligatures;
  scroll-behavior: smooth;
  text-rendering: optimizeLegibility;
}

/* ===== ENTERPRISE HERO ENHANCEMENTS ===== */

/* Performance optimization for animated elements */
.hero-element {
  will-change: transform, opacity;
  contain: layout style paint;
}

/* Smooth cursor transitions */
* {
  cursor: default;
}a, button, [role="button"], input[type="submit"] {
  cursor: pointer;
}

/* Focus states without outline but with opacity */
button:focus,
a:focus,
input:focus {
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

/* Enterprise-level selection styling */
::-moz-selection {
  background-color: #8B7355;
  color: white;
}
::selection {
  background-color: #8B7355;
  color: white;
}::-moz-selection {
  background-color: #8B7355;
  color: white;
}

/* Subtle animations for staggered loading */
@keyframes enterpriseFadeIn {
}.enterprise-fade {
  animation: enterpriseFadeIn 1s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

/* Golden ratio spacing utilities */
.golden-spacing {
  margin-bottom: calc(1.618 * 1rem);
}.golden-padding {
  padding: calc(1.618 * 1rem);
}body {
  overflow-x: hidden;
  background: var(--sanctuary);
  color: var(--charcoal);
  font-family: 'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  /* Keep this to prevent horizontal scroll */
  /* Ensure smooth scrolling works properly */
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

/* ===== PRECISE TYPOGRAPHY HIERARCHY ===== */

/* LOGO */
.logo {
  font-family: 'Cormorant Garamond', serif;
  font-size: 22px;
  font-weight: 400;
  letter-spacing: 0.5px;
  color: var(--charcoal);
}

/* HERO TITLE - WARM MINIMAL WHISPER */
.hero-title {
  font-family: var(--font-primary);
  font-size: clamp(72px, 10vw, var(--text-8xl)); /* 140px on desktop */
  font-weight: var(--font-whisper);               /* Ultra-light 200 */
  letter-spacing: 0.22em;                         /* Slightly less spacing - more human */
  line-height: 1.05;                              /* Slightly looser for warmth */
  color: var(--charcoal);
  opacity: var(--opacity-visible);                /* 0.85 - barely visible effect */
  position: relative;
  top: -16px;                                     /* Slightly lower - more grounded */
  animation: fadeInUp 1.2s ease-out;
  text-shadow: 0 0 40px rgba(44, 41, 40, 0.03);  /* Warmer glow */
}

/* SECTION HEADERS */
.section-header {
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(36px, 5vw, 56px);
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.3;
  color: var(--charcoal);
}

/* CARD TITLES */
.card-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 28px;
  font-weight: 400;
  letter-spacing: 0.05em;
  line-height: 1.4;
  color: var(--charcoal);
}

/* BODY TEXT */
.body-text {
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  letter-spacing: 0.3px;
  line-height: 1.8;
  color: var(--charcoal);
}

/* NAVIGATION LINKS */
.nav-link {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 300;
  letter-spacing: 0.3px;
  text-transform: none;
  color: var(--charcoal);
}

/* SUBTLE TEXT */
.subtle-text {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  color: var(--stone);
}

/* Standard HTML elements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 300;
  letter-spacing: 0.05em;
  line-height: 1.3;
  color: var(--charcoal);
  margin-bottom: 1.5rem;
}h1 {
  font-size: clamp(64px, 8vw, 128px);
  line-height: 1.1;
  letter-spacing: 0.15em;
}h2 {
  font-size: clamp(36px, 5vw, 56px);
  letter-spacing: 0.08em;
}h3 {
  font-size: 28px;
  letter-spacing: 0.05em;
}p {
  margin-bottom: 1.5rem;
  color: var(--charcoal);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  line-height: 1.8;
  letter-spacing: 0.3px;
}

/* ===== PROFESSIONAL NAVBAR - OLD MONEY STYLE ===== */
/* Nowy navbar używa Tailwind CSS - wszystkie style są w komponencie React */

/* ===== SEKCJA ZAJĘCIA ONLINE - NOWA SEKCJA ===== */
.online-section {
  background: linear-gradient(135deg, var(--whisper) 0%, var(--sanctuary) 100%);
  padding: 100px 0;
  text-align: center;
}.online-title {
  margin-bottom: 20px;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 200;
  letter-spacing: 0.05em;
}.mobile-menu-button:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}.hamburger-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 18px;
}

/* Enhanced hamburger lines */
.hamburger-line {
  width: 24px;
  height: 2px;
  background: var(--charcoal);
  
  transition: all var(--duration-quick) var(--ease-premium);
  transform-origin: center;
  position: absolute;
}.hamburger-line:nth-child(1) {
  top: 0;
}.hamburger-line:nth-child(2) {
  top: 8px;
}.hamburger-line:nth-child(3) {
  top: 16px;
}.hamburger-line.open:nth-child(1) {
  top: 8px;
  transform: rotate(45deg);
}.hamburger-line.open:nth-child(2) {
  transform: scale(0);
  opacity: 0;
}.hamburger-line.open:nth-child(3) {
  top: 8px;
  transform: rotate(-45deg);
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  z-index: 998;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

/* Mobile menu - Enterprise */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: min(400px, 90vw);
  height: 100vh;
  background: white;
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
}.mobile-menu.open {
  right: 0;
}.mobile-menu-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 80px 30px 40px;
  gap: 20px;
}

/* Mobile navigation links */
.mobile-nav-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}.mobile-nav-item {
  transform: translateX(30px);
  animation: slideInMobile 0.4s ease-out forwards;
  opacity: 0;
}.mobile-nav-item:nth-child(1) {
  animation-delay: 0.1s;
}.mobile-nav-item:nth-child(2) {
  animation-delay: 0.15s;
}.mobile-nav-item:nth-child(3) {
  animation-delay: 0.2s;
}.mobile-nav-item:nth-child(4) {
  animation-delay: 0.25s;
}.mobile-nav-item:nth-child(5) {
  animation-delay: 0.3s;
}.mobile-nav-item:nth-child(6) {
  animation-delay: 0.35s;
}.mobile-nav-link {
  position: relative;
  display: block;
  padding: 16px 20px;
  border: 1px solid transparent;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 18px;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}.mobile-nav-link:hover {
  border-color: rgba(212, 175, 55, 0.2);
  background: var(--whisper);
  color: var(--temple-gold);
  transform: translateX(4px);
}.mobile-nav-link:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}.mobile-nav-link.active {
  border-color: rgba(212, 175, 55, 0.3);
  background: rgba(212, 175, 55, 0.1);
  color: var(--temple-gold);
  font-weight: 500;
}.mobile-nav-link.online-classes {
  background: linear-gradient(135deg, var(--temple-gold), var(--golden-amber));
  color: white;
  font-weight: 500;
}.mobile-nav-link.online-classes:hover {
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
  transform: translateX(4px) scale(1.02);
}

/* Mobile dropdown */
.mobile-dropdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}.mobile-dropdown-content {
  margin-left: 20px;
  padding-left: 20px;
  border-left: 2px solid var(--whisper);
}.mobile-dropdown-section {
  margin-bottom: 12px;
}.mobile-dropdown-header {
  margin-bottom: 8px;
  padding-left: 20px;
  color: var(--stone);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.mobile-nav-link.sub-link {
  margin-left: 0;
  padding-left: 20px;
  color: var(--stone);
  font-size: 16px;
}.mobile-nav-link.sub-link:hover {
  color: var(--temple-gold);
}

/* ===== ANIMATIONS - ENTERPRISE LEVEL ===== */

@keyframes fadeIn {
}

@keyframes slideInMobile {
}

@keyframes dropdownSlide {
}

/* ===== RESPONSIVE NAVIGATION ===== */

@media (max-width: 768px) {.navigation {
  padding: 16px 4%;
}.navigation.scrolled {
  padding: 12px 4%;
}.logo {
  font-size: 20px;
}
}

@media (max-width: 480px) {.mobile-menu {
  right: -100vw;
  width: 100vw;
}
}.mobile-nav-link:hover,
.mobile-nav-link:focus {
  opacity: 0.6;
}

/* Hide mobile menu on desktop */
@media (min-width: 768px) {.mobile-menu-button {
  display: none;
}
}

/* Hamburger menu dla mobile */
@media (max-width: 768px) {.nav-menu {
  position: fixed;
  top: 0;
  left: -100%;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: var(--sanctuary);
  transition: 0.3s;
  gap: 32px;
}.nav-menu.active {
  left: 0;
}.nav-link {
  font-family: 'Cormorant Garamond', serif;
  font-size: 24px;
}.dropdown-menu {
  position: static;
  visibility: visible;
  margin-top: 0;
  border: none;
  background: transparent;
  box-shadow: none;
  transform: none;
  opacity: 1;
}.dropdown-item {
  padding: 8px 0;
  border-bottom: none;
  font-size: 18px;
}.dropdown-header {
  padding: 16px 0 8px;
  background: transparent;
  color: var(--temple-gold);
  font-size: 14px;
}
}

/* ===== SEKCJA ZAJĘCIA ONLINE - NOWA SEKCJA ===== */
.online-section {
  background: linear-gradient(135deg, var(--whisper) 0%, var(--sanctuary) 100%);
  padding: 100px 0;
  text-align: center;
}.online-subtitle {
  margin-bottom: 60px;
  color: var(--temple-gold);
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  font-style: italic;
}.online-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  max-width: 900px;
  margin: 0 auto;
  gap: 40px;
}.online-feature {
  text-align: center;
}.feature-icon {
  margin-bottom: 16px;
  color: var(--temple-gold);
  font-size: 36px;
}.feature-title {
  margin-bottom: 8px;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 20px;
  font-weight: 400;
}.feature-description {
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  line-height: 1.6;
}

@media (max-width: 768px) {
}

/* Links - Warm minimal hover effects */
a {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}a:hover {
  opacity: 0.75;
  /* Slightly more visible on hover */
  transform: translateY(-0.5px);
}img {
  display: block;
  width: 100%;
  max-width: 100%;
  height: auto;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(0.96) contrast(1.04) saturate(1.02);
  image-rendering: optimize-contrast;
}img:hover {
  transform: scale(1.02);
  filter: brightness(1.02) contrast(1.08) saturate(1.05);
}

/* Specjalne stylowanie dla obrazów w kartach */
.destination-card img,
.retreat-card img,
.blog-card img {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}.destination-card:hover img,
.retreat-card:hover img,
.blog-card:hover img {
  transform: scale(1.05);
  filter: brightness(1.04) contrast(1.12) saturate(1.08);
}

/* ===== HERO SECTION - ULTRA-MINIMAL PERFECTION ===== */
.hero {
  height: 100vh;
  min-height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: linear-gradient(
    135deg,
    var(--sanctuary) 0%,
    var(--human-warmth) 30%,
    var(--rice) 70%,
    var(--whisper) 100%
  );
  position: relative;
  padding: 0 var(--element-breathing);
  overflow: hidden;
}.hero::before {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(193, 155, 104, 0.03) 0%,
    transparent 50%
  );
  animation: gentleRotate 120s linear infinite;
  content: '';
  pointer-events: none;
}

@keyframes gentleRotate {0% {
  transform: rotate(0deg);
}100% {
  transform: rotate(360deg);
}
}.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1000px;
  padding: 0;
  animation: fadeInUp 1.5s ease-out;
}

/* Hero subtitle - whisper effect */
.hero-subtitle {
  font-family: var(--font-secondary);
  font-size: var(--text-base);                   /* 14px - slightly larger */
  font-weight: var(--font-light);                /* 300 weight */
  color: var(--stone);
  opacity: var(--opacity-subtle);                /* 0.6 opacity */
  margin: var(--micro-spacing) 0 0 0;            /* 32px top spacing */
  letter-spacing: 0.1em;
  text-transform: none;
}

/* Hero quote - poetic accent */
.hero-quote {
  font-family: var(--font-primary);
  font-size: 16px;
  font-weight: var(--font-light);               /* 300 weight */
  color: var(--stone);
  opacity: var(--opacity-soft);                 /* 0.7 opacity */
  font-style: italic;
  letter-spacing: 0.05em;
  margin: var(--micro-spacing) 0;               /* 32px spacing */
  animation: fadeInDelayed 1.8s ease-out forwards;
}

/* Hero meta - locations at bottom */
.hero-meta {
  font-family: var(--font-secondary);
  font-size: 11px;                               /* Even smaller */
  font-weight: var(--font-whisper);              /* 200 weight */
  color: var(--stone);
  opacity: var(--opacity-whisper);               /* 0.4 - barely visible */
  letter-spacing: 0.3em;
  text-transform: none;
  margin: var(--micro-spacing) 0 0 0;            /* 32px top spacing */
  animation: fadeInDelayed 2s ease-out forwards;
  opacity: 0;                                     /* Start invisible */
}

/* Hero CTA - Ultra-minimal call to action */
.hero-cta {
  margin: var(--hero-spacing) 0 0 0;             /* 120px top spacing */
  animation: fadeInDelayed 2.5s ease-out forwards;
  opacity: 0;                                     /* Start invisible */
}.hero-cta-link {
  border-bottom: 1px solid transparent;
  font-family: var(--font-secondary);
  font-size: var(--text-sm);
  text-decoration: none;
  text-transform: none;
  transition: all var(--duration-quick) var(--ease-premium);
  opacity: 0.7;
  /* 12px */
  font-weight: var(--font-light);
  /* 300 weight */
  color: var(--temple-gold);
  letter-spacing: 0.2em;
}.hero-cta-link:hover {
  opacity: 1;
  /* BAKASANA RULE: Only opacity changes on hover */;
}

/* Hero quote - osobisty akcent */
.hero-quote {
  font-family: 'Cormorant Garamond', serif;
  font-size: 18px;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20px;
  letter-spacing: 0.05em;
}

/* ===== ENHANCED ANIMATIONS FOR ENTERPRISE FEEL ===== */

/* Fade-in animation for locations - more organic */
@keyframes fadeInDelayed {
}

/* Enhanced fade-in for main elements */
@keyframes fadeInUp {
}

/* Subtle breathing animation for spiritual elements */
@keyframes breathe {
}

/* Warm glow animation for interactive elements */
@keyframes warmGlow {
}

/* Elegant slide-in for cards */
@keyframes slideInRight {
}

/* Smooth scale for hover effects */
@keyframes smoothScale {
}

/* Gentle pulse for spiritual elements */
@keyframes gentlePulse {
}

/* Organic float for floating elements */
@keyframes organicFloat {
}.subtle-link {
  border-bottom: 1px solid transparent;
  color: var(--stone);
  font-size: 12px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 300ms ease;
  letter-spacing: 0.5px;
}.subtle-link:hover {
  opacity: 0.7;
  /* BAKASANA RULE: Only opacity changes on hover */;
}

/* ===== LAYOUT SYSTEM - Przestrzeń jako Luksus ===== */
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 120px 8%; /* BAKASANA STANDARD: 120px vertical, 8% horizontal */
}.section {
  overflow: hidden;
  padding: var(--section-padding);
  /* BAKASANA STANDARD: 120px between sections */
  background: transparent;
  /* Zero-container philosophy */
  position: relative;
}.section-breathe {
  padding: 180px 15%;
}

/* ===== PŁYNNE PRZEJŚCIA MIĘDZY SEKCJAMI ===== */
.section-smooth {
  position: relative;
  z-index: 1;
}.section-smooth::before {
  position: absolute;
  top: -60px;
  right: 0;
  left: 0;
  z-index: -1;
  height: 120px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  content: '';
  pointer-events: none;
}.section-smooth::after {
  position: absolute;
  right: 0;
  bottom: -60px;
  left: 0;
  z-index: -1;
  height: 120px;
  background: linear-gradient(
    to top,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  content: '';
  pointer-events: none;
}

/* Warianty dla różnych sekcji */
.section-warm {
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    var(--human-warmth) 30%,
    var(--rice) 100%
  );
}.section-spiritual {
  background: linear-gradient(
    180deg,
    var(--rice) 0%,
    var(--whisper) 50%,
    var(--sanctuary) 100%
  );
}.section-testimonials {
  background: linear-gradient(
    135deg,
    var(--warm-black) 0%,
    var(--warm-black-gradient) 100%
  );
  color: var(--sanctuary);
}

/* ===== DESTINATION CARDS - POETYCKIE KARTY ===== */
.destinations {
  padding: var(--section-padding);
  background: var(--sanctuary);
  position: relative;
}.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 8%;
  gap: 60px;
}.destination-card {
  position: relative;
  overflow: hidden;
  border: none;
  background: rgba(255, 255, 255, 0.6);
  box-shadow: 0 1px 3px rgba(44, 41, 40, 0.02),
    0 4px 12px rgba(44, 41, 40, 0.04);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}.destination-card::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(
    135deg,
    rgba(253, 249, 243, 0.8) 0%,
    rgba(249, 246, 242, 0.9) 100%
  );
  transition: opacity 0.4s ease;
  opacity: 0;
  content: '';
}.destination-card:hover {
  box-shadow: 0 4px 16px rgba(44, 41, 40, 0.06),
    0 12px 32px rgba(44, 41, 40, 0.08);
  transform: translateY(-8px);
}.destination-card:hover::before {
  opacity: 1;
}.card-image {
  position: relative;
  background-size: cover;
  background-position: center;
  /* zamiast grayscale */
  transition: all 600ms ease;
  aspect-ratio: 4/3;
  filter: brightness(0.95) contrast(1.05);
}

/* Hover effects for destination images */
.destination-image {
  filter: brightness(0.95) contrast(1.05); /* zamiast grayscale */
}.destination-image:hover {
  filter: brightness(1) contrast(1.1);
}

/* Hidden overlay with details */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 400ms ease;
  padding: 32px;
  text-align: center;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.1em;
}.destination-card:hover .card-overlay {
  opacity: 1;
}.card-content {
  padding: 48px 32px;
  text-align: center;
}.card-meta {
  margin-bottom: 16px;
  color: var(--stone);
  font-size: 11px;
  font-weight: 300;
  text-transform: uppercase;
  opacity: 0;
  /* Hidden initially */
  transition: opacity 300ms ease;
  letter-spacing: 1px;
}.destination-card:hover .card-meta {
  opacity: 1;
}.card-description {
  max-width: 400px;
  margin: 24px 0 32px 0;
  margin-right: auto;
  margin-left: auto;
  opacity: 0;
  /* Hidden initially */
  transition: opacity 300ms ease;
}.destination-card:hover .card-description {
  opacity: 0.8;
}.card-details {
  position: absolute;
  right: 16px;
  bottom: 16px;
  color: var(--stone);
  font-size: 11px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  opacity: var(--opacity-whisper);
  /* Ultra-subtle */
  transition: opacity 300ms ease;
  letter-spacing: 0.5px;
}.card-details:hover {
  opacity: var(--opacity-soft);
}

/* ===== GHOST BUTTONS - DUCHOWA PROSTOTA Z CIEPŁEM I LUDZKOŚCIĄ ===== */
.btn-ghost {
  padding: 18px 54px;
  border: 1px solid var(--stone);
  background: transparent;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}.btn-ghost::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(193, 155, 104, 0.1),
    transparent
  );
  transition: left 0.8s ease;
  content: '';
}.btn-ghost:hover::before {
  left: 100%;
}.btn-ghost:hover {
  border-color: var(--temple-gold);
  box-shadow: 0 4px 12px rgba(193, 155, 104, 0.1),
    0 8px 24px rgba(193, 155, 104, 0.05);
  color: var(--temple-gold);
  transform: translateY(-2px);
}

/* Enhanced breathing animation for buttons */
@keyframes breathe {
}

/* Primary button variant */
.btn-primary {
  color: var(--temple-gold);
  border-color: var(--temple-gold);
  background: rgba(193, 155, 104, 0.05);
}.btn-primary:hover {
  background: rgba(193, 155, 104, 0.1);
  box-shadow: 0 6px 16px rgba(193, 155, 104, 0.15),
    0 12px 32px rgba(193, 155, 104, 0.08);
  transform: translateY(-2px);
}

/* Accent button variant */
.btn-accent {
  border-color: var(--temple-gold);
  color: var(--temple-gold);
  background: rgba(193, 155, 104, 0.03);
}.btn-accent:hover {
  background: rgba(193, 155, 104, 0.08);
  transform: translateY(-2px);
}

/* ===== SECTION DIVIDERS - SACRED GEOMETRY ===== */
.section-divider {
  width: 120px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(193, 155, 104, 0.3) 15%,
    rgba(193, 155, 104, 0.6) 35%,
    rgba(193, 155, 104, 0.8) 50%,
    rgba(193, 155, 104, 0.6) 65%,
    rgba(193, 155, 104, 0.3) 85%,
    transparent 100%
  );
  margin: 80px auto;
  position: relative;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}.section-divider:hover {
  opacity: 1;
}

/* Bardzo subtelny element w centrum */
.section-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 2px;
  background: var(--temple-gold);
  opacity: 0.8;
  transform: translate(-50%, -50%);
  animation: gentlePulse 3s ease-in-out infinite;
}

@keyframes gentlePulse {
}.btn-accent {
  border-color: var(--temple-gold);
  color: var(--temple-gold);
}

/* ===== ENHANCED ABOUT JULIA SECTION - ENTERPRISE AUTHENTICITY ===== */
.about-julia-section {
  background: linear-gradient(180deg, var(--human-warmth) 0%, var(--sanctuary-variant) 100%);
  position: relative;
  overflow: hidden;
  padding: 140px 0;
}

/* Subtelny wzór w tle */
.about-julia-section::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -10%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(201, 165, 117, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

/* Enhanced Divider */
.section-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 80px 0;
  position: relative;
}.divider-line {
  width: 120px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #C9A575, transparent);
}.divider-symbol {
  margin: 0 30px;
  color: #C9A575;
  font-size: 24px;
  opacity: 0.6;
}

/* Asymetryczny layout */
.about-julia-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 100px;
  align-items: center;
  position: relative;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 8%;
}

/* Zdjęcie wystaje poza sekcję */
.julia-image-wrapper {
  position: relative;
  margin-top: -60px;
  margin-bottom: -60px;
}.julia-image {
  box-shadow: 0 40px 80px rgba(0,0,0,0.1);
  transition: all 500ms ease;
  filter: contrast(1.1) brightness(0.98);
}.julia-image:hover {
  box-shadow: 0 50px 100px rgba(0,0,0,0.15);
  filter: contrast(1.15) brightness(1);
}

/* Typografia dopasowana do hero */
.about-julia-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 72px;
  font-weight: 200;
  letter-spacing: 0.1em;
  color: #3A3A3A;
  margin-bottom: 40px;
  line-height: 1.2;
}

/* Złota linia pod tytułem */
.title-accent {
  width: 60px;
  height: 2px;
  background: #C9A575;
  margin: 20px 0 40px 0;
}

/* Cytat w złotej ramce */
.julia-quote {
  border-left: 3px solid #C9A575;
  padding-left: 30px;
  margin: 40px 0;
  font-size: 20px;
  line-height: 1.8;
  color: #C9A575;
  font-style: italic;
  font-family: 'Cormorant Garamond', serif;
}

/* Statystyki w eleganckich boxach */
.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 60px;
}.stat-box {
  padding: 30px 20px;
  background: rgba(255,255,255,0.5);
  text-align: center;
  transition: all 0.3s ease;
  /* BAKASANA RULE: Zero border-radius */;
  -webkit-backdrop-filter: blur(10px);;
          backdrop-filter: blur(10px);
}.stat-box:hover {
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  transform: translateY(-5px);
}

/* Responsive dla sekcji About Julia */
@media (max-width: 1024px) {.about-julia-content {
  grid-template-columns: 1fr;
  gap: 60px;
}.julia-image-wrapper {
  margin-top: 0;
  margin-bottom: 0;
}.about-julia-title {
  font-size: 48px;
}.stats-container {
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
}

@media (max-width: 768px) {.julia-quote {
  font-size: 18px;
}
}

/* ===== UTILITY CLASSES FOR FONT FAMILIES ===== */
.font-primary {
  font-family: var(--font-primary);
}.font-secondary {
  font-family: var(--font-secondary);
}.font-accent {
  font-family: var(--font-accent);
}

/* ===== FOOTER - MINIMALIZM ===== */
.footer {
  background: var(--whisper);
  padding: 80px 8% 40px 8%;
  text-align: center;
}.footer-content {
  max-width: 600px;
  margin: 0 auto;
}.spiritual-greeting {
  margin-bottom: 48px;
  color: var(--temple-gold);
  font-family: 'Cormorant Garamond', serif;
  font-size: 18px;
  font-weight: 300;
  letter-spacing: 1px;
}.footer-links {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
  gap: 32px;
}.footer-link {
  color: var(--stone);
  font-size: 11px;
  font-weight: 300;
  text-decoration: none;
  text-transform: uppercase;
  transition: opacity 300ms ease;
  letter-spacing: 0.5px;
}.footer-link:hover {
  opacity: 0.6;
}.social-links {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
  gap: 24px;
}.social-icon {
  width: 20px;
  height: 20px;
  transition: opacity 300ms ease;
  opacity: 0.4;
}.social-icon:hover {
  opacity: 0.8;
}.copyright {
  color: var(--stone);
  font-size: 10px;
  font-weight: 300;
  opacity: 0.5;
  letter-spacing: 0.3px;
}

/* ===== TESTIMONIALS - CIEPLEJSZE ===== */
.testimonial-card {
  background: rgba(255, 255, 255, 0.05); /* bardzo subtelne dla ciemnego tła */
  border-left: 2px solid #C9A575; /* złoty akcent z boku */
  padding: 24px;
   /* zachowaj sharp edges */
}

/* ===== JULIA SIGNATURE - OSOBISTY DOTYK ===== */
.julia-signature {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 40px 0;
}.signature-line {
  width: 40px;
  height: 1px;
  background: #C9A575;
  opacity: 0.5;
}.signature-text {
  color: #C9A575;
  font-family: 'Cormorant Garamond', serif;
  font-size: 32px;
  font-weight: 300;
  font-style: italic;
}

/* ===== ENHANCED TESTIMONIALS SECTION ===== */
.testimonials-section {
  background: var(--warm-black);
  padding: 120px 0;
  position: relative;
}.testimonials-section .section-divider {
  margin: 80px 0;
}.testimonials-section .divider-line {
  background: linear-gradient(90deg, transparent, #C9A575, transparent);
}.testimonials-section .divider-symbol {
  color: #C9A575;
}

/* ===== ENHANCED CONTACT SECTION ===== */
.contact-section {
  background: linear-gradient(180deg, #FAF5F0 0%, #FDF9F3 100%);
  padding: 120px 0;
  position: relative;
}.contact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  max-width: 1000px;
  margin: 60px auto;
  padding: 0 8%;
  gap: 30px;
}.contact-card {
  display: block;
  padding: 40px 30px;
  border: 1px solid rgba(201, 165, 117, 0.1);
  background: rgba(255, 255, 255, 0.7);
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}.contact-card:hover {
  border-color: rgba(201, 165, 117, 0.3);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

/* ===== ENHANCED BLOG SECTION ===== */
.blog-section {
  background: linear-gradient(180deg, #FDF9F3 0%, #FAF5F0 100%);
  padding: 120px 0;
  position: relative;
}.blog-section::before {
  position: absolute;
  top: -50%;
  left: -10%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(201, 165, 117, 0.02) 0%, transparent 70%);
  content: '';
  pointer-events: none;
}.blog-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  max-width: 1200px;
  margin: 60px auto;
  padding: 0 8%;
  gap: 40px;
}.blog-card {
  padding: 30px;
  border: 1px solid rgba(201, 165, 117, 0.1);
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}.blog-card:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 25px 50px rgba(0,0,0,0.1);
  transform: translateY(-8px);
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
}

/* ===== ONLINE CTA PULSOWANIE ===== */
@keyframes subtle-pulse {
}.online-cta {
  animation: subtle-pulse 3s ease-in-out infinite;
}

/* ===== ONLINE CLASSES CARD ===== */
.online-classes-card {
  background: linear-gradient(135deg, #FFFFFF 0%, #FAF8F4 100%);
  border: 1px solid #E8E8E8;
}

/* ===== BAKASANA UTILITY CLASSES ===== */

/* Typography utilities */
.font-cormorant { font-family: var(--font-primary); }.font-inter {
  font-family: var(--font-secondary);
}.letter-spacing-wide {
  letter-spacing: 0.1em;
}.letter-spacing-wider {
  letter-spacing: 0.2em;
}

/* Color utilities */
.text-sanctuary { color: var(--sanctuary); }.text-charcoal {
  color: var(--charcoal);
}.text-stone {
  color: var(--stone);
}.text-charcoal-gold {
  color: var(--temple-gold);
}.text-sage-green {
  color: var(--sage-green);
}.bg-sanctuary {
  background-color: var(--sanctuary);
}.bg-charcoal {
  background-color: var(--charcoal);
}.bg-whisper {
  background-color: var(--whisper);
}

/* ===== UNIFIED CONTAINER SYSTEM ===== */
.container-responsive {
  max-width: var(--container-max);
  margin: 0 auto;
  padding-left: var(--container-padding-mobile);
  padding-right: var(--container-padding-mobile);
}

@media (min-width: 768px) {.container-responsive {
  padding-right: var(--container-padding-tablet);
  padding-left: var(--container-padding-tablet);
}
}

@media (min-width: 1024px) {
}

@media (min-width: 1440px) {
}

@media (min-width: 1920px) {
}

/* Spacing utilities */
.section-padding { padding: var(--section-padding); }.container-padding {
  padding: 0 var(--element-breathing);
}.breathe-spacing {
  padding: var(--breathe-spacing) 0;
}

/* Layout utilities */
.text-center { text-align: center; }.max-width-content {
  max-width: var(--container-max);
  margin: 0 auto;
}

/* Opacity utilities for whisper effects */
.opacity-whisper { opacity: var(--opacity-whisper); }.opacity-subtle {
  opacity: var(--opacity-subtle);
}.opacity-soft {
  opacity: var(--opacity-soft);
}.opacity-visible {
  opacity: var(--opacity-visible);
}

/* ===== ENTERPRISE RESPONSIVE SYSTEM - COMPLETE COVERAGE ===== */

/* Mobile First - Base styles above, overrides below */

/* Small Mobile (320px-479px) */
@media (max-width: 480px) {.hero-title {
  font-size: 36px;
  letter-spacing: 0.15em;
}.section-header {
  font-size: 24px;
}.card-title {
  font-size: 20px;
}.container {
  padding: 60px 4%;
}.nav-links {
  gap: 16px;
}
}

/* Mobile (480px-767px) */
@media (min-width: 480px) and (max-width: 768px) {.about-container {
  grid-template-columns: 1fr;
  gap: 48px;
}
}

/* Tablet (768px-1023px) */
@media (min-width: 768px) and (max-width: 1024px) {
}

/* Desktop (1024px-1439px) */
@media (min-width: 1024px) and (max-width: 1440px) {
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
}

/* ===== KLUCZOWE ZASADY IMPLEMENTACJI ===== */

/* 1. Zero Rounded Corners */
* {  }

/* 2. Tylko Opacity Hovers */
.hover-element:hover {
  opacity: 0.7;
  /* NIGDY: transform, color, background, shadow */
}

/* 3. Performance Optimizations */
.smooth-transition {
  transition: all var(--duration-quick) var(--ease-premium);
}

/* 🎯 PREMIUM TRANSITION UTILITIES */
.transition-instant {
  transition: all var(--duration-instant) var(--ease-swift);
}.transition-quick {
  transition: all var(--duration-quick) var(--ease-premium);
}.transition-medium {
  transition: all var(--duration-medium) var(--ease-smooth);
}.transition-slow {
  transition: all var(--duration-slow) var(--ease-elastic);
}.gpu-acceleration {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 4. Enterprise Accessibility */
.focus-visible,
*:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  font-size: 14px;
  font-weight: 300;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {.btn-ghost {
  border-width: 2px;
}
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {* {
  animation-duration: 0.01ms !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}.mandala-outer,
  .mandala-middle,
  .mandala-inner {
  animation: none !important;
}
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {:root {
  --charcoal: #e0e0e0;
  --sanctuary: #1a1a1a;
  --stone: #a0a0a0;
  --whisper: #2a2a2a;
}
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}::-webkit-scrollbar-track {
  background: transparent;
}::-webkit-scrollbar-thumb {
  background: var(--stone);
  opacity: 0.3;
}

/* ===== SPIRITUAL ELEMENTS ===== */

/* Om Symbol Styling */
.om-symbol {
  font-family: 'Noto Sans Devanagari', serif;
  color: var(--om-symbol);
  font-size: 1.5rem;
  opacity: 0.8;
  display: inline-block;
}

/* Balinese Greeting */
.balinese-greeting {
  font-style: italic;
  color: var(--temple-gold);
  font-size: 0.9rem;
  letter-spacing: 0.05em;
  font-family: 'Inter', sans-serif;
  font-weight: 300;
}

/* Cultural Text Accents */
.temple-gold-text {
  color: var(--temple-gold);
  font-weight: 400;
}.sage-text {
  color: var(--sage-green);
  font-weight: 400;
}.sri-lankan-accent {
  color: var(--ocean-blue);
  font-weight: 400;
}

/* ===== LOTUS DECORATIVE ELEMENTS ===== */
.lotus-divider {
  text-align: center;
  margin: 60px 0;
  color: var(--sage-green);
  font-size: 1.2rem;
  opacity: 0.6;
}.lotus-divider::before,
.lotus-divider::after {
  margin: 0 20px;
  opacity: 0.4;
  content: '❀';
}

/* ===== RETREAT CARD STYLING - ULTRA-MINIMAL ===== */
.retreat-card {
  background: transparent;
  border: none;
  margin-bottom: 80px;
  transition: opacity 0.2s ease;
}.retreat-card:hover {
  opacity: 0.9;
}.retreat-image {
  width: 100%;
  height: 400px;
  /* Sharp edges */
  display: block;
  -o-object-fit: cover;
     object-fit: cover;
}.retreat-image-container {
  overflow: hidden;
  margin-bottom: 30px;
}

/* ===== ENTERPRISE ENHANCEMENTS ===== */

/* Sacred Quote Styling */
.sacred-quote {
  font-family: 'Cormorant Garamond', serif;
  font-style: italic;
  font-weight: 300;
  letter-spacing: 0.02em;
  line-height: 1.6;
  color: var(--stone);
}

/* Enterprise Image Treatments */
.enterprise-image {
  position: relative;
  overflow: hidden;
}.enterprise-image img {
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}.enterprise-image:hover img {
  opacity: 0.9;
  /* BAKASANA RULE: Only opacity changes on hover */;
}

/* Sacred Dividers */
.sacred-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 4rem 0;
}.sacred-divider::before,
.sacred-divider::after {
  flex: 1;
  height: 1px;
  background: var(--stone);
  opacity: 0.3;
  content: '';
}.sacred-divider-content {
  margin: 0 2rem;
  color: var(--stone);
  font-size: 1.5rem;
  opacity: 0.6;
}

/* ===== ABOUT PAGE - OLD MONEY ELEGANCE ===== */

.about-page-elegant {
  background: linear-gradient(135deg, 
    #fdfcf8 0%,
    #f9f7f2 30%,
    #f5f3ef 70%,
    #f1efeb 100%
  );
  min-height: 100vh;
  position: relative;
}.about-page-elegant::before {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: radial-gradient(circle at 30% 20%, rgba(193, 155, 104, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(193, 155, 104, 0.03) 0%, transparent 50%);
  content: '';
  pointer-events: none;
}

/* Hero Section - Old Money Refined (Zmniejszona wysokość o 20%) */
.about-hero-refined {
  padding: 80px 0 60px 0;
  background: linear-gradient(135deg, 
    rgba(253, 252, 248, 0.97) 0%,
    rgba(249, 247, 242, 0.99) 50%,
    rgba(245, 243, 239, 0.95) 100%
  );
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(193, 155, 104, 0.1);
}.about-hero-refined::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23c19b68' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
  content: '';
}.about-hero-container {
  position: relative;
  z-index: 10;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-hero-ornament {
  position: relative;
  width: 80px;
  height: 1px;
  margin: 0 auto 40px auto;
  background: var(--temple-gold);
  opacity: 0.6;
}.about-hero-ornament::before,
.about-hero-ornament::after {
  position: absolute;
  top: -2px;
  width: 6px;
  height: 6px;
  background: var(--temple-gold);
  content: '';
}.about-hero-ornament::before {
  left: -10px;
}.about-hero-ornament::after {
  right: -10px;
}.about-hero-content-elegant {
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
}.about-hero-name {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  animation: fadeInUp 1s ease-out;
  letter-spacing: 0.02em;
}.about-hero-divider {
  width: 120px;
  height: 1px;
  margin: 0 auto 20px auto;
  background: var(--temple-gold);
  opacity: 0.8;
}.about-hero-credentials {
  margin: 0 0 20px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.4;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}.about-hero-quote {
  position: relative;
  margin: 0;
  padding: 0 40px;
  color: var(--temple-gold);
  font-family: var(--font-primary);
  font-size: 1.6rem;
  font-style: italic;
  line-height: 1.4;
  transform: translateY(0);
  animation: fadeInUp 1.2s ease-out;
  opacity: 0.9;
}.about-hero-quote::before,
.about-hero-quote::after {
  position: absolute;
  top: -10px;
  color: var(--temple-gold);
  font-size: 3rem;
  opacity: 0.3;
  content: '"';
}.about-hero-quote::before {
  left: 0;
}.about-hero-quote::after {
  right: 0;
}

/* Main Content - Refined Layout */
.about-content-refined {
  padding: 100px 0;
  background: #fdfcf8;
}.about-content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-content-grid {
  display: grid;
  align-items: start;
  grid-template-columns: 1fr 1.2fr;
  gap: 6rem;
}.about-photo-column {
  position: sticky;
  top: 100px;
  display: flex;
  justify-content: center;
}.about-photo-frame {
  position: relative;
  padding: 20px;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08),
    0 8px 40px rgba(0, 0, 0, 0.04);
  transform: rotate(-1deg);
  transition: all 0.3s ease;
}.about-photo-frame:hover {
  transform: rotate(0deg) scale(1.02);
}.about-photo-placeholder {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  width: 350px;
  height: 440px;
  border: 1px solid rgba(193, 155, 104, 0.2);
  background: linear-gradient(135deg, 
    #f9f7f2 0%,
    #f5f3ef 100%
  );
}.about-photo-ornament {
  position: absolute;
  left: 50%;
  width: 60px;
  height: 20px;
  background: var(--temple-gold);
  transform: translateX(-50%);
  opacity: 0.1;
}.about-photo-ornament-top {
  top: 20px;
  clip-path: polygon(0 0, 100% 0, 90% 100%, 10% 100%);
}.about-photo-ornament-bottom {
  bottom: 20px;
  clip-path: polygon(10% 0, 90% 0, 100% 100%, 0 100%);
}.about-photo-content {
  z-index: 2;
  color: var(--temple-gold);
  text-align: center;
}.about-photo-text {
  margin-top: 20px;
  text-align: center;
}.about-photo-label {
  display: block;
  margin-bottom: 8px;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.about-photo-name {
  display: block;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 1.2rem;
  font-style: italic;
}.about-text-column {
  padding-top: 40px;
}.about-text-content {
  max-width: 600px;
}.about-text-intro {
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 300;
  line-height: 1.8;
}.about-text-paragraph {
  margin: 0 0 32px 0;
  text-align: justify;
  -webkit-hyphens: auto;
          hyphens: auto;
}.about-text-paragraph:first-child {
  color: var(--temple-gold);
  font-size: 1.3rem;
  font-weight: 400;
}

/* Credentials Section - Refined */
.about-credentials-refined {
  padding: 60px 0;
  background: linear-gradient(135deg, 
    var(--whisper) 0%,
    rgba(248, 246, 240, 0.95) 100%
  );
}.about-credentials-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-credentials-header {
  margin-bottom: 60px;
  text-align: center;
}.about-credentials-title {
  margin: 0 0 16px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 300;
  line-height: 1.2;
}.about-credentials-subtitle {
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.about-credentials-grid-refined {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  max-width: 900px;
  margin: 0 auto;
  gap: 2rem;
}.about-credentials-grid-refined .about-credential-card:first-child {

}.about-credential-card {
  position: relative;
  padding: 3rem 2rem;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08),
    0 8px 40px rgba(0, 0, 0, 0.04);
  text-align: center;
  transform: translateY(0);
  transition: all 0.3s ease;
}.about-credential-card:hover {
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
    0 20px 60px rgba(0, 0, 0, 0.08);
  transform: translateY(-8px);
}.about-credential-card::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--temple-gold), rgba(193, 155, 104, 0.5));
  transition: opacity 0.3s ease;
  opacity: 0;
  content: '';
}.about-credential-card:hover::before {
  opacity: 1;
}.about-credential-icon-refined {
  display: block;
  margin-bottom: 24px;
  color: var(--temple-gold);
  font-size: 2.8rem;
  transition: transform 0.3s ease;
  opacity: 0.9;
}.about-credential-card:hover .about-credential-icon-refined {
  transform: scale(1.1);
  opacity: 1;
}.about-credential-content {
  text-align: center;
}.about-credential-label-refined {
  margin-bottom: 8px;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.about-credential-value-refined {
  margin-bottom: 8px;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 1.8rem;
  font-weight: 300;
}.about-credential-description {
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Journeys Section - Refined */
.about-journeys-refined {
  padding: 100px 0;
  background: #fdfcf8;
}.about-journeys-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-journeys-header-refined {
  margin-bottom: 60px;
  text-align: center;
}.about-journeys-title-refined {
  margin: 0 0 32px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 300;
  line-height: 1.2;
}.about-journeys-ornament {
  position: relative;
  width: 100px;
  height: 1px;
  margin: 0 auto 32px auto;
  background: var(--temple-gold);
  opacity: 0.6;
}.about-journeys-ornament::before,
.about-journeys-ornament::after {
  position: absolute;
  top: -3px;
  width: 8px;
  height: 8px;
  background: var(--temple-gold);
  content: '';
}.about-journeys-ornament::before {
  left: -15px;
}.about-journeys-ornament::after {
  right: -15px;
}.about-journeys-description-refined {
  max-width: 800px;
  margin: 0 auto;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  line-height: 1.8;
}.about-journeys-grid-refined {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
}.about-journey-card-refined {
  position: relative;
  overflow: hidden;
  padding: 3rem;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}.about-journey-card-refined::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 2px;
  background: var(--temple-gold);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  content: '';
}.about-journey-card-refined:hover::before {
  transform: translateX(0);
}.about-journey-card-refined:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-8px);
}.about-journey-header {
  margin-bottom: 24px;
}.about-journey-title-refined {
  margin: 0 0 8px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 1.8rem;
  font-weight: 300;
  line-height: 1.2;
}.about-journey-subtitle {
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-style: italic;
}.about-journey-content {
  margin-top: 24px;
}.about-journey-description-refined {
  margin-bottom: 24px;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
}.about-journey-link-refined {
  position: relative;
  padding-bottom: 2px;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  letter-spacing: 0.02em;
}.about-journey-link-refined::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--temple-gold);
  transition: width 0.3s ease;
  content: '';
}.about-journey-link-refined:hover::after {
  width: 100%;
}

/* CTA Section - Refined */
.about-cta-refined {
  padding: 100px 0;
  background: var(--whisper);
  position: relative;
  overflow: hidden;
}.about-cta-refined::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23c19b68' fill-opacity='0.02'%3E%3Cpath d='M20 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0-20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm20 0c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
  content: '';
}.about-cta-container {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-cta-content-refined {
  position: relative;
  padding: 4rem 3rem;
  background: white;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}.about-cta-ornament {
  position: relative;
  width: 60px;
  height: 1px;
  margin: 0 auto 32px auto;
  background: var(--temple-gold);
  opacity: 0.6;
}.about-cta-ornament::before,
.about-cta-ornament::after {
  position: absolute;
  top: -2px;
  width: 5px;
  height: 5px;
  background: var(--temple-gold);
  content: '';
}.about-cta-ornament::before {
  left: -10px;
}.about-cta-ornament::after {
  right: -10px;
}.about-cta-title-refined {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 300;
  line-height: 1.2;
}.about-cta-description-refined {
  margin: 0 0 40px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  line-height: 1.6;
}.about-cta-button-refined {
  position: relative;
  display: inline-block;
  overflow: hidden;
  padding: 18px 40px;
  border: 2px solid var(--temple-gold);
  background: transparent;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.4s ease;
  letter-spacing: 0.05em;
}.about-cta-button-refined::before {
  position: absolute;
  top: 0;
  left: -100%;
  z-index: -1;
  width: 100%;
  height: 100%;
  background: var(--temple-gold);
  transition: left 0.4s ease;
  content: '';
}.about-cta-button-refined:hover::before {
  left: 0;
}.about-cta-button-refined:hover {
  box-shadow: 0 8px 30px rgba(193, 155, 104, 0.4);
  color: white;
  transform: translateY(-3px);
}

/* NOWY UKŁAD WYŚRODKOWANY - OPCJA 1 (Old Money Elegance) */

/* Main Content - Centered Layout */
.about-content-centered {
  padding: 60px 0;
  background: linear-gradient(135deg, 
    #fdfcf8 0%,
    #f9f7f2 100%
  );
  position: relative;
  z-index: 5;
}.about-content-container-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

/* Małe wyśrodkowane zdjęcie */
.about-photo-centered {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50px;
  width: 100%;
}.about-photo-frame-small {
  position: relative;
  padding: 16px;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08),
    0 16px 64px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}.about-photo-frame-small:hover {
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.12),
    0 12px 60px rgba(0, 0, 0, 0.06);
  transform: scale(1.05);
}.about-photo-placeholder-small {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  width: 100px;
  height: 100px;
  border: 1px solid rgba(193, 155, 104, 0.2);
  background: linear-gradient(135deg, 
    #f9f7f2 0%,
    #f5f3ef 100%
  );
}.about-photo-placeholder-small .about-photo-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: var(--temple-gold);
}.about-photo-placeholder-small .about-photo-ornament {
  display: none;
}.about-photo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 140px;
  height: 140px;
  background: radial-gradient(circle, rgba(193, 155, 104, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease;
  opacity: 0;
  pointer-events: none;
}.about-photo-frame-small:hover .about-photo-glow {
  opacity: 1;
}

/* Tekst wyśrodkowany */
.about-text-centered {
  margin-bottom: 80px;
  width: 100%;
  display: flex;
  justify-content: center;
}.about-text-content-centered {
  display: grid;
  align-items: start;
  grid-template-columns: 1fr 1fr;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 60px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  gap: 50px;
}.about-text-paragraph-centered {
  margin: 0 0 32px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.25rem;
  font-weight: 300;
  line-height: 1.8;
  text-align: left;
  transition: opacity 0.3s ease;
  opacity: 0.9;
}.about-text-paragraph-centered:first-child {
  position: relative;
  color: var(--temple-gold);
  font-size: 1.35rem;
  font-weight: 400;
}.about-text-paragraph-centered:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
  opacity: 1;
}

/* Kolumny tekstu - symetria */
.about-text-column-left,
.about-text-column-right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  height: 100%;
}.about-text-column-left .about-text-paragraph-centered,
.about-text-column-right .about-text-paragraph-centered {
  width: 100%;
  margin-bottom: 32px;
}.about-text-column-left .about-text-paragraph-centered:last-child,
.about-text-column-right .about-text-paragraph-centered:last-child {
  margin-bottom: 0;
}

/* Journeys - Centered Single Card */
.about-journeys-centered {
  padding: 60px 0;
  background: linear-gradient(135deg, 
    var(--whisper) 0%,
    rgba(248, 246, 240, 0.95) 100%
  );
}.about-journeys-container-centered {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-journeys-header-centered {
  margin-bottom: 40px;
  text-align: center;
}.about-journeys-title-centered {
  margin: 0 0 16px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 300;
  line-height: 1.2;
}.about-journeys-description-centered {
  max-width: 500px;
  margin: 0 auto 40px auto;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  line-height: 1.8;
}.about-journeys-card-single {
  display: flex;
  justify-content: center;
}.about-journey-card-centered {
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 550px;
  padding: 3.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  text-align: center;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}.about-journey-card-centered::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 2px;
  background: var(--temple-gold);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  content: '';
}.about-journey-card-centered:hover::before {
  transform: translateX(0);
}.about-journey-card-centered:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-8px);
}.about-journey-header-centered {
  margin-bottom: 24px;
}.about-journey-title-centered {
  margin: 0 0 8px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 1.8rem;
  font-weight: 300;
  line-height: 1.2;
}.about-journey-subtitle-centered {
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-style: italic;
}.about-journey-content-centered {
  margin-top: 24px;
}.about-journey-description-centered {
  margin-bottom: 24px;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
}.about-journey-button-centered {
  position: relative;
  display: inline-block;
  overflow: hidden;
  padding: 16px 32px;
  border: 2px solid var(--temple-gold);
  background: transparent;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.4s ease;
  letter-spacing: 0.05em;
}.about-journey-button-centered::before {
  position: absolute;
  top: 0;
  left: -100%;
  z-index: -1;
  width: 100%;
  height: 100%;
  background: var(--temple-gold);
  transition: left 0.4s ease;
  content: '';
}.about-journey-button-centered:hover::before {
  left: 0;
}.about-journey-button-centered:hover {
  box-shadow: 0 12px 40px rgba(193, 155, 104, 0.4);
  color: white;
  transform: translateY(-3px);
  letter-spacing: 0.08em;
}

/* Responsive Design - Enhanced */
@media (max-width: 1024px) {
  
  /* Nowy układ wyśrodkowany - tablet */
  .about-text-content-centered {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 32px;
  }
}

@media (max-width: 768px) {.about-credentials-title,
  .about-journeys-title-refined,
  .about-cta-title-refined {
  font-size: 2rem;
}.about-credential-card,
  .about-journey-card-refined {
  padding: 2rem;
}
  
  /* Nowy układ wyśrodkowany - responsive */
  .about-content-centered {
    padding: 40px 0;
  }.about-journeys-centered {
  padding: 40px 0;
}
}

/* ===== ANIMATIONS FOR OLD MONEY ELEGANCE ===== */

@keyframes fadeInUp {
}

@keyframes fadeInScale {
}

/* Animation delays for staggered effect */
.about-credential-card:nth-child(1) {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}.about-credential-card:nth-child(2) {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}.about-credential-card:nth-child(3) {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}.about-photo-centered {
  animation: fadeInScale 1s ease-out 0.1s both;
}

@keyframes fadeInUp {
}

/* =============================================
   🎨 INTEGRACJA WIZUALNA - BAKASANA O MNIE
   Jednolite tło, minimalistyczny design
   ============================================= */

/* Resetuj wszystkie kontenery - usuń białe tła */
.about-page-integrated {
  background: linear-gradient(
    180deg, 
    #FAF8F5 0%, 
    #F5F0E8 100%
  );
  min-height: 100vh;
  position: relative;
}

/* Efekt płynnego przejścia między sekcjami */
.about-page-integrated section {
  position: relative;
}.about-page-integrated section::before {
  position: absolute;
  top: -50px;
  right: 0;
  left: 0;
  z-index: 1;
  height: 100px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(250, 248, 245, 0.3),
    transparent
  );
  content: '';
  pointer-events: none;
}.about-page-integrated section:first-child::before {
  display: none;
}

/* Delikatne separatory zamiast białych boxów */
.content-separator {
  width: 60px;
  height: 1px;
  background: #BE9561;
  margin: 40px auto;
  opacity: 0.5;
  position: relative;
}.content-separator::before,
.content-separator::after {
  position: absolute;
  top: -1px;
  width: 3px;
  height: 3px;
  background: #BE9561;
  content: '';
}.content-separator::before {
  left: -8px;
}.content-separator::after {
  right: -8px;
}

/* HERO SECTION - Minimalistyczny */
.about-hero-minimal {
  padding: 120px 0 80px 0;
  background: none;
  text-align: center;
}.about-hero-container-minimal {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-hero-content-minimal {
  padding: 0;
  background: none;
}.about-hero-name-minimal {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.1;
  letter-spacing: -0.02em;
}.about-hero-credentials-minimal {
  margin: 0 0 40px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 400;
}.about-hero-quote-minimal {
  margin: 0;
  color: var(--temple-gold);
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 300;
  font-style: italic;
  opacity: 0.9;
}

/* MAIN CONTENT - Zintegrowane */
.about-content-integrated {
  padding: 80px 0;
  background: none;
}.about-content-container-integrated {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

/* Zdjęcie - subtelne, zintegrowane */
.about-photo-integrated {
  display: flex;
  justify-content: center;
  margin-bottom: 60px;
}.about-photo-frame-integrated {
  position: relative;
  padding: 12px;
  border: 1px solid rgba(190, 149, 97, 0.15);
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}.about-photo-frame-integrated:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}.about-photo-placeholder-integrated {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  background: transparent;
  color: var(--temple-gold);
}

/* Tekst - bez białego tła */
.about-text-integrated {
  margin-bottom: 80px;
}.about-text-content-integrated {
  display: grid;
  align-items: start;
  grid-template-columns: 1fr 1fr;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
  background: none;
  gap: 80px;
}.about-text-paragraph-integrated {
  margin: 0 0 32px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.25rem;
  font-weight: 300;
  line-height: 1.8;
  text-align: left;
  opacity: 0.9;
}.about-text-paragraph-integrated:first-child {
  color: var(--temple-gold);
  font-size: 1.35rem;
  font-weight: 400;
}

/* SEKCJA MOJA HISTORIA */
.about-story-integrated {
  padding: 80px 0;
  background: none;
}.about-story-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}.about-story-title {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3em;
}.about-story-content {
  margin-top: 60px;
  text-align: left;
}.about-story-paragraph {
  margin: 0 0 32px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 300;
  line-height: 1.8;
  opacity: 0.9;
}

/* CERTYFIKACJE - Minimalistyczne */
.about-credentials-integrated {
  padding: 80px 0;
  background: none;
}.about-credentials-container-integrated {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}.about-credentials-title-integrated {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3em;
}.about-credentials-list-integrated {
  display: flex;
  flex-direction: column;
  margin-top: 60px;
  text-align: left;
  gap: 32px;
}.about-credential-item-integrated {
  display: flex;
  align-items: flex-start;
  padding: 0;
  border: none;
  background: none;
  box-shadow: none;
  gap: 24px;
}.about-credential-marker {
  width: 3px;
  height: 3px;
  margin-top: 12px;
  background: var(--temple-gold);
  flex-shrink: 0;
}.about-credential-content-integrated {
  flex: 1;
}.about-credential-label-integrated {
  margin: 0 0 8px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 400;
}.about-credential-description-integrated {
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 300;
  opacity: 0.8;
}

/* PODRÓŻE - Zintegrowane */
.about-journeys-integrated {
  padding: 80px 0;
  background: none;
}.about-journeys-container-integrated {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}.about-journeys-title-integrated {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3em;
}.about-journeys-content-integrated {
  margin-top: 60px;
  text-align: left;
}.about-journeys-description-integrated {
  margin: 0 0 40px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 300;
  line-height: 1.8;
  opacity: 0.9;
}.about-journeys-destinations {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
  gap: 20px;
}.about-destination-item {
  display: flex;
  align-items: center;
  padding-left: 20px;
  border-left: 3px solid var(--temple-gold);
  gap: 20px;
}.about-destination-name {
  min-width: 120px;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
}.about-destination-description {
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 300;
  opacity: 0.8;
}.about-journey-button-integrated {
  display: inline-block;
  margin-top: 20px;
  padding: 16px 0;
  border: none;
  border-bottom: 2px solid var(--temple-gold);
  background: none;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  letter-spacing: 0.05em;
}.about-journey-button-integrated:hover {
  color: var(--charcoal);
  transform: translateY(-2px);
  border-bottom-color: var(--charcoal);
}

/* SEKCJA JOGA ONLINE - Zintegrowana */
.about-online-integrated {
  padding: 80px 0;
  background: none;
}.about-online-container-integrated {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}.about-online-title-integrated {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3em;
}.about-online-content-integrated {
  margin-top: 60px;
  text-align: left;
}.about-online-description-integrated {
  margin: 0 0 50px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 300;
  line-height: 1.8;
  text-align: center;
  opacity: 0.9;
}.about-online-methods {
  display: flex;
  flex-direction: column;
  margin-bottom: 50px;
  gap: 32px;
}.about-online-method-item {
  display: flex;
  align-items: flex-start;
  padding: 0;
  border: none;
  background: none;
  box-shadow: none;
  gap: 24px;
}.about-online-method-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  background: rgba(190, 149, 97, 0.1);
  color: var(--temple-gold);
  transition: all 0.3s ease;
  flex-shrink: 0;
}.about-online-method-item:hover .about-online-method-icon {
  background: rgba(190, 149, 97, 0.2);
  transform: scale(1.1);
}.about-online-method-content {
  flex: 1;
}.about-online-method-title {
  margin: 0 0 8px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.3rem;
  font-weight: 400;
}.about-online-method-description {
  margin: 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 300;
  line-height: 1.6;
  opacity: 0.8;
}.about-online-platforms {
  margin-bottom: 40px;
  padding: 32px 0;
  border-top: 1px solid rgba(190, 149, 97, 0.2);
  border-bottom: 1px solid rgba(190, 149, 97, 0.2);
}.about-online-platforms-title {
  margin: 0 0 20px 0;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
}.about-online-platforms-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
}.about-online-platform-item {
  padding: 8px 16px;
  border: 1px solid rgba(190, 149, 97, 0.2);
  background: rgba(190, 149, 97, 0.1);
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  font-weight: 400;
  transition: all 0.3s ease;
}.about-online-platform-item:hover {
  background: rgba(190, 149, 97, 0.2);
  transform: translateY(-2px);
}.about-online-button-integrated {
  display: inline-block;
  width: 100%;
  margin-top: 20px;
  padding: 16px 0;
  border: none;
  border-bottom: 2px solid var(--temple-gold);
  background: none;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  letter-spacing: 0.05em;
}.about-online-button-integrated:hover {
  color: var(--charcoal);
  transform: translateY(-2px);
  border-bottom-color: var(--charcoal);
}

/* CTA SECTION - Minimalistyczny */
.about-cta-integrated {
  padding: 80px 0;
  background: none;
}.about-cta-container-integrated {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.about-cta-content-integrated {
  padding: 0;
  background: none;
  text-align: center;
}.about-cta-title-integrated {
  margin: 20px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 300;
  line-height: 1.2;
}.about-cta-description-integrated {
  margin: 0 0 40px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 1.2rem;
  font-weight: 300;
  line-height: 1.6;
}.about-cta-button-integrated {
  position: relative;
  display: inline-block;
  overflow: hidden;
  padding: 18px 40px;
  border: 2px solid var(--temple-gold);
  background: transparent;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.4s ease;
  letter-spacing: 0.05em;
}.about-cta-button-integrated::before {
  position: absolute;
  top: 0;
  left: -100%;
  z-index: -1;
  width: 100%;
  height: 100%;
  background: var(--temple-gold);
  transition: left 0.4s ease;
  content: '';
}.about-cta-button-integrated:hover::before {
  left: 0;
}.about-cta-button-integrated:hover {
  box-shadow: 0 8px 30px rgba(193, 155, 104, 0.4);
  color: white;
  transform: translateY(-3px);
}

/* RESPONSIVE DESIGN */
@media (max-width: 1024px) {
}

@media (max-width: 768px) {
}

/* Subtelne animacje wejścia */
.about-content-integrated,
.about-story-integrated,
.about-credentials-integrated,
.about-journeys-integrated,
.about-online-integrated,
.about-cta-integrated {
  animation: fadeInUp 0.8s ease-out both;
}.about-story-integrated {
  animation-delay: 0.1s;
}.about-credentials-integrated {
  animation-delay: 0.2s;
}.about-journeys-integrated {
  animation-delay: 0.3s;
}.about-online-integrated {
  animation-delay: 0.4s;
}.about-cta-integrated {
  animation-delay: 0.5s;
}

/* Efekty hover dla paragrafów */
.about-text-paragraph-integrated:hover,
.about-story-paragraph:hover,
.about-journeys-description-integrated:hover,
.about-online-description-integrated:hover {
  opacity: 1;
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Efekty hover dla elementów kwalifikacji */
.about-credential-item-integrated:hover {
  transform: translateX(10px);
  transition: all 0.3s ease;
}.about-credential-item-integrated:hover .about-credential-marker {
  transform: scale(1.5);
  transition: all 0.3s ease;
}

/* Efekty hover dla destynacji */
.about-destination-item:hover {
  transform: translateX(10px);
  border-left-color: var(--charcoal);
  transition: all 0.3s ease;
}.about-destination-item:hover .about-destination-name {
  color: var(--temple-gold);
  transition: all 0.3s ease;
}

@keyframes fadeInLeft {
}

@keyframes fadeInRight {
}

@keyframes scaleIn {
}

/* Apply animations to key elements */
.about-hero-content-elegant {
  animation: fadeInUp 0.8s ease-out;
}.about-credential-card:nth-child(1) {
  animation-delay: 0.1s;
}.about-journey-card-refined:nth-child(1) {
  animation-delay: 0.1s;
}.about-journey-card-refined:nth-child(2) {
  animation-delay: 0.2s;
}

/* Reduce animations on mobile for better performance */
@media (prefers-reduced-motion: reduce) {
}

/* Hover effects for enhanced interactivity */
.about-hero-name:hover {
  color: var(--temple-gold);
  transition: color 0.3s ease;
}.about-text-paragraph:hover {
  color: var(--charcoal);
  transition: color 0.3s ease;
}

/* Subtle parallax effect for ornaments */
.about-hero-ornament {
  transition: transform 0.3s ease;
}.about-hero-refined:hover .about-hero-ornament {
  transform: translateY(-2px);
}.about-journeys-header-refined:hover .about-journeys-ornament {
  transform: translateY(-1px);
}

/* Animacje dla sekcji O mnie */
@keyframes fadeInUp {
}

@keyframes gentleFloat {
}

/* Subtelne animacje dla ornamentów */
.about-hero-ornament {
  animation: gentleFloat 4s ease-in-out infinite;
}

/* ===== MAGAZINE STYLE BLOG SECTION ===== */

/* Magazine Hero Section */
.magazine-hero {
  padding: 80px 0 60px 0;
  max-width: var(--container-max);
  margin: 0 auto;
  text-align: center;
}.magazine-hero-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.magazine-header-line {
  width: 60px;
  height: 1px;
  margin: 0 auto 32px auto;
  background: var(--temple-gold);
  opacity: 0.6;
}.magazine-title {
  margin: 0 0 24px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 48px;
  text-transform: none;
  /* Znacznie mniejszy niż poprzedni */
  font-weight: 300;
  letter-spacing: 0.05em;
}.magazine-subtitle {
  margin: 0 0 32px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 16px;
  font-weight: 300;
  font-style: italic;
}.magazine-meta {
  margin-bottom: 32px;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 12px;
  font-weight: 300;
  opacity: 0.7;
  letter-spacing: 0.1em;
}

/* Magazine Content Layout */
.magazine-content {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}.magazine-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  margin-bottom: 80px;
  gap: 60px;
}

/* Featured Article - Large */
.magazine-featured {
  grid-row: span 2;
}

/* Secondary Articles */
.magazine-secondary {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* Small Articles Grid */
.magazine-grid-small {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

/* Magazine Card Styles */
.magazine-card,
.magazine-card-featured {
  background: var(--pure-white);
  border: 1px solid rgba(139, 134, 128, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}.magazine-card:hover,
.magazine-card-featured:hover {
  box-shadow: 0 8px 25px rgba(42, 39, 36, 0.08);
  transform: translateY(-2px);
}.magazine-card-link {
  display: block;
  height: 100%;
  color: inherit;
  text-decoration: none;
}

/* Image Sections */
.magazine-card-image {
  position: relative;
  overflow: hidden;
}.magazine-card .magazine-card-image {
  height: 200px;
}.magazine-card-featured .magazine-card-image {
  height: 300px;
}.magazine-image-bg {
  width: 100%;
  height: 100%;
  transition: transform 0.4s ease;
  filter: sepia(0.1) contrast(0.95);
}.magazine-card:hover .magazine-image-bg,
.magazine-card-featured:hover .magazine-image-bg {
  transform: scale(1.02);
}.magazine-image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: flex-end;
  padding: 20px;
  background: linear-gradient(to bottom, transparent 0%, rgba(42, 39, 36, 0.1) 100%);
}.magazine-category {
  padding: 6px 12px;
  background: rgba(42, 39, 36, 0.7);
  color: var(--pure-white);
  font-family: var(--font-secondary);
  font-size: 10px;
  font-weight: 400;
  text-transform: uppercase;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  letter-spacing: 0.1em;
}

/* Content Sections */
.magazine-card-content {
  padding: 32px;
}.magazine-card-featured .magazine-card-content {
  padding: 40px;
}.magazine-card-title {
  margin: 0 0 16px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 20px;
  font-weight: 400;
  line-height: 1.4;
  transition: color 0.3s ease;
}.magazine-card-featured .magazine-card-title {
  margin-bottom: 20px;
  font-size: 28px;
}.magazine-card-link:hover .magazine-card-title {
  color: var(--temple-gold);
}.magazine-card-excerpt {
  display: -webkit-box;
  overflow: hidden;
  margin: 0 0 24px 0;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 300;
  line-height: 1.6;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}.magazine-card-featured .magazine-card-excerpt {
  margin-bottom: 32px;
  font-size: 15px;
  -webkit-line-clamp: 4;
}.magazine-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}.magazine-read-more {
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-size: 11px;
  font-weight: 400;
  text-transform: uppercase;
  transition: opacity 0.3s ease;
  letter-spacing: 0.1em;
}.magazine-card-link:hover .magazine-read-more {
  opacity: 0.7;
}.magazine-read-time {
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 11px;
  font-weight: 300;
  opacity: 0.6;
}

/* Empty State */
.magazine-empty {
  text-align: center;
  padding: 120px 0;
}.magazine-empty-content {
  max-width: 400px;
  margin: 0 auto;
}.magazine-empty-title {
  margin: 0 0 16px 0;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: 24px;
  font-weight: 300;
}.magazine-empty-text {
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 300;
  line-height: 1.6;
}

/* Magazine Responsive Styles */
@media (max-width: 1024px) {.magazine-secondary {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}.magazine-grid-small {
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}
}

@media (max-width: 768px) {.magazine-hero {
  padding: 60px 0 40px 0;
}.magazine-content {
  padding: 0 5%;
}.magazine-card-content,
  .magazine-card-featured .magazine-card-content {
  padding: 24px;
}.magazine-card .magazine-card-image,
  .magazine-card-featured .magazine-card-image {
  height: 180px;
}
}

@media (max-width: 480px) {.magazine-card-title,
  .magazine-card-featured .magazine-card-title {
  font-size: 18px;
}.magazine-card-excerpt,
  .magazine-card-featured .magazine-card-excerpt {
  font-size: 13px;
}
  
  /* Enhanced mobile responsiveness */
  .hero-title {
    font-size: clamp(48px, 12vw, 72px);
    letter-spacing: 0.12em;
    line-height: 1.1;
  }.hero-subtitle {
  margin-top: 24px;
  font-size: 13px;
}.hero-quote {
  margin: 24px 0;
  font-size: 15px;
}.section-divider {
  width: 80px;
  margin: 60px auto;
}
}

/* ===== ENHANCED UTILITY CLASSES ===== */
.text-balance {
  text-wrap: balance;
}.smooth-scroll {
  scroll-behavior: smooth;
}.backdrop-blur-subtle {
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}.glass-effect {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}.elegant-shadow {
  box-shadow: 0 1px 3px rgba(44, 41, 40, 0.02),
    0 4px 12px rgba(44, 41, 40, 0.04),
    0 16px 24px rgba(44, 41, 40, 0.02);
}.elegant-shadow-hover {
  box-shadow: 0 4px 16px rgba(44, 41, 40, 0.06),
    0 12px 32px rgba(44, 41, 40, 0.08),
    0 24px 48px rgba(44, 41, 40, 0.04);
}.gradient-text {
  background: linear-gradient(135deg, var(--temple-gold), var(--om-symbol));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}.spiritual-ornament {
  position: relative;
}.spiritual-ornament::before {
  position: absolute;
  top: -16px;
  left: 50%;
  color: var(--temple-gold);
  font-size: 18px;
  transform: translateX(-50%);
  opacity: 0.6;
  content: '॰';
}.micro-interaction {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}.micro-interaction:hover {
  transform: translateY(-1px);
  filter: brightness(1.05);
}

/* ===== ENHANCED COMPONENT CONSISTENCY ===== */
.enterprise-card {
  background: rgba(255, 255, 255, 0.6);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}.enterprise-card::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(
    135deg,
    rgba(253, 249, 243, 0.8) 0%,
    rgba(249, 246, 242, 0.9) 100%
  );
  transition: opacity 0.4s ease;
  opacity: 0;
  content: '';
}.enterprise-card:hover::before {
  opacity: 1;
}.enterprise-card:hover {
  box-shadow: 0 4px 16px rgba(44, 41, 40, 0.06),
    0 12px 32px rgba(44, 41, 40, 0.08);
  transform: translateY(-8px);
}

/* ===== SMOOTH SECTION TRANSITIONS ===== */
.section-transition {
  position: relative;
  overflow: hidden;
}.section-transition::before {
  position: absolute;
  top: -60px;
  right: 0;
  left: 0;
  z-index: -1;
  height: 120px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  content: '';
  pointer-events: none;
}.section-transition::after {
  position: absolute;
  right: 0;
  bottom: -60px;
  left: 0;
  z-index: -1;
  height: 120px;
  background: linear-gradient(
    to top,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  content: '';
  pointer-events: none;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}.will-change-opacity {
  will-change: opacity;
}.gpu-layer {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ENHANCED ACCESSIBILITY ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}.focus-visible-only {
  outline: none;
}.focus-visible-only:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {.navigation,
  .footer,
  .btn-ghost,
  .social-links {
  display: none !important;
}
}

/* =============================================
   🎯 NOWE SEKCJE - PLAN NAPRAWCZY
   ============================================= */

/* ===== SEKCJA MIEJSCA GDZIE ODDYCHASZ ===== */
.destinations-section {
  background: linear-gradient(180deg, #FDF9F3 0%, #FAF5F0 100%);
  padding: 120px 0;
}

@media (max-width: 768px) {
}.destination-image-wrapper {
  overflow: hidden;
  height: 400px;
}.destination-image {
  width: 100%;
  height: 100%;
  transition: transform 0.4s ease;
  -o-object-fit: cover;
     object-fit: cover;
}.destination-card:hover .destination-image {
  transform: scale(1.05);
}.destination-title {
  margin-bottom: 16px;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 36px;
  font-weight: 300;
}.destination-meta {
  margin-bottom: 24px;
  color: #C9A575;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}.destination-description {
  margin-bottom: 20px;
  color: var(--charcoal);
  font-size: 18px;
  line-height: 1.6;
}.destination-testimonial {
  margin-bottom: 24px;
  color: var(--stone);
  font-size: 14px;
  font-style: italic;
}.destination-link {
  display: inline-block;
  color: #C9A575;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.3s ease;
  letter-spacing: 0.1em;
}.destination-link:hover {
  color: var(--charcoal);
}

/* ===== SEKCJA O JULII (UPROSZCZONA) ===== */
.about-julia-simple {
  background: #FFFFFF;
  padding: 100px 0;
}.about-julia-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 8%;
  text-align: center;
}.julia-portrait-wrapper {
  margin-bottom: 40px;
}.julia-portrait {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  -o-object-fit: cover;
     object-fit: cover;
}.julia-title {
  margin-bottom: 20px;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 200;
}.divider-gold {
  width: 60px;
  height: 2px;
  margin: 0 auto 40px;
  background: #C9A575;
}.julia-bio {
  margin-bottom: 40px;
  color: #5A5A5A;
  font-size: 18px;
  line-height: 1.8;
}.julia-bio p {
  margin-bottom: 24px;
}.text-gold {
  color: #C9A575;
}.julia-cta {
  display: inline-block;
  padding: 16px 48px;
  border: 1px solid #C9A575;
  color: #C9A575;
  font-size: 14px;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.3s ease;
  letter-spacing: 0.1em;
}.julia-cta:hover {
  background: #C9A575;
  color: white;
  transform: translateY(-2px);
}

/* ===== SEKCJA KONTAKT (MINIMALISTYCZNY) ===== */
.contact-simple {
  background: linear-gradient(135deg, #FAF5F0 0%, #F5EDE4 100%);
  padding: 100px 0;
}.contact-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 8%;
  text-align: center;
}.contact-title {
  margin-bottom: 20px;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-size: 56px;
  font-weight: 200;
}.contact-subtitle {
  margin-bottom: 60px;
  color: #C9A575;
  font-size: 20px;
  font-style: italic;
}.contact-options {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  gap: 30px;
}

@media (max-width: 768px) {
}.contact-button {
  display: block;
  flex: 1;
  padding: 20px 40px;
  border: 1px solid #E8E8E8;
  background: white;
  color: var(--charcoal);
  font-size: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
}.contact-button:hover {
  border-color: #C9A575;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}.contact-button h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 400;
}.contact-button p {
  margin: 0;
  color: var(--stone);
  font-size: 14px;
}

/* ===== FOOTER ULTRA MINIMALISTYCZNY - ELEGANT EDITION ===== */
.footer {
  background: #1a1a1a; /* Nie czarny, cieplejszy */
  color: #F5F5F5;
  padding: 60px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

/* Subtelny pattern/tekstura */
.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(
      circle at 20% 50%, 
      rgba(184,149,106,0.03) 0%, 
      transparent 50%
    );
  pointer-events: none;
}

/* Delikatny separator na górze */
.footer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(184,149,106,0.3),
    transparent
  );
}

@media (max-width: 768px) {
}

/* ===== GHOST BUTTONS - UNIVERSAL ELEGANT STYLE ===== */
.btn-ghost {
  background: transparent;
  border: 1.5px solid #B8956A;
  color: #B8956A;
  padding: 12px 30px;
  
  transition: all 0.3s ease;
  font-weight: 300;
  letter-spacing: 1px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* For dark sections - global ghost button */
.dark-section .btn-ghost {
  border-color: rgba(255,255,255,0.7);
  color: rgba(255,255,255,0.9);
}.dark-section .btn-ghost:hover {
  border-color: rgba(255,255,255,0.9);
  background: rgba(255,255,255,0.1);
  color: white;
}

/* ===== FLOATING ANIMATION ===== */
@keyframes float {
}

/* ===== GLOBALNE POPRAWKI ===== */
* {
  transition: all 0.3s ease;
}

/* ===== SPÓJNA HIERARCHIA FONTÓW ===== */
/* Hero title - najważniejszy */
.hero-title {
  font-size: clamp(120px, 10vw, 140px);
}

/* Section headers - główne nagłówki sekcji */
.section-header {
  font-size: clamp(48px, 6vw, 56px);
}

/* Subsection headers - podsekcje */
.subsection-header {
  font-size: clamp(32px, 4vw, 36px);
}

/* Body text - tekst główny */
.body-text {
  font-size: clamp(16px, 1.5vw, 18px);
}

/* Small text - drobny tekst */
.small-text {
  font-size: clamp(12px, 1vw, 14px);
}

/* ===== KOLORY - TYLKO DOZWOLONE ===== */
.text-main { color: #FDF9F3; }.text-section {
  color: #FAF5F0;
}.text-primary {
  color: #3A3A3A;
}.text-accent {
  color: #C9A575;
}.text-footer {
  color: #2A2520;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {.destinations-section {
  padding: 80px 0;
}.about-julia-simple {
  padding: 80px 0;
}.contact-simple {
  padding: 80px 0;
}
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
}.destination-card,
.julia-portrait,
.contact-button {
  animation: fadeInUp 0.8s ease-out;
}.file\:border-0::file-selector-button {
  border-width: 0px;
}.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.file\:font-medium::file-selector-button {
  font-weight: 500;
}.placeholder\:font-light::-moz-placeholder {
  font-weight: 300;
}.placeholder\:font-light::placeholder {
  font-weight: 300;
}.placeholder\:text-sage::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(139 134 128 / var(--tw-text-opacity, 1));
}.placeholder\:text-sage::placeholder {
  --tw-text-opacity: 1;
  color: rgb(139 134 128 / var(--tw-text-opacity, 1));
}.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}.last\:border-r-0:last-child {
  border-right-width: 0px;
}.focus-within\:bg-whisper\/30:focus-within {
  background-color: rgb(245 242 237 / 0.3);
}.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus-within\:ring-enterprise-brown\/10:focus-within {
  --tw-ring-color: rgb(139 115 85 / 0.1);
}.focus-within\:ring-enterprise-brown\/20:focus-within {
  --tw-ring-color: rgb(139 115 85 / 0.2);
}.focus-within\:ring-terra\/20:focus-within {
  --tw-ring-color: rgb(184 147 92 / 0.2);
}.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:-translate-y-\[1px\]:hover {
  --tw-translate-y: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:translate-x-1:hover {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:translate-x-2:hover {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:rotate-1:hover {
  --tw-rotate: 1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:scale-\[1\.01\]:hover {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:transform:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:animate-none:hover {
  animation: none;
}.hover\:border-charcoal:hover {
  --tw-border-opacity: 1;
  border-color: rgb(42 39 36 / var(--tw-border-opacity, 1));
}.hover\:border-enterprise-brown:hover {
  --tw-border-opacity: 1;
  border-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}.hover\:border-enterprise-brown\/20:hover {
  border-color: rgb(139 115 85 / 0.2);
}.hover\:border-enterprise-brown\/30:hover {
  border-color: rgb(139 115 85 / 0.3);
}.hover\:border-red-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}.hover\:border-sanctuary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 252 248 / var(--tw-border-opacity, 1));
}.hover\:border-stone-light:hover {
  --tw-border-opacity: 1;
  border-color: rgb(196 191 184 / var(--tw-border-opacity, 1));
}.hover\:border-terra:hover {
  --tw-border-opacity: 1;
  border-color: rgb(184 147 92 / var(--tw-border-opacity, 1));
}.hover\:bg-accent:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(139 115 85 / var(--tw-bg-opacity, 1));
}.hover\:bg-accent\/10:hover {
  background-color: rgb(139 115 85 / 0.1);
}.hover\:bg-accent\/5:hover {
  background-color: rgb(139 115 85 / 0.05);
}.hover\:bg-black\/95:hover {
  background-color: rgb(0 0 0 / 0.95);
}.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}.hover\:bg-charcoal:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(42 39 36 / var(--tw-bg-opacity, 1));
}.hover\:bg-charcoal-light:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(74 69 63 / var(--tw-bg-opacity, 1));
}.hover\:bg-charcoal\/10:hover {
  background-color: rgb(42 39 36 / 0.1);
}.hover\:bg-charcoal\/20:hover {
  background-color: rgb(42 39 36 / 0.2);
}.hover\:bg-charcoal\/5:hover {
  background-color: rgb(42 39 36 / 0.05);
}.hover\:bg-charcoal\/80:hover {
  background-color: rgb(42 39 36 / 0.8);
}.hover\:bg-charcoal\/90:hover {
  background-color: rgb(42 39 36 / 0.9);
}.hover\:bg-enterprise-brown:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(139 115 85 / var(--tw-bg-opacity, 1));
}.hover\:bg-enterprise-brown\/10:hover {
  background-color: rgb(139 115 85 / 0.1);
}.hover\:bg-enterprise-brown\/5:hover {
  background-color: rgb(139 115 85 / 0.05);
}.hover\:bg-enterprise-brown\/90:hover {
  background-color: rgb(139 115 85 / 0.9);
}.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.hover\:bg-primary\/80:hover {
  background-color: rgb(42 39 36 / 0.8);
}.hover\:bg-pure-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}.hover\:bg-sage\/5:hover {
  background-color: rgb(139 134 128 / 0.05);
}.hover\:bg-sage\/90:hover {
  background-color: rgb(139 134 128 / 0.9);
}.hover\:bg-sanctuary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 252 248 / var(--tw-bg-opacity, 1));
}.hover\:bg-sanctuary\/10:hover {
  background-color: rgb(253 252 248 / 0.1);
}.hover\:bg-sanctuary\/80:hover {
  background-color: rgb(253 252 248 / 0.8);
}.hover\:bg-sand:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 122 / var(--tw-bg-opacity, 1));
}.hover\:bg-secondary\/80:hover {
  background-color: rgb(107 101 96 / 0.8);
}.hover\:bg-silk\/10:hover {
  background-color: rgb(240 237 232 / 0.1);
}.hover\:bg-stone:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(168 163 158 / var(--tw-bg-opacity, 1));
}.hover\:bg-stone\/30:hover {
  background-color: rgb(168 163 158 / 0.3);
}.hover\:bg-terra:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(184 147 92 / var(--tw-bg-opacity, 1));
}.hover\:bg-terra\/20:hover {
  background-color: rgb(184 147 92 / 0.2);
}.hover\:bg-terra\/90:hover {
  background-color: rgb(184 147 92 / 0.9);
}.hover\:bg-whisper:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 242 237 / var(--tw-bg-opacity, 1));
}.hover\:bg-whisper\/50:hover {
  background-color: rgb(245 242 237 / 0.5);
}.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}.hover\:bg-white\/50:hover {
  background-color: rgb(255 255 255 / 0.5);
}.hover\:bg-white\/90:hover {
  background-color: rgb(255 255 255 / 0.9);
}.hover\:from-green-600:hover {
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.hover\:from-terra:hover {
  --tw-gradient-from: #B8935C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(184 147 92 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.hover\:from-white\/10:hover {
  --tw-gradient-from: rgb(255 255 255 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.hover\:to-green-700:hover {
  --tw-gradient-to: #15803d var(--tw-gradient-to-position);
}.hover\:to-sand:hover {
  --tw-gradient-to: #D4AF7A var(--tw-gradient-to-position);
}.hover\:to-white\/15:hover {
  --tw-gradient-to: rgb(255 255 255 / 0.15) var(--tw-gradient-to-position);
}.hover\:tracking-\[0\.25em\]:hover {
  letter-spacing: 0.25em;
}.hover\:text-\[\#B8956F\]\/80:hover {
  color: rgb(184 149 111 / 0.8);
}.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}.hover\:text-charcoal:hover {
  --tw-text-opacity: 1;
  color: rgb(42 39 36 / var(--tw-text-opacity, 1));
}.hover\:text-charcoal\/70:hover {
  color: rgb(42 39 36 / 0.7);
}.hover\:text-charcoal\/80:hover {
  color: rgb(42 39 36 / 0.8);
}.hover\:text-enterprise-brown:hover {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}.hover\:text-enterprise-brown\/80:hover {
  color: rgb(139 115 85 / 0.8);
}.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}.hover\:text-pure-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}.hover\:text-sanctuary:hover {
  --tw-text-opacity: 1;
  color: rgb(253 252 248 / var(--tw-text-opacity, 1));
}.hover\:text-terra:hover {
  --tw-text-opacity: 1;
  color: rgb(184 147 92 / var(--tw-text-opacity, 1));
}.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.hover\:underline:hover {
  text-decoration-line: underline;
}.hover\:no-underline:hover {
  text-decoration-line: none;
}.hover\:decoration-enterprise-brown:hover {
  text-decoration-color: #8B7355;
}.hover\:opacity-100:hover {
  opacity: 1;
}.hover\:opacity-70:hover {
  opacity: 0.7;
}.hover\:opacity-80:hover {
  opacity: 0.8;
}.hover\:opacity-90:hover {
  opacity: 0.9;
}.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-\[0_0_20px_rgba\(193\2c 155\2c 104\2c 0\.3\)\]:hover {
  --tw-shadow: 0 0 20px rgba(193,155,104,0.3);
  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-\[0_10px_30px_rgba\(139\2c 115\2c 85\2c 0\.1\)\]:hover {
  --tw-shadow: 0 10px 30px rgba(139,115,85,0.1);
  --tw-shadow-colored: 0 10px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-\[0_20px_40px_rgba\(139\2c 115\2c 85\2c 0\.15\)\]:hover {
  --tw-shadow: 0 20px 40px rgba(139,115,85,0.15);
  --tw-shadow-colored: 0 20px 40px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-\[0_6px_20px_rgba\(193\2c 155\2c 104\2c 0\.2\)\]:hover {
  --tw-shadow: 0 6px 20px rgba(193,155,104,0.2);
  --tw-shadow-colored: 0 6px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-\[0_8px_25px_rgba\(193\2c 155\2c 104\2c 0\.25\)\]:hover {
  --tw-shadow: 0 8px 25px rgba(193,155,104,0.25);
  --tw-shadow-colored: 0 8px 25px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-elegant:hover {
  --tw-shadow: 0 4px 16px rgba(139, 115, 85, 0.08);
  --tw-shadow-colored: 0 4px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-premium-shadow:hover {
  --tw-shadow: 0 8px 32px rgba(139, 115, 85, 0.12);
  --tw-shadow-colored: 0 8px 32px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-green-500\/25:hover {
  --tw-shadow-color: rgb(34 197 94 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}.hover\:shadow-premium-shadow:hover {
  --tw-shadow-color: rgba(139, 115, 85, 0.15);
  --tw-shadow: var(--tw-shadow-colored);
}.hover\:shadow-sanctuary\/20:hover {
  --tw-shadow-color: rgb(253 252 248 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}.hover\:shadow-sanctuary\/30:hover {
  --tw-shadow-color: rgb(253 252 248 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}.focus\:border-charcoal:focus {
  --tw-border-opacity: 1;
  border-color: rgb(42 39 36 / var(--tw-border-opacity, 1));
}.focus\:border-enterprise-brown:focus {
  --tw-border-opacity: 1;
  border-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}.focus\:border-enterprise-brown\/40:focus {
  border-color: rgb(139 115 85 / 0.4);
}.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}.focus\:border-terra:focus {
  --tw-border-opacity: 1;
  border-color: rgb(184 147 92 / var(--tw-border-opacity, 1));
}.focus\:bg-sanctuary:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(253 252 248 / var(--tw-bg-opacity, 1));
}.focus\:opacity-70:focus {
  opacity: 0.7;
}.focus\:opacity-80:focus {
  opacity: 0.8;
}.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-accent\/20:focus {
  --tw-ring-color: rgb(139 115 85 / 0.2);
}.focus\:ring-accent\/30:focus {
  --tw-ring-color: rgb(139 115 85 / 0.3);
}.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}.focus\:ring-enterprise-brown:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(139 115 85 / var(--tw-ring-opacity, 1));
}.focus\:ring-enterprise-brown\/10:focus {
  --tw-ring-color: rgb(139 115 85 / 0.1);
}.focus\:ring-enterprise-brown\/20:focus {
  --tw-ring-color: rgb(139 115 85 / 0.2);
}.focus\:ring-enterprise-brown\/50:focus {
  --tw-ring-color: rgb(139 115 85 / 0.5);
}.focus\:ring-green-500\/30:focus {
  --tw-ring-color: rgb(34 197 94 / 0.3);
}.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}.focus\:ring-temple-gold:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(139 115 85 / var(--tw-ring-opacity, 1));
}.focus\:ring-terra\/20:focus {
  --tw-ring-color: rgb(184 147 92 / 0.2);
}.focus\:ring-white\/50:focus {
  --tw-ring-color: rgb(255 255 255 / 0.5);
}.focus\:ring-opacity-50:focus {
  --tw-ring-opacity: 0.5;
}.focus\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}.focus\:ring-offset-1:focus {
  --tw-ring-offset-width: 1px;
}.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus-visible\:ring-offset-1:focus-visible {
  --tw-ring-offset-width: 1px;
}.active\:translate-y-0:active {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.active\:scale-95:active {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.active\:bg-sanctuary:active {
  --tw-bg-opacity: 1;
  background-color: rgb(253 252 248 / var(--tw-bg-opacity, 1));
}.active\:text-sanctuary:active {
  --tw-text-opacity: 1;
  color: rgb(253 252 248 / var(--tw-text-opacity, 1));
}.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}.disabled\:opacity-50:disabled {
  opacity: 0.5;
}.group:hover .group-hover\:visible {
  visibility: visible;
}.group:hover .group-hover\:w-full {
  width: 100%;
}.group\/btn:hover .group-hover\/btn\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group\/btn:hover .group-hover\/btn\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.group:hover .group-hover\:border-enterprise-brown\/20 {
  border-color: rgb(139 115 85 / 0.2);
}.group:hover .group-hover\:bg-enterprise-brown {
  --tw-bg-opacity: 1;
  background-color: rgb(139 115 85 / var(--tw-bg-opacity, 1));
}.group:hover .group-hover\:bg-enterprise-brown\/10 {
  background-color: rgb(139 115 85 / 0.1);
}.group:hover .group-hover\:bg-enterprise-brown\/20 {
  background-color: rgb(139 115 85 / 0.2);
}.group:hover .group-hover\:bg-opacity-30 {
  --tw-bg-opacity: 0.3;
}.group:hover .group-hover\:from-charcoal\/40 {
  --tw-gradient-from: rgb(42 39 36 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 39 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.group:hover .group-hover\:from-enterprise-brown\/20 {
  --tw-gradient-from: rgb(139 115 85 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.group:hover .group-hover\:to-terra\/20 {
  --tw-gradient-to: rgb(184 147 92 / 0.2) var(--tw-gradient-to-position);
}.group:hover .group-hover\:text-charcoal-light {
  --tw-text-opacity: 1;
  color: rgb(74 69 63 / var(--tw-text-opacity, 1));
}.group:hover .group-hover\:text-enterprise-brown {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}.group:hover .group-hover\:text-enterprise-brown\/70 {
  color: rgb(139 115 85 / 0.7);
}.group:hover .group-hover\:text-sanctuary {
  --tw-text-opacity: 1;
  color: rgb(253 252 248 / var(--tw-text-opacity, 1));
}.group:hover .group-hover\:text-terra {
  --tw-text-opacity: 1;
  color: rgb(184 147 92 / var(--tw-text-opacity, 1));
}.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}.group:hover .group-hover\:ring-enterprise-brown\/30 {
  --tw-ring-color: rgb(139 115 85 / 0.3);
}.group:hover .group-hover\:grayscale-0 {
  --tw-grayscale: grayscale(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.peer:checked ~ .peer-checked\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.peer:focus ~ .peer-focus\:ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}@media (min-width: 768px) {

  .sm\:mb-2xl {
    margin-bottom: 64px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-12 {
    height: 3rem;
  }

  .sm\:w-12 {
    width: 3rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:gap-md {
    gap: 24px;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-hero-padding {
    padding-left: 24px;
    padding-right: 24px;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}@media (min-width: 1024px) {

  .md\:left-auto {
    left: auto;
  }

  .md\:right-4 {
    right: 1rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-20 {
    margin-bottom: 5rem;
  }

  .md\:mb-2xl {
    margin-bottom: 64px;
  }

  .md\:mb-lg {
    margin-bottom: 32px;
  }

  .md\:mb-xl {
    margin-bottom: 48px;
  }

  .md\:mt-xl {
    margin-top: 48px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-20 {
    height: 5rem;
  }

  .md\:h-80 {
    height: 20rem;
  }

  .md\:h-\[340px\] {
    height: 340px;
  }

  .md\:h-\[480px\] {
    height: 480px;
  }

  .md\:h-\[580px\] {
    height: 580px;
  }

  .md\:w-2\/5 {
    width: 40%;
  }

  .md\:w-3\/5 {
    width: 60%;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:-translate-x-2 {
    --tw-translate-x: -0.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:-translate-x-8 {
    --tw-translate-x: -2rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-2 {
    --tw-translate-x: 0.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-xl {
    gap: 48px;
  }

  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:border-b-0 {
    border-bottom-width: 0px;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .md\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:py-36 {
    padding-top: 9rem;
    padding-bottom: 9rem;
  }

  .md\:py-44 {
    padding-top: 11rem;
    padding-bottom: 11rem;
  }

  .md\:text-right {
    text-align: right;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .md\:text-8xl {
    font-size: 6rem;
    line-height: 1;
  }

  .md\:text-9xl {
    font-size: 8rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}@media (min-width: 1440px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:mb-24 {
    margin-bottom: 6rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-24 {
    height: 6rem;
  }

  .lg\:h-\[85px\] {
    height: 85px;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:gap-20 {
    gap: 5rem;
  }

  .lg\:gap-2xl {
    gap: 64px;
  }

  .lg\:gap-lg {
    gap: 32px;
  }

  .lg\:gap-xl {
    gap: 48px;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:px-\[60px\] {
    padding-left: 60px;
    padding-right: 60px;
  }

  .lg\:px-hero-padding {
    padding-left: 24px;
    padding-right: 24px;
  }

  .lg\:py-36 {
    padding-top: 9rem;
    padding-bottom: 9rem;
  }

  .lg\:py-44 {
    padding-top: 11rem;
    padding-bottom: 11rem;
  }

  .lg\:py-52 {
    padding-top: 13rem;
    padding-bottom: 13rem;
  }

  .lg\:py-section {
    padding-top: 120px;
    padding-bottom: 120px;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .lg\:text-9xl {
    font-size: 8rem;
    line-height: 1;
  }

  .lg\:text-\[17px\] {
    font-size: 17px;
  }

  .lg\:text-\[8rem\] {
    font-size: 8rem;
  }

  .lg\:text-\[9rem\] {
    font-size: 9rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}@media (min-width: 1920px) {

  .xl\:block {
    display: block;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:text-\[10rem\] {
    font-size: 10rem;
  }
}.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/microinteractions.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🎯 BAKASANA - AWARD-WINNING MICROINTERACTIONS
   Inspired by Apple.com, Stripe.com, Linear.app
   ============================================= */

/* ===== PREMIUM ANIMATION SYSTEM ===== */
:root {
  /* 🎯 UNIFIED TIMING SCALE - Optimized for 9/10 UX */
  --duration-instant: 150ms;    /* Micro-interactions: hover, focus, ripples */
  --duration-quick: 250ms;      /* Small UI changes: button states, tooltips */
  --duration-medium: 350ms;     /* Medium transitions: modal open/close, cards */
  --duration-slow: 500ms;       /* Large transitions: page changes, complex animations */
  --duration-extended: 750ms;   /* Special effects: parallax, complex reveals */

  /* 🌊 PREMIUM EASING FUNCTIONS - Natural motion curves */
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);      /* Smooth, natural */
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);    /* Playful bounce */
  --ease-swift: cubic-bezier(0.4, 0, 0.2, 1);               /* Quick, decisive */
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);  /* Elastic feel */
  --ease-premium: cubic-bezier(0.16, 1, 0.3, 1);            /* Premium feel */

  /* 🎭 TRANSFORM ORIGINS */
  --origin-center: center center;
  --origin-top: center top;
  --origin-bottom: center bottom;
  --origin-left: left center;
  --origin-right: right center;

  /* 🎨 ANIMATION STATES */
  --scale-hover: 1.02;
  --scale-active: 0.98;
  --scale-focus: 1.01;
}

/* ===== BUTTON MICROINTERACTIONS ===== */
.btn-primary {position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all var(--duration-quick) var(--ease-premium);
  will-change: transform, box-shadow, background-color;
}
/* 3D hover effect */
.btn-primary:hover {
  background: linear-gradient(135deg, #C4996B 0%, #B8935C 100%);
  box-shadow: 0 10px 25px rgba(184, 147, 92, 0.25),
      0 6px 12px rgba(184, 147, 92, 0.15);
  transform: translateY(-2px) scale(var(--scale-hover));
}
/* Active state with spring back */
.btn-primary:active {
    transform: translateY(0px) scale(var(--scale-active));
    transition-duration: var(--duration-instant);
  }
/* Premium ripple effect */
.btn-primary::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                               rgba(255, 255, 255, 0.15) 0%,
                               transparent 70%);
    opacity: 0;
    transition: opacity var(--duration-quick) var(--ease-premium);
    pointer-events: none;
  }
/* Focus state with premium ring */
.btn-primary:focus-visible {
    outline: none;
    box-shadow:
      0 0 0 3px rgba(196, 153, 107, 0.3),
      0 0 0 6px rgba(196, 153, 107, 0.1);
    transform: scale(var(--scale-focus));
  }
.btn-primary:hover::before {
  opacity: 1;
}
/* Ripple effect */
.btn-primary::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    
    transform: scale(0);
    pointer-events: none;
    transition: transform var(--entrance-duration) var(--spring-elastic);
  }
.btn-primary:focus::after {
  width: 100%;
  height: 100%;
  transform: scale(1);
}

/* ===== CARD MICROINTERACTIONS ===== */
.card-interactive {position: relative;
  transform: translateZ(0);
  transition: all var(--duration-medium) var(--ease-premium);
  will-change: transform, box-shadow;
}
/* Subtle lift on hover */
.card-interactive:hover {
  box-shadow: 0 20px 40px rgba(42, 39, 36, 0.08),
      0 10px 20px rgba(42, 39, 36, 0.06);
  transform: translateY(-8px) scale(1.01);
}
/* Simplified 2D tilt effect - no 3D transforms */
.card-interactive.tilt-active {
    transition: transform var(--micro-duration) var(--spring-smooth);
  }
/* Glowing border on focus */
.card-interactive:focus-within {
    outline: none;
    box-shadow: 
      0 0 0 2px rgba(184, 147, 92, 0.2),
      0 0 0 4px rgba(184, 147, 92, 0.1),
      0 20px 40px rgba(42, 39, 36, 0.08);
  }

/* ===== LINK MICROINTERACTIONS ===== */
.link-enhanced {position: relative;
  color: var(--temple-gold);
  text-decoration: none;
  overflow: hidden;
  transition: color var(--standard-duration) var(--spring-smooth);
}
/* Animated underline */
.link-enhanced::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--temple-gold), var(--golden-amber));
  transition: width var(--standard-duration) var(--spring-smooth);
  content: '';
}
.link-enhanced:hover::after {
  width: 100%;
}
/* Shine effect */
.link-enhanced::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 70%
    );
    transform: translateX(-100%);
    transition: transform var(--entrance-duration) var(--spring-smooth);
  }

/* ===== FORM MICROINTERACTIONS ===== */
.form-field {
  position: relative;
}
.form-field input, .form-field textarea {transition: all var(--standard-duration) var(--spring-smooth);
    will-change: border-color, box-shadow, background-color;
  }
.form-field input:focus, .form-field textarea:focus {
  border-color: var(--temple-gold);
  background-color: rgba(253, 252, 248, 0.8);
  box-shadow: 0 0 0 3px rgba(184, 147, 92, 0.1),
        0 4px 12px rgba(184, 147, 92, 0.08);
  outline: none;
}
.form-field input:invalid, .form-field textarea:invalid {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}
/* Floating labels */
.form-field label {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--stone);
    pointer-events: none;
    transition: all var(--standard-duration) var(--spring-smooth);
    background: linear-gradient(to bottom, 
                               transparent 0%, 
                               transparent 40%, 
                               var(--sanctuary) 50%, 
                               var(--sanctuary) 100%);
    padding: 0 4px;
  }
.form-field input:not(:-moz-placeholder) + label {
  top: 0;
  color: var(--temple-gold);
  font-size: 12px;
  transform: translateY(-50%);
}
.form-field input:focus + label,
  .form-field input:not(:placeholder-shown) + label {
  top: 0;
  color: var(--temple-gold);
  font-size: 12px;
  transform: translateY(-50%);
}

/* ===== SCROLL REVEAL ANIMATIONS ===== */
.reveal-on-scroll {opacity: 0;
  transform: translateY(30px);
  transition: all var(--entrance-duration) var(--spring-smooth);
  will-change: opacity, transform;
}
.reveal-on-scroll.revealed {
  transform: translateY(0);
  opacity: 1;
}

.reveal-stagger {opacity: 0;
  transform: translateY(20px);
  transition: all var(--entrance-duration) var(--spring-smooth);
  will-change: opacity, transform;
}

.reveal-stagger.revealed {
  transform: translateY(0);
  opacity: 1;
}

/* Staggered delays for multiple elements */
.reveal-stagger:nth-child(1) { transition-delay: 0s; }.reveal-stagger:nth-child(2) {
  transition-delay: 0.1s;
}.reveal-stagger:nth-child(3) {
  transition-delay: 0.2s;
}.reveal-stagger:nth-child(4) {
  transition-delay: 0.3s;
}.reveal-stagger:nth-child(5) {
  transition-delay: 0.4s;
}

/* ===== MAGNETIC CURSOR EFFECTS ===== */
.magnetic-element {
  position: relative;
  cursor: none;
  transition: transform var(--micro-duration) var(--spring-smooth);
  will-change: transform;
}

.cursor-follower {position: fixed;
  width: 20px;
  height: 20px;
  background: var(--temple-gold);
  
  pointer-events: none;
  z-index: 9999;
  transition: transform var(--micro-duration) var(--spring-smooth);
  will-change: transform;
}

.cursor-follower.expanded {
  background: rgba(184, 147, 92, 0.3);
  transform: scale(2);
}

/* ===== GLASSMORPHISM EFFECTS ===== */
.glass-card {background: rgba(253, 252, 248, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all var(--standard-duration) var(--spring-smooth);
  will-change: background, backdrop-filter;
}
.glass-card:hover {
  background: rgba(253, 252, 248, 0.9);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
}

/* ===== LOADING ANIMATIONS ===== */
.loading-shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    90deg,
    var(--whisper) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    var(--whisper) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {0% {
  background-position: -200% 0;
}100% {
  background-position: 200% 0;
}
}

/* ===== PARALLAX SCROLL EFFECTS ===== */
.parallax-container {
  overflow: hidden;
  position: relative;
}.parallax-layer {
  position: absolute;
  transition: transform var(--micro-duration) linear;
  inset: 0;
  will-change: transform;
}.parallax-slow {
  transform: translateY(var(--parallax-slow, 0));
}.parallax-medium {
  transform: translateY(var(--parallax-medium, 0));
}.parallax-fast {
  transform: translateY(var(--parallax-fast, 0));
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {*,
  *::before,
  *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}
}

/* ===== FOCUS INDICATORS ===== */
.focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  
}

/* ===== SMOOTH SCROLLING ===== */
html {
  scroll-behavior: smooth;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}.will-change-opacity {
  will-change: opacity;
}.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ADVANCED LOADING ANIMATIONS ===== */
@keyframes fadeInUp {from {
  transform: translateY(30px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}

@keyframes fadeInScale {
}

@keyframes slideInRight {
}

@keyframes bounceIn {50% {
  transform: scale(1.05);
  opacity: 1;
}70% {
  transform: scale(0.9);
}
}

@keyframes float {0%, 100% {
  transform: translateY(0px);
}
}

/* ===== UTILITY ANIMATION CLASSES ===== */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}.animate-bounce-in {
  animation: bounceIn 0.6s ease-out;
}.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* ===== ENHANCED TOUCH INTERACTIONS ===== */
.touch-feedback {position: relative;
  overflow: hidden;
}
.touch-feedback::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  content: '';
  pointer-events: none;
}
.touch-feedback:active::after {
  width: 200px;
  height: 200px;
}

/* ===== PROGRESSIVE ENHANCEMENT ===== */
@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {.enhanced-glass {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}
}

/* ===== PREMIUM INTERACTIVE ELEMENTS ===== */

/* Enhanced unified card with premium interactions */
.unified-card-premium {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-medium) var(--ease-premium);
  will-change: transform, box-shadow;
  cursor: pointer;
}.unified-card-premium:hover {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12),
    0 12px 24px rgba(0, 0, 0, 0.08);
  transform: translateY(-6px) scale(var(--scale-hover));
}.unified-card-premium:active {
  transform: translateY(-3px) scale(var(--scale-active));
  transition-duration: var(--duration-instant);
}.unified-card-premium:focus-visible {
  box-shadow: 0 0 0 3px rgba(196, 153, 107, 0.3),
    0 25px 50px rgba(0, 0, 0, 0.12);
  outline: none;
}

/* Premium link with animated underline */
.link-premium {
  position: relative;
  transition: all var(--duration-quick) var(--ease-premium);
  text-decoration: none;
  display: inline-block;
}.link-premium::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--golden), var(--sunset));
  transition: width var(--duration-medium) var(--ease-elastic);
  content: '';
}.link-premium:hover {
  color: var(--golden);
  transform: translateY(-1px);
}.link-premium:hover::after {
  width: 100%;
}.link-premium:focus-visible {
  color: var(--golden);
  transform: scale(var(--scale-focus));
  outline: none;
}

/* Premium button ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}.btn-ripple::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--duration-medium) var(--ease-elastic),
              height var(--duration-medium) var(--ease-elastic);
  content: '';
}.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

@supports (container-type: inline-size) {.container-animations {
  container-type: inline-size;
}

  @container (max-width: 400px) {.container-animations .animate-fade-in-up {
  animation-duration: var(--duration-medium);
}
  }
}
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/typography.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   📝 BAKASANA - RESPONSIVE TYPOGRAPHY SYSTEM
   Fluid typography with perfect readability
   ============================================= */

/* ===== FLUID TYPOGRAPHY VARIABLES ===== */
:root {
  /* Base font size - adjustable via accessibility */
  --base-font-size: 16px;
  
  /* Typography scale - Perfect fourth (1.333) */
  --type-scale: 1.333;
  
  /* Line height system */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.7;
  
  /* Letter spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Fluid font sizes using clamp() */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  --text-6xl: clamp(4rem, 3rem + 5vw, 6rem);
  --text-7xl: clamp(6rem, 4rem + 10vw, 8rem);
}

/* ===== ACCESSIBILITY FONT SIZE ADJUSTMENTS ===== */
html[data-font-size="12"] {
  --base-font-size: 12px;
}html[data-font-size="14"] {
  --base-font-size: 14px;
}html[data-font-size="16"] {
  --base-font-size: 16px;
}html[data-font-size="18"] {
  --base-font-size: 18px;
}html[data-font-size="20"] {
  --base-font-size: 20px;
}html[data-font-size="24"] {
  --base-font-size: 24px;
}

/* ===== HIGH CONTRAST TYPOGRAPHY ===== */
html[data-high-contrast="true"] {
  --text-color: #000000;
  --text-color-light: #333333;
  --text-color-muted: #666666;
}html[data-high-contrast="true"] * {
  font-weight: var(--font-weight-medium) !important;
  text-shadow: none !important;
}

/* ===== BASE TYPOGRAPHY STYLES ===== */
html {
  font-size: var(--base-font-size);
  line-height: var(--line-height-normal);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}body {
  color: var(--charcoal);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* ===== HEADING STYLES ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-cormorant), Georgia, serif;
  font-weight: var(--font-weight-light);
  line-height: var(--line-height-tight);
  color: var(--charcoal);
  margin-bottom: 0.5em;
  letter-spacing: var(--letter-spacing-tight);
}h1 {
  margin-bottom: 0.3em;
  font-size: var(--text-6xl);
  line-height: 0.9;
  letter-spacing: var(--letter-spacing-tight);
}h2 {
  margin-bottom: 0.4em;
  font-size: var(--text-5xl);
  line-height: 1;
}h3 {
  margin-bottom: 0.5em;
  font-size: var(--text-4xl);
  line-height: 1.1;
}h4 {
  margin-bottom: 0.6em;
  font-size: var(--text-3xl);
  line-height: 1.2;
}h5 {
  margin-bottom: 0.7em;
  font-size: var(--text-2xl);
  line-height: 1.3;
}h6 {
  margin-bottom: 0.8em;
  font-size: var(--text-xl);
  line-height: 1.4;
}

/* ===== PARAGRAPH STYLES ===== */
p {
  font-size: var(--text-base);
  line-height: var(--line-height-relaxed);
  color: var(--charcoal);
  margin-bottom: 1.5em;
  max-width: 65ch; /* Optimal reading width */
}p.lead {
  margin-bottom: 2em;
  color: var(--stone);
  font-size: var(--text-lg);
  line-height: var(--line-height-relaxed);
}p.small {
  color: var(--stone);
  font-size: var(--text-sm);
  line-height: var(--line-height-normal);
}

/* ===== LINK STYLES ===== */
a {
  color: var(--temple-gold);
  text-decoration: none;
  transition: color 0.3s ease;
  position: relative;
}a:hover {
  color: var(--golden-amber);
}a:focus {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== LIST STYLES ===== */
ul, ol {
  margin-bottom: 1.5em;
  padding-left: 1.5em;
}li {
  margin-bottom: 0.5em;
  font-size: var(--text-base);
  line-height: var(--line-height-normal);
}

/* ===== BLOCKQUOTE STYLES ===== */
blockquote {
  font-family: var(--font-cormorant), Georgia, serif;
  font-size: var(--text-xl);
  font-style: italic;
  line-height: var(--line-height-relaxed);
  color: var(--stone);
  margin: 2em 0;
  padding: 1.5em;
  border-left: 4px solid var(--temple-gold);
  background: var(--whisper);
  position: relative;
}blockquote::before {
  position: absolute;
  top: -0.5em;
  left: 0.5em;
  color: var(--temple-gold);
  font-size: 3em;
  line-height: 1;
  content: '"';
}

/* ===== CODE STYLES ===== */
code, pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--text-sm);
  background: var(--rice);
  
}code {
  padding: 0.2em 0.4em;
  color: var(--charcoal);
}pre {
  overflow-x: auto;
  margin: 1.5em 0;
  padding: 1em;
  border: 1px solid var(--stone-light);
}

/* ===== UTILITY CLASSES ===== */
.text-xs { font-size: var(--text-xs); }.text-sm {
  font-size: var(--text-sm);
}.text-base {
  font-size: var(--text-base);
}.text-lg {
  font-size: var(--text-lg);
}.text-xl {
  font-size: var(--text-xl);
}.text-2xl {
  font-size: var(--text-2xl);
}.text-3xl {
  font-size: var(--text-3xl);
}.text-4xl {
  font-size: var(--text-4xl);
}.text-5xl {
  font-size: var(--text-5xl);
}.text-6xl {
  font-size: var(--text-6xl);
}.text-7xl {
  font-size: var(--text-7xl);
}.font-light {
  font-weight: var(--font-weight-light);
}.font-normal {
  font-weight: var(--font-weight-normal);
}.font-medium {
  font-weight: var(--font-weight-medium);
}.font-semibold {
  font-weight: var(--font-weight-semibold);
}.font-bold {
  font-weight: var(--font-weight-bold);
}.leading-tight {
  line-height: var(--line-height-tight);
}.leading-normal {
  line-height: var(--line-height-normal);
}.leading-relaxed {
  line-height: var(--line-height-relaxed);
}.tracking-tight {
  letter-spacing: var(--letter-spacing-tight);
}.tracking-normal {
  letter-spacing: var(--letter-spacing-normal);
}.tracking-wide {
  letter-spacing: var(--letter-spacing-wide);
}.tracking-wider {
  letter-spacing: var(--letter-spacing-wider);
}.tracking-widest {
  letter-spacing: var(--letter-spacing-widest);
}.font-cormorant {
  font-family: var(--font-cormorant), Georgia, serif;
}.font-sans {
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* ===== RESPONSIVE OVERRIDES ===== */
@media (max-width: 768px) {:root {
  --text-4xl: clamp(2rem, 5vw, 2.5rem);
  --text-5xl: clamp(2.5rem, 6vw, 3rem);
  --text-6xl: clamp(3rem, 8vw, 4rem);
}h1, h2, h3, h4, h5, h6 {
  line-height: 1.1;
}p {
  line-height: 1.6;
}blockquote {
  margin: 1.5em 0;
  padding: 1em;
  font-size: var(--text-lg);
}
}

@media (max-width: 480px) {ul, ol {
  padding-left: 1em;
}
}

/* ===== PRINT STYLES ===== */
@media print {* {
  background: transparent !important;
  box-shadow: none !important;
  color: black !important;
  text-shadow: none !important;
}p, blockquote {
  orphans: 3;
  widows: 3;
}a {
  color: black !important;
  text-decoration: underline;
}a[href^="http"]:after {
  font-size: 0.8em;
  content: " (" attr(href) ")";
}
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
}

/* Focus visible for keyboard navigation */
html[data-keyboard-navigation="true"] *:focus {
  outline: 2px solid var(--temple-gold) !important;
  outline-offset: 2px !important;
  
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  
  z-index: 1000;
  transition: top 0.3s ease;
}.skip-link:focus {
  top: 6px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/advanced-grid.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🏗️ BAKASANA ADVANCED GRID SYSTEM
   Perfect 10/10 Responsive Layout System
   ============================================= */

/* ===== CONTAINER QUERIES SUPPORT ===== */
@supports (container-type: inline-size) {.grid-container {
  container-type: inline-size;
}
}

/* ===== RESPONSIVE GRID SYSTEM ===== */

/* Auto-fit Grid - Automatically adjusts columns based on content */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width, 280px), 1fr));
  gap: clamp(1rem, 4vw, 2rem);
  width: 100%;
}

/* Auto-fill Grid - Maintains empty columns */
.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--min-column-width, 280px), 1fr));
  gap: clamp(1rem, 4vw, 2rem);
  width: 100%;
}

/* Responsive Grid Variants */
.grid-responsive-cards {
  --min-column-width: 300px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width), 1fr));
  gap: clamp(1.5rem, 5vw, 3rem);
  align-items: start;
}.grid-responsive-services {
  display: grid;
  align-items: stretch;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width), 1fr));
  --min-column-width: 280px;
  gap: clamp(2rem, 6vw, 4rem);
}.grid-responsive-testimonials {
  display: grid;
  align-items: start;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width), 1fr));
  --min-column-width: 320px;
  gap: clamp(1.5rem, 4vw, 2.5rem);
}.grid-responsive-gallery {
  display: grid;
  align-items: center;
  grid-template-columns: repeat(auto-fill, minmax(var(--min-column-width), 1fr));
  --min-column-width: 250px;
  gap: clamp(1rem, 3vw, 1.5rem);
}

/* ===== CONTAINER QUERY RESPONSIVE GRIDS ===== */
@supports (container-type: inline-size) {.grid-container-responsive {
  display: grid;
  container-type: inline-size;
  gap: clamp(1rem, 4vw, 2rem);
}

  /* Small container - single column */
  @container (max-width: 400px) {
  }

  /* Medium container - two columns */
  @container (min-width: 401px) and (max-width: 800px) {
  }

  /* Large container - three columns */
  @container (min-width: 801px) and (max-width: 1200px) {
  }

  /* Extra large container - four columns */
  @container (min-width: 1201px) {
  }
}

/* ===== SPECIALIZED GRID LAYOUTS ===== */

/* Masonry-style Grid */
.grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-template-rows: masonry; /* Future CSS feature */
  gap: clamp(1rem, 4vw, 2rem);
  align-items: start;
}

/* Fallback for browsers without masonry support */
@supports not (grid-template-rows: masonry) {.grid-masonry {
  display: grid;
  align-items: start;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: clamp(1rem, 4vw, 2rem);
}
}

/* Hero Grid - Special layout for hero sections */
.grid-hero {
  display: grid;
  grid-template-columns: 1fr;
  gap: clamp(2rem, 8vw, 4rem);
  align-items: center;
  min-height: 100vh;
  padding: clamp(2rem, 8vw, 6rem) 0;
}

@media (min-width: 1024px) {.grid-hero {
  grid-template-columns: 1fr 1fr;
  gap: clamp(4rem, 10vw, 8rem);
}
}

/* Stats Grid - Perfect for statistics display */
.grid-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: clamp(1rem, 4vw, 2rem);
  text-align: center;
}

@media (min-width: 768px) {.grid-stats {
  grid-template-columns: repeat(4, 1fr);
  gap: clamp(2rem, 6vw, 4rem);
}
}

/* ===== GRID UTILITIES ===== */

/* Gap utilities */
.gap-responsive-sm { gap: clamp(0.5rem, 2vw, 1rem); }.gap-responsive-md {
  gap: clamp(1rem, 4vw, 2rem);
}.gap-responsive-lg {
  gap: clamp(1.5rem, 6vw, 3rem);
}.gap-responsive-xl {
  gap: clamp(2rem, 8vw, 4rem);
}

/* Alignment utilities */
.grid-items-start { align-items: start; }.grid-items-center {
  align-items: center;
}.grid-items-end {
  align-items: end;
}.grid-items-stretch {
  align-items: stretch;
}.grid-content-start {
  justify-content: start;
}.grid-content-center {
  justify-content: center;
}.grid-content-end {
  justify-content: end;
}.grid-content-between {
  justify-content: space-between;
}

/* ===== RESPONSIVE BREAKPOINT OVERRIDES ===== */

/* Mobile-first approach with progressive enhancement */
@media (max-width: 480px) {.grid-auto-fit,
  .grid-auto-fill,
  .grid-responsive-cards,
  .grid-responsive-services,
  .grid-responsive-testimonials {
  grid-template-columns: 1fr;
  gap: clamp(1rem, 6vw, 1.5rem);
}
}

@media (min-width: 481px) and (max-width: 768px) {
}

@media (min-width: 769px) and (max-width: 1024px) {.grid-responsive-cards {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {.grid-auto-fit,
  .grid-auto-fill,
  .grid-responsive-cards,
  .grid-responsive-services,
  .grid-responsive-testimonials,
  .grid-responsive-gallery {
  transition: none;
}
}

/* High contrast mode support */
@media (prefers-contrast: high) {
}

/* ===== PRINT STYLES ===== */
@media print {
}

/* ===== FUTURE-PROOF FEATURES ===== */

/* CSS Grid Level 3 - Subgrid support */
@supports (grid-template-rows: subgrid) {.grid-subgrid-rows {
  grid-template-rows: subgrid;
}.grid-subgrid-columns {
  grid-template-columns: subgrid;
}
}

/* CSS Grid Level 4 - Grid template areas enhancement */
.grid-semantic {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar main aside"
    "footer footer footer";
  grid-template-columns: 200px 1fr 200px;
  grid-template-rows: auto 1fr auto;
  gap: clamp(1rem, 4vw, 2rem);
  min-height: 100vh;
}

@media (max-width: 768px) {.grid-semantic {
  grid-template-rows: auto auto auto auto auto;
  grid-template-columns: 1fr;
  grid-template-areas: "header"
      "main"
      "sidebar"
      "aside"
      "footer";
}
}
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/touch-targets.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   👆 BAKASANA TOUCH TARGET OPTIMIZATION
   WCAG 2.1 AA Compliant Touch Targets
   ============================================= */

/* ===== TOUCH TARGET STANDARDS ===== */
:root {
  /* WCAG 2.1 AA minimum touch target size */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 52px;
  --touch-target-xl: 56px;
  
  /* Touch target spacing */
  --touch-spacing-min: 8px;
  --touch-spacing-comfortable: 12px;
  --touch-spacing-large: 16px;
}

/* ===== BASE TOUCH TARGET CLASSES ===== */

/* Minimum compliant touch target */
.touch-target {
  min-width: var(--touch-target-min);
  min-height: var(--touch-target-min);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Comfortable touch target */
.touch-target-comfortable {
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Large touch target */
.touch-target-large {
  min-width: var(--touch-target-large);
  min-height: var(--touch-target-large);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Extra large touch target */
.touch-target-xl {
  min-width: var(--touch-target-xl);
  min-height: var(--touch-target-xl);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* ===== INTERACTIVE ELEMENT OPTIMIZATION ===== */

/* Buttons */
button,
.btn,
[role="button"] {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: 0.75rem 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Links */
a {
  min-height: var(--touch-target-min);
  display: inline-flex;
  align-items: center;
  padding: 0.5rem;
  margin: -0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}a:hover,
a:focus {
  background-color: rgba(139, 115, 85, 0.1);
}

/* Form inputs */
input,
textarea,
select {
  min-height: var(--touch-target-min);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
}

/* Checkboxes and radio buttons */
input[type="checkbox"],
input[type="radio"] {
  min-width: var(--touch-target-min);
  min-height: var(--touch-target-min);
  transform: scale(1.5);
  margin: 0.5rem;
}

/* ===== NAVIGATION TOUCH TARGETS ===== */

/* Desktop navigation links */
.nav-link {
  min-height: var(--touch-target-comfortable);
  padding: 0.75rem 1rem;
  display: inline-flex;
  align-items: center;
  margin: 0 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

/* Mobile navigation links */
.mobile-nav-link {
  min-height: var(--touch-target-comfortable);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 0.25rem;
  margin: 0.25rem 0;
  transition: all 0.2s ease;
}

/* Hamburger menu button */
.hamburger-button {
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

/* ===== CARD TOUCH TARGETS ===== */

/* Clickable cards */
.card-clickable {
  min-height: var(--touch-target-min);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
}.card-clickable:hover,
.card-clickable:focus {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* ===== SOCIAL MEDIA TOUCH TARGETS ===== */

/* Social media icons */
.social-icon {
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  padding: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0.25rem;
  transition: all 0.2s ease;
}

/* WhatsApp floating button */
.whatsapp-float {
  min-width: var(--touch-target-xl);
  min-height: var(--touch-target-xl);
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* ===== FOOTER TOUCH TARGETS ===== */

/* Footer links */
.footer-link {
  min-height: var(--touch-target-min);
  padding: 0.75rem 0.5rem;
  display: inline-flex;
  align-items: center;
  margin: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

/* Footer contact buttons */
.footer-contact-button {
  min-height: var(--touch-target-comfortable);
  padding: 1rem 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.5rem;
  margin: 0.5rem;
  transition: all 0.2s ease;
}

/* ===== RESPONSIVE TOUCH TARGET ADJUSTMENTS ===== */

/* Mobile devices - larger touch targets */
@media (max-width: 768px) {:root {
  --touch-target-comfortable: 52px;
  --touch-target-large: 56px;
  --touch-target-min: 48px;
  --touch-target-xl: 60px;
}
  
  /* Increase spacing between touch targets */
  .mobile-nav-link {
    margin: 0.5rem 0;
    padding: 1.25rem 1.5rem;
  }
  
  /* Larger social icons on mobile */
  .social-icon {
    min-width: var(--touch-target-large);
    min-height: var(--touch-target-large);
    padding: 1rem;
  }
  
  /* Larger footer links on mobile */
  .footer-link {
    padding: 1rem 0.75rem;
    margin: 0.5rem 0.25rem;
  }
}

/* Tablet devices - medium touch targets */
@media (min-width: 769px) and (max-width: 1024px) {
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Focus indicators for touch targets */
.touch-target:focus,
.touch-target-comfortable:focus,
.touch-target-large:focus,
.touch-target-xl:focus,
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--enterprise-brown);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(139, 115, 85, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {.touch-target,
  .touch-target-comfortable,
  .touch-target-large,
  .touch-target-xl,
  button,
  a,
  input,
  textarea,
  select {
  border: 2px solid currentColor;
}
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {.touch-target,
  .touch-target-comfortable,
  .touch-target-large,
  .touch-target-xl,
  button,
  a,
  .card-clickable,
  .social-icon,
  .whatsapp-float {
  transition: none;
}
}

/* ===== TOUCH TARGET SPACING UTILITIES ===== */

/* Spacing between touch targets */
.touch-spacing {
  gap: var(--touch-spacing-min);
}.touch-spacing-comfortable {
  gap: var(--touch-spacing-comfortable);
}.touch-spacing-large {
  gap: var(--touch-spacing-large);
}

/* Margin utilities for touch targets */
.touch-margin {
  margin: var(--touch-spacing-min);
}.touch-margin-comfortable {
  margin: var(--touch-spacing-comfortable);
}.touch-margin-large {
  margin: var(--touch-spacing-large);
}

/* ===== PRINT STYLES ===== */
@media print {.touch-target,
  .touch-target-comfortable,
  .touch-target-large,
  .touch-target-xl {
  min-width: auto;
  min-height: auto;
  padding: 0.25rem 0.5rem;
}
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Skip links */
.skip-links {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 9999;
}.skip-link {
  position: absolute;
  top: -100px;
  left: 0;
  display: flex;
  align-items: center;
  min-height: var(--touch-target-min);
  padding: 0.75rem 1rem;
  border-radius: 0 0 0.25rem 0;
  background: var(--enterprise-brown);
  color: var(--sanctuary);
  font-weight: 500;
  text-decoration: none;
  transition: top 0.3s ease;
}.skip-link:focus {
  top: 0;
  outline: 2px solid var(--sanctuary);
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode */
.high-contrast {
  --charcoal: #000000;
  --sanctuary: #ffffff;
  --enterprise-brown: #0066cc;
  --stone: #666666;
  --sage: #333333;
}.high-contrast * {
  box-shadow: 0 0 0 1px currentColor !important;
  text-shadow: none !important;
}.high-contrast img {
  filter: contrast(1.2) brightness(1.1);
}

/* Reduced motion */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Focus visible enhancement */
.focus-visible-enabled *:focus-visible {
  outline: 3px solid var(--enterprise-brown);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(139, 115, 85, 0.2);
}

/* Enhanced focus indicators for interactive elements */
.focus-visible-enabled button:focus-visible,
.focus-visible-enabled a:focus-visible,
.focus-visible-enabled input:focus-visible,
.focus-visible-enabled textarea:focus-visible,
.focus-visible-enabled select:focus-visible {
  outline: 3px solid var(--enterprise-brown);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(139, 115, 85, 0.2);
  position: relative;
  z-index: 10;
}

/* Keyboard navigation indicators */
.keyboard-navigation-active {
  outline: 2px dashed var(--enterprise-brown);
  outline-offset: 4px;
}

/* ARIA live regions */
[aria-live] {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Enhanced button states for accessibility */
button[aria-pressed="true"] {
  background-color: var(--enterprise-brown);
  color: var(--sanctuary);
}button[aria-expanded="true"] {
  background-color: var(--stone);
}

/* Enhanced form accessibility */
input:invalid,
textarea:invalid,
select:invalid {
  border: 2px solid #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}input:valid,
textarea:valid,
select:valid {
  border: 2px solid #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.2);
}

/* Required field indicators */
[required]::after {
  content: " *";
  color: #e74c3c;
  font-weight: bold;
}

/* Error message styling */
.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}.error-message::before {
  font-weight: bold;
  content: "⚠";
}

/* Success message styling */
.success-message {
  color: #27ae60;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}.success-message::before {
  font-weight: bold;
  content: "✓";
}
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/advanced-typography.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   📝 BAKASANA ADVANCED TYPOGRAPHY SYSTEM
   Perfect 10/10 Responsive Typography
   ============================================= */

/* ===== ADVANCED FLUID TYPOGRAPHY ===== */
:root {
  /* Perfect fluid typography scale using modern CSS */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.375rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.625rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3.25rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4.5rem);
  --text-6xl: clamp(4rem, 3rem + 5vw, 7rem);
  --text-7xl: clamp(5rem, 4rem + 8vw, 10rem);
  
  /* Advanced line height system */
  --leading-none: 1;
  --leading-tight: 1.1;
  --leading-snug: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Responsive letter spacing */
  --tracking-tighter: clamp(-0.05em, -0.02vw, -0.025em);
  --tracking-tight: clamp(-0.025em, -0.01vw, -0.0125em);
  --tracking-normal: 0;
  --tracking-wide: clamp(0.025em, 0.01vw, 0.05em);
  --tracking-wider: clamp(0.05em, 0.02vw, 0.1em);
  --tracking-widest: clamp(0.1em, 0.05vw, 0.25em);
}

/* ===== RESPONSIVE TYPOGRAPHY CLASSES ===== */

/* Fluid headings */
.heading-display {
  font-size: var(--text-7xl);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-tighter);
  font-weight: 200;
}.heading-hero {
  font-size: var(--text-6xl);
  font-weight: 300;
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
}.heading-section {
  font-size: var(--text-5xl);
  font-weight: 300;
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-normal);
}.heading-subsection {
  font-size: var(--text-4xl);
  font-weight: 400;
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-normal);
}.heading-card {
  font-size: var(--text-3xl);
  font-weight: 400;
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-normal);
}.heading-small {
  font-size: var(--text-2xl);
  font-weight: 500;
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

/* Fluid body text */
.text-lead {
  font-size: var(--text-xl);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  font-weight: 300;
}.text-body {
  font-size: var(--text-base);
  font-weight: 400;
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
}.text-small {
  font-size: var(--text-sm);
  font-weight: 400;
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}.text-caption {
  font-size: var(--text-xs);
  font-weight: 500;
  line-height: var(--leading-normal);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
}

/* ===== RESPONSIVE TYPOGRAPHY UTILITIES ===== */

/* Responsive margins */
.text-spacing-tight {
  margin-bottom: clamp(0.5rem, 2vw, 1rem);
}.text-spacing-normal {
  margin-bottom: clamp(1rem, 3vw, 1.5rem);
}.text-spacing-relaxed {
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
}.text-spacing-loose {
  margin-bottom: clamp(2rem, 5vw, 3.5rem);
}

/* Responsive text alignment */
.text-responsive-center {
  text-align: center;
}

@media (min-width: 768px) {.text-responsive-left {
  text-align: left;
}.text-responsive-right {
  text-align: right;
}
}

/* ===== ADVANCED RESPONSIVE RULES ===== */

/* Container-based typography */
@supports (container-type: inline-size) {.typography-container {
  container-type: inline-size;
}
  
  @container (max-width: 400px) {.typography-container .heading-hero {
  font-size: clamp(2rem, 8vw, 3rem);
  line-height: 1.1;
}.typography-container .text-lead {
  font-size: clamp(1rem, 4vw, 1.125rem);
}
  }
  
  @container (min-width: 401px) and (max-width: 800px) {
  }
  
  @container (min-width: 801px) {
  }
}

/* ===== DEVICE-SPECIFIC OPTIMIZATIONS ===== */

/* Mobile typography optimizations */
@media (max-width: 480px) {:root {
  --text-3xl: clamp(1.5rem, 5vw, 2rem);
  --text-4xl: clamp(1.75rem, 6vw, 2.5rem);
  --text-5xl: clamp(2rem, 8vw, 3rem);
  --text-6xl: clamp(2.5rem, 10vw, 4rem);
}
  
  /* Tighter line heights on mobile */
  .heading-display,
  .heading-hero,
  .heading-section {
    line-height: var(--leading-tight);
  }
  
  /* Reduced letter spacing on mobile */
  .heading-display,
  .heading-hero {
    letter-spacing: var(--tracking-normal);
  }
}

/* Tablet typography optimizations */
@media (min-width: 481px) and (max-width: 1024px) {
}

/* Large screen optimizations */
@media (min-width: 1440px) {
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Respect user font size preferences */
@media (prefers-reduced-motion: reduce) {* {
  transition: none !important;
  animation: none !important;
}
}

/* High contrast mode */
@media (prefers-contrast: high) {.heading-display,
  .heading-hero,
  .heading-section,
  .heading-subsection,
  .heading-card,
  .heading-small {
  font-weight: 600;
  text-shadow: none;
}
}

/* Forced colors mode */
@media (forced-colors: active) {.heading-display,
  .heading-hero,
  .heading-section {
  color: CanvasText;
}
}

/* ===== READING EXPERIENCE OPTIMIZATIONS ===== */

/* Optimal reading width */
.reading-width {
  max-width: clamp(45ch, 75vw, 75ch);
  margin-left: auto;
  margin-right: auto;
}

/* Improved readability */
.reading-optimized {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== PRINT TYPOGRAPHY ===== */
@media print {.text-lead,
  .text-body {
  orphans: 2;
  widows: 2;
}
}

/* ===== FUTURE-PROOF FEATURES ===== */

/* CSS Fonts Level 4 - Variable fonts support */
@supports (font-variation-settings: normal) {.variable-font {
  font-variation-settings: "wght" 400,
      "slnt" 0,
      "ital" 0;
}.variable-font-light {
  font-variation-settings: "wght" 300,
      "slnt" 0,
      "ital" 0;
}.variable-font-bold {
  font-variation-settings: "wght" 600,
      "slnt" 0,
      "ital" 0;
}
}

/* CSS Text Level 4 - Text spacing */
@supports (text-spacing: trim-start) {.text-spacing-optimized {
  text-spacing: trim-start;
}
}
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/premium-utilities.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🏆 BAKASANA - PREMIUM UTILITIES FOR 9/10 UX
   Advanced utilities for world-class experience
   ============================================= */

/* ===== PREMIUM ANIMATIONS ===== */
.animate-premium-fade-in {
  animation: premiumFadeIn var(--duration-medium) var(--ease-premium) forwards;
}.animate-premium-slide-up {
  animation: premiumSlideUp var(--duration-medium) var(--ease-premium) forwards;
}.animate-premium-scale-in {
  animation: premiumScaleIn var(--duration-quick) var(--ease-premium) forwards;
}

@keyframes premiumFadeIn {from {
  transform: translateY(20px) scale(0.98);
  opacity: 0;
}to {
  transform: translateY(0) scale(1);
  opacity: 1;
}
}

@keyframes premiumSlideUp {
}

@keyframes premiumScaleIn {
}

/* ===== PREMIUM HOVER EFFECTS ===== */
.hover-lift {
  transition: all var(--duration-quick) var(--ease-premium);
}.hover-lift:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px) scale(1.02);
}.hover-glow {
  transition: all var(--duration-quick) var(--ease-premium);
}.hover-glow:hover {
  box-shadow: 0 0 30px rgba(196, 153, 107, 0.3);
}.hover-magnetic {
  transition: transform var(--duration-instant) var(--ease-swift);
}

/* ===== PREMIUM FOCUS STATES ===== */
.focus-premium {
  outline: none;
  transition: all var(--duration-instant) var(--ease-swift);
}.focus-premium:focus-visible {
  box-shadow: 0 0 0 3px rgba(196, 153, 107, 0.3),
    0 0 0 6px rgba(196, 153, 107, 0.1);
  transform: scale(1.01);
}

/* ===== PREMIUM LOADING STATES ===== */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(253, 252, 248, 0.2) 0%,
    rgba(253, 252, 248, 0.5) 50%,
    rgba(253, 252, 248, 0.2) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}.loading-pulse-premium {
  animation: premiumPulse 2s ease-in-out infinite;
}

@keyframes premiumPulse {0%, 100% {
  transform: scale(1);
  opacity: 0.8;
}50% {
  transform: scale(1.02);
  opacity: 1;
}
}

/* ===== PREMIUM GLASS EFFECTS ===== */
.glass-premium {
  background: rgba(253, 252, 248, 0.8);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
          backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}.glass-dark {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(42, 39, 36, 0.8);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
          backdrop-filter: blur(20px) saturate(180%);
}

/* ===== PREMIUM GRADIENTS ===== */
.gradient-premium {
  background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F3 50%, #F5F2ED 100%);
}.gradient-warm {
  background: linear-gradient(135deg, #C19B68 0%, #B8935C 50%, #A67C52 100%);
}.gradient-sunset {
  background: linear-gradient(135deg, #E6C18A 0%, #D4AF7A 50%, #C19B68 100%);
}

/* ===== PREMIUM SHADOWS ===== */
.shadow-premium {
  box-shadow: 
    0 4px 16px rgba(139, 115, 85, 0.08),
    0 8px 32px rgba(139, 115, 85, 0.04);
}.shadow-premium-hover {
  box-shadow: 0 8px 32px rgba(139, 115, 85, 0.12),
    0 16px 64px rgba(139, 115, 85, 0.06);
}.shadow-premium-focus {
  box-shadow: 0 0 0 3px rgba(196, 153, 107, 0.3),
    0 8px 32px rgba(139, 115, 85, 0.12);
}

/* ===== PREMIUM TYPOGRAPHY ===== */
.text-premium {
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}.text-balance {
  text-wrap: balance;
}.text-pretty {
  text-wrap: pretty;
}

/* ===== PREMIUM CONTAINERS ===== */
.container-premium {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 clamp(1rem, 5vw, 3rem);
}.container-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 clamp(1rem, 5vw, 2rem);
}

/* ===== PREMIUM GRID SYSTEMS ===== */
.grid-premium {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: clamp(1rem, 3vw, 2rem);
}.grid-masonry {
  -moz-column-fill: balance;
       column-fill: balance;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  -moz-column-width: 300px;
       column-width: 300px;
  -moz-columns: auto;
       columns: auto;
}.grid-masonry > * {
  margin-bottom: 2rem;
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}

/* ===== PREMIUM SCROLL EFFECTS ===== */
.scroll-smooth-premium {
  scroll-behavior: smooth;
  scroll-padding-top: 100px;
}.scroll-snap-premium {
  scroll-snap-type: y mandatory;
}.scroll-snap-premium > * {
  scroll-snap-align: start;
}

/* ===== PREMIUM ACCESSIBILITY ===== */
.sr-only-premium {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}.skip-link-premium {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: 9999;
  padding: 8px;
  background: var(--charcoal);
  color: var(--silk);
  text-decoration: none;
  transition: top var(--duration-quick) var(--ease-premium);
}.skip-link-premium:focus {
  top: 6px;
}

/* ===== PREMIUM PERFORMANCE ===== */
.will-change-premium {
  will-change: transform, opacity;
}.contain-premium {
  contain: layout style paint;
}.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== PREMIUM RESPONSIVE ===== */
@media (max-width: 768px) {.mobile-optimized {
  font-size: clamp(0.875rem, 4vw, 1rem);
  line-height: 1.6;
  letter-spacing: 0.01em;
}
}

@media (min-width: 1920px) {.desktop-enhanced {
  font-size: clamp(1rem, 1.2vw, 1.125rem);
}
}

/* ===== PREMIUM PRINT STYLES ===== */
@media print {.print-hidden {
  display: none !important;
}.print-optimized {
  background: white !important;
  box-shadow: none !important;
  color: black !important;
}
}

/* ===== PREMIUM REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {.respect-motion-preference {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}
}
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/navbar-fix.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🔧 NAVBAR CONTRAST FIX - Desktop Navigation
   Ensures proper visibility and contrast for desktop navbar links
   ============================================= */

/* Desktop navbar link contrast enhancement */
@media (min-width: 1024px) {
  /* Base desktop navbar styles */
  .desktop-nav-link {
    color: #2F2B28 !important; /* Slightly darker for better contrast */
    font-weight: 450 !important; /* Slightly bolder for better visibility */
    opacity: 1 !important;
    text-shadow: none !important;
    padding: 6px 12px !important; /* Add subtle padding for better click area */
    border-radius: 4px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* Hover state with gold color */
  .desktop-nav-link:hover {
    color: #D4AF37 !important; /* Gold hover color to match mobile */
    transform: translateY(-1px) !important;
    background: rgba(212, 175, 55, 0.06) !important; /* Subtle background on hover */
  }

  /* Active state */
  .desktop-nav-link.active {
    color: #D4AF37 !important; /* Gold for active state */
    font-weight: 500 !important;
    background: rgba(212, 175, 55, 0.08) !important; /* Subtle background for active state */
  }

  /* Special styling for highlighted links */
  .desktop-nav-link.highlight {
    color: #FFFFFF !important; /* White text for better contrast */
    background: linear-gradient(135deg, #8B7355, #A68B5B) !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(139, 115, 85, 0.2) !important;
  }.desktop-nav-link.highlight:hover {
  background: linear-gradient(135deg, #A68B5B, #B8956C) !important;
  box-shadow: 0 3px 12px rgba(139, 115, 85, 0.25) !important;
  color: #FFFFFF !important;
  transform: translateY(-1px) !important;
}
}

/* Enhanced navbar background for luxury feel */
@media (min-width: 1024px) {.navbar-scrolled {
  border-bottom: 1px solid rgba(212, 175, 55, 0.15) !important;
  background: rgba(255, 253, 250, 0.95) !important;
  box-shadow: 0 2px 20px rgba(139, 115, 85, 0.08) !important;
  -webkit-backdrop-filter: blur(24px) !important;
  /* Warm white with slight tint */
    backdrop-filter: blur(24px) !important;
}.navbar-transparent {
  background: rgba(255, 253, 250, 0.25) !important;
  box-shadow: 0 1px 8px rgba(139, 115, 85, 0.04) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  /* Subtle warm transparency */
    backdrop-filter: blur(12px) !important;
}
}

/* Logo contrast enhancement */
@media (min-width: 1024px) {.navbar-logo {
  color: #2A2622 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  /* Darker for better contrast */
    font-weight: 400 !important;
}.navbar-logo:hover {
  color: #8B7355 !important;
  text-shadow: 0 2px 4px rgba(139, 115, 85, 0.15) !important;
  transform: scale(1.02) !important;
}
}

/* Mobile menu - for devices below 1024px */
@media (max-width: 1023px) {.mobile-nav-link {
  color: #3A3633 !important;
}.mobile-nav-link:hover {
  color: #D4AF37 !important;
}.mobile-nav-link.active {
  color: #D4AF37 !important;
}

  /* Mobile navbar background with warm tint */
  .navbar-scrolled {
    background: rgba(255, 253, 250, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 2px 15px rgba(139, 115, 85, 0.08) !important;
  }
}

/* Accessibility enhancements */
@media (min-width: 1024px) {.desktop-nav-link:focus {
  color: #D4AF37 !important;
  outline: 2px solid #D4AF37 !important;
  outline-offset: 2px !important;
}
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {.desktop-nav-link {
  color: #000000 !important;
}.desktop-nav-link:hover,
    .desktop-nav-link.active {
  color: #B8860B !important;
}
  }
}

/* Animation enhancements for better UX */
@media (min-width: 1024px) {
  
  /* Underline animation for active/hover states */
  .nav-underline {
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    opacity: 0;
    transform: scaleX(0);
    transition: all 0.3s ease;
  }.desktop-nav-link:hover .nav-underline,
  .desktop-nav-link.active .nav-underline {
  transform: scaleX(1);
  opacity: 1;
}
}

/* =============================================
   🚨 CRITICAL FIX - Force navbar visibility
   High specificity overrides to ensure navbar works
   ============================================= */

/* Force desktop menu visibility on desktop screens */
@media (min-width: 1024px) {nav .hidden.md\\:flex {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}
  
  /* Hide mobile elements on desktop */
  nav .md\\:hidden {
    display: none !important;
  }
  
  /* Ensure WhatsApp button is visible on desktop */
  nav .hidden.md\\:block {
    display: block !important;
    visibility: visible !important;
  }
}

/* Force mobile menu visibility on mobile screens */
@media (max-width: 1023px) {nav .md\\:hidden {
  display: block !important;
  visibility: visible !important;
}
  
  /* Hide desktop elements on mobile */
  nav .hidden.md\\:flex,
  nav .hidden.md\\:block {
    display: none !important;
  }
  
  /* Mobile menu container */
  nav .md\\:hidden.absolute {
    position: absolute !important;
  }
}

/* Additional safety overrides */
.navbar-logo {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure hamburger button works */
button[aria-label="Toggle menu"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@media (min-width: 1024px) {button[aria-label="Toggle menu"] {
  display: none !important;
}
}
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/navbar-critical-fix.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/* =============================================
   🚨 CRITICAL NAVBAR FIX - HIGHEST PRIORITY
   This file must be loaded LAST to override all conflicts
   ============================================= */

/* CRITICAL: Force navbar visibility with maximum specificity */
html body nav .hidden.md\\:flex,
html body nav div.hidden.md\\:flex {
  display: flex;
  visibility: visible;
  opacity: 1;
}html body nav .hidden.md\\:block,
html body nav div.hidden.md\\:block {
  display: block;
  visibility: visible;
  opacity: 1;
}

/* CRITICAL: Desktop breakpoint enforcement */
@media screen and (min-width: 1024px) {
  /* Force desktop menu to show */
  nav [class*="hidden"][class*="md:flex"] {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Force WhatsApp button to show */
  nav [class*="hidden"][class*="md:block"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Force mobile elements to hide */
  nav [class*="md:hidden"] {
    display: none !important;
  }
  
  /* Ensure hamburger is hidden */
  nav button[aria-label="Toggle menu"] {
    display: none !important;
  }
}

/* CRITICAL: Mobile breakpoint enforcement */
@media screen and (max-width: 1023px) {
  /* Force mobile menu elements to show */
  nav [class*="md:hidden"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Force desktop elements to hide */
  nav [class*="hidden"][class*="md:flex"],
  nav [class*="hidden"][class*="md:block"] {
    display: none !important;
  }
  
  /* Ensure hamburger is visible */
  nav button[aria-label="Toggle menu"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* CRITICAL: Logo always visible */
.navbar-logo,
nav .navbar-logo,
nav a.navbar-logo {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* CRITICAL: Override any conflicting display properties */
nav * {
  box-sizing: border-box;
}

/* Debug helper - remove in production */
nav .hidden.md\\:flex::before {
  content: "Desktop Menu Should Be Visible";
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  color: red;
  background: yellow;
  padding: 2px;
  z-index: 9999;
  display: none; /* Enable for debugging */
}

@media (min-width: 1024px) {nav .hidden.md\\:flex::before {
  display: block;
}
}
