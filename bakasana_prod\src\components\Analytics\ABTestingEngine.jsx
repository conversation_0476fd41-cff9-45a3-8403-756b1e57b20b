'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

// 🚀 ADVANCED A/B TESTING ENGINE
// Enterprise-level experimentation platform for conversion optimization

export default function ABTestingEngine() {
  const pathname = usePathname();
  const [activeTests, setActiveTests] = useState({});
  const [userVariants, setUserVariants] = useState({});
  const [testResults, setTestResults] = useState({});

  // ========================================
  // 1. CURRENT ACTIVE TESTS
  // ========================================

  const ENTERPRISE_TESTS = {
    // Hero Section Optimization
    hero_headline_2025: {
      name: 'Hero Headline Optimization Q1 2025',
      status: 'active',
      trafficSplit: 20, // 20% each variant
      startDate: '2025-01-15',
      endDate: '2025-03-15',
      conversionGoal: 'program_page_visit',
      variants: {
        control: {
          headline:
            'BAKASANA - Najlepsze Retreaty Jogi na Bali i Sri Lanka 2025',
          subheadline:
            'Odkryj transformacyjne podróże z certyfikowaną instruktorką Julią Jakubowicz',
          ctaText: 'Sprawdź Program',
          ctaColor: '#C9A575',
        },
        emotional: {
          headline: 'Znajdź Swoją Wewnętrzną Harmonię w Raju',
          subheadline:
            'Transformacyjne retreaty jogi które zmienią Twoje życie na zawsze',
          ctaText: 'Rozpocznij Podróż',
          ctaColor: '#B8956A',
        },
        urgency: {
          headline: 'Tylko 8 Miejsc na Retreat Dreams w 2025!',
          subheadline:
            'Dołącz do ekskluzywnej grupy na Bali i Sri Lanka - ostatnie miejsca!',
          ctaText: 'Zarezerwuj Teraz',
          ctaColor: '#D4441C',
        },
        social_proof: {
          headline: 'Już 247+ Osób Transformowało Życie z BAKASANA',
          subheadline:
            'Dołącz do społeczności szczęśliwych uczestników naszych retreatów',
          ctaText: 'Zobacz Opinie',
          ctaColor: '#6B7280',
        },
        value_focused: {
          headline: 'Kompletny Retreat Jogi od 485 PLN dziennie',
          subheadline:
            'Wszystko wliczone: zakwaterowanie, wyżywienie, joga, medytacja i zwiedzanie',
          ctaText: 'Sprawdź Cenę',
          ctaColor: '#059669',
        },
      },
      metrics: {
        impressions: 0,
        clicks: 0,
        conversions: 0,
        revenue: 0,
      },
    },

    // Pricing Page Optimization
    pricing_strategy_2025: {
      name: 'Pricing Strategy Optimization',
      status: 'active',
      trafficSplit: 25,
      startDate: '2025-01-01',
      endDate: '2025-02-28',
      conversionGoal: 'booking_form_start',
      variants: {
        control: {
          display: 'single_price',
          format: 'od 3400 PLN',
          emphasis: 'starting_from',
          paymentOptions: 'hidden',
        },
        transparent: {
          display: 'detailed_breakdown',
          format: '3400 PLN (wszystko wliczone)',
          emphasis: 'no_hidden_costs',
          paymentOptions: 'visible',
        },
        comparative: {
          display: 'value_comparison',
          format: '3400 PLN vs 8500 PLN (comparable retreats)',
          emphasis: 'savings',
          paymentOptions: 'prominent',
        },
        installment: {
          display: 'payment_plans',
          format: '3x 1133 PLN lub 6x 567 PLN',
          emphasis: 'affordability',
          paymentOptions: 'multiple',
        },
      },
      metrics: {
        impressions: 0,
        formStarts: 0,
        formCompletions: 0,
        revenue: 0,
      },
    },

    // Testimonials Display
    testimonials_format_2025: {
      name: 'Testimonials Format Test',
      status: 'active',
      trafficSplit: 25,
      startDate: '2025-01-10',
      endDate: '2025-03-10',
      conversionGoal: 'testimonial_engagement',
      variants: {
        control: {
          format: 'text_carousel',
          display: 'horizontal_scroll',
          photos: false,
          videos: false,
        },
        photo_grid: {
          format: 'photo_testimonials',
          display: 'masonry_grid',
          photos: true,
          videos: false,
        },
        video_stories: {
          format: 'video_testimonials',
          display: 'instagram_style',
          photos: true,
          videos: true,
        },
        interactive: {
          format: 'interactive_stories',
          display: 'click_through',
          photos: true,
          videos: true,
        },
      },
      metrics: {
        impressions: 0,
        engagements: 0,
        timeSpent: 0,
        clicks: 0,
      },
    },

    // Contact Form Optimization
    contact_form_2025: {
      name: 'Contact Form Optimization',
      status: 'active',
      trafficSplit: 25,
      startDate: '2025-01-05',
      endDate: '2025-02-15',
      conversionGoal: 'form_completion',
      variants: {
        control: {
          fields: ['name', 'email', 'phone', 'message'],
          layout: 'vertical',
          steps: 1,
          validation: 'on_submit',
        },
        minimal: {
          fields: ['email', 'retreat_interest'],
          layout: 'horizontal',
          steps: 1,
          validation: 'real_time',
        },
        progressive: {
          fields: [
            'name',
            'email',
            'phone',
            'retreat_type',
            'dates',
            'budget',
            'experience',
          ],
          layout: 'wizard',
          steps: 3,
          validation: 'step_by_step',
        },
        conversational: {
          fields: ['dynamic_based_on_answers'],
          layout: 'chat_interface',
          steps: 'variable',
          validation: 'conversational',
        },
      },
      metrics: {
        impressions: 0,
        starts: 0,
        completions: 0,
        abandonmentRate: 0,
      },
    },

    // Mobile Experience
    mobile_experience_2025: {
      name: 'Mobile Experience Optimization',
      status: 'active',
      trafficSplit: 50, // Only for mobile users
      startDate: '2025-01-01',
      endDate: '2025-03-31',
      conversionGoal: 'mobile_conversion',
      deviceTarget: 'mobile',
      variants: {
        control: {
          navigation: 'hamburger_menu',
          hero: 'full_screen',
          cta: 'fixed_bottom',
          contact: 'form_page',
        },
        native_app: {
          navigation: 'bottom_tabs',
          hero: 'card_based',
          cta: 'floating_action',
          contact: 'swipe_up_modal',
        },
      },
      metrics: {
        impressions: 0,
        engagements: 0,
        conversions: 0,
        bounceRate: 0,
      },
    },
  };

  // ========================================
  // 2. USER ASSIGNMENT LOGIC
  // ========================================

  useEffect(() => {
    const assignUserToTests = () => {
      const userId = localStorage.getItem('userId') || generateUserId();
      const deviceType = /Mobi|Android/i.test(navigator.userAgent)
        ? 'mobile'
        : 'desktop';
      const assignments = {};

      Object.entries(ENTERPRISE_TESTS).forEach(([testId, test]) => {
        if (test.status !== 'active') return;

        // Check device targeting
        if (test.deviceTarget && test.deviceTarget !== deviceType) return;

        // Check date range
        const now = new Date();
        const startDate = new Date(test.startDate);
        const endDate = new Date(test.endDate);

        if (now < startDate || now > endDate) return;

        // Assign variant based on user ID hash
        const hash = hashString(userId + testId);
        const variants = Object.keys(test.variants);
        const variantIndex = hash % variants.length;
        const assignedVariant = variants[variantIndex];

        assignments[testId] = {
          variant: assignedVariant,
          config: test.variants[assignedVariant],
        };

        // Track assignment
        trackEvent('ab_test_assignment', {
          testId,
          variant: assignedVariant,
          userId,
        });
      });

      setUserVariants(assignments);
      setActiveTests(ENTERPRISE_TESTS);
    };

    const generateUserId = () => {
      const userId =
        'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
      localStorage.setItem('userId', userId);
      return userId;
    };

    const hashString = str => {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      return Math.abs(hash);
    };

    assignUserToTests();
  }, []);

  // ========================================
  // 3. CONVERSION TRACKING
  // ========================================

  const trackConversion = (testId, goal, value = 1) => {
    if (!userVariants[testId]) return;

    const conversion = {
      testId,
      variant: userVariants[testId].variant,
      goal,
      value,
      timestamp: new Date().toISOString(),
      page: pathname,
      userId: localStorage.getItem('userId'),
    };

    // Track in analytics
    trackEvent('ab_test_conversion', conversion);

    // Store locally for analysis
    const conversions = JSON.parse(
      localStorage.getItem('ab_test_conversions') || '[]'
    );
    conversions.push(conversion);
    localStorage.setItem('ab_test_conversions', JSON.stringify(conversions));

    // Update test metrics
    setTestResults(prev => ({
      ...prev,
      [testId]: {
        ...prev[testId],
        conversions: (prev[testId]?.conversions || 0) + 1,
        revenue: (prev[testId]?.revenue || 0) + value,
      },
    }));
  };

  const trackEvent = (eventName, data) => {
    // Google Analytics
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'AB_Testing',
        event_label: data.testId,
        custom_parameter_1: data.variant,
        custom_parameter_2: data.goal,
        value: data.value,
      });
    }

    // Mixpanel
    if (window.mixpanel) {
      window.mixpanel.track(eventName, data);
    }

    // Facebook Pixel
    if (window.fbq) {
      window.fbq('trackCustom', eventName, data);
    }
  };

  // ========================================
  // 4. REAL-TIME RESULTS MONITORING
  // ========================================

  useEffect(() => {
    const monitorResults = () => {
      Object.entries(activeTests).forEach(([testId, test]) => {
        if (test.status !== 'active') return;

        const conversions = JSON.parse(
          localStorage.getItem('ab_test_conversions') || '[]'
        );
        const testConversions = conversions.filter(c => c.testId === testId);

        const results =
          test.variants &&
          Object.keys(test.variants).reduce((acc, variant) => {
            const variantConversions = testConversions.filter(
              c => c.variant === variant
            );
            acc[variant] = {
              conversions: variantConversions.length,
              revenue: variantConversions.reduce((sum, c) => sum + c.value, 0),
              conversionRate:
                variantConversions.length > 0
                  ? (variantConversions.length / 100) * 100
                  : 0, // Simplified calculation
            };
            return acc;
          }, {});

        setTestResults(prev => ({
          ...prev,
          [testId]: results,
        }));
      });
    };

    // Monitor every 30 seconds
    const interval = setInterval(monitorResults, 30000);
    monitorResults(); // Initial call

    return () => clearInterval(interval);
  }, [activeTests]);

  // ========================================
  // 5. GLOBAL FUNCTIONS
  // ========================================

  useEffect(() => {
    // Make functions available globally
    window.abTest = {
      trackConversion,
      getUserVariant: testId => userVariants[testId],
      getTestResults: testId => testResults[testId],
      getAllTests: () => activeTests,
      getUserVariants: () => userVariants,
    };

    // Auto-track page view conversions
    Object.entries(userVariants).forEach(([testId, assignment]) => {
      if (activeTests[testId]?.conversionGoal === 'page_view') {
        trackConversion(testId, 'page_view', 1);
      }
    });
  }, [userVariants, activeTests, testResults]);

  // ========================================
  // 6. TEST RESULTS ANALYSIS
  // ========================================

  const analyzeTestResults = testId => {
    const results = testResults[testId];
    if (!results) return null;

    const variants = Object.keys(results);
    const analysis = {
      winner: null,
      confidence: 0,
      recommendation: '',
      insights: [],
    };

    // Find best performing variant
    let bestVariant = variants[0];
    let bestRate = results[bestVariant].conversionRate;

    variants.forEach(variant => {
      if (results[variant].conversionRate > bestRate) {
        bestRate = results[variant].conversionRate;
        bestVariant = variant;
      }
    });

    analysis.winner = bestVariant;
    analysis.confidence = calculateConfidence(results);

    // Generate insights
    if (analysis.confidence > 95) {
      analysis.recommendation = `Implement ${bestVariant} variant - statistically significant winner`;
      analysis.insights.push(
        `${bestVariant} outperforms control by ${((bestRate / results.control.conversionRate - 1) * 100).toFixed(1)}%`
      );
    } else if (analysis.confidence > 85) {
      analysis.recommendation = `Continue test - ${bestVariant} shows promise but needs more data`;
    } else {
      analysis.recommendation = 'No clear winner yet - continue testing';
    }

    return analysis;
  };

  const calculateConfidence = results => {
    // Simplified confidence calculation
    const variants = Object.keys(results);
    const totalConversions = variants.reduce(
      (sum, v) => sum + results[v].conversions,
      0
    );

    if (totalConversions < 100) return 0;
    if (totalConversions < 500) return 75;
    if (totalConversions < 1000) return 85;
    return 95;
  };

  // ========================================
  // 7. AUTOMATIC OPTIMIZATION
  // ========================================

  useEffect(() => {
    const autoOptimize = () => {
      Object.entries(activeTests).forEach(([testId, test]) => {
        const analysis = analyzeTestResults(testId);

        if (analysis && analysis.confidence > 95) {
          // Auto-implement winning variant
          const winner = analysis.winner;
          const winnerConfig = test.variants[winner];

          // Store winning configuration
          localStorage.setItem(
            `winning_config_${testId}`,
            JSON.stringify({
              variant: winner,
              config: winnerConfig,
              implementedAt: new Date().toISOString(),
              confidence: analysis.confidence,
            })
          );

          // Track implementation
          trackEvent('ab_test_winner_implemented', {
            testId,
            winner,
            confidence: analysis.confidence,
            totalConversions: Object.values(testResults[testId] || {}).reduce(
              (sum, r) => sum + r.conversions,
              0
            ),
          });
        }
      });
    };

    // Run auto-optimization daily
    const interval = setInterval(autoOptimize, 24 * 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [activeTests, testResults]);

  // Component is invisible - just tracking
  return null;
}

// Helper functions for external use
export const trackConversion = (testId, goal, value) => {
  if (typeof window !== 'undefined' && window.abTest) {
    window.abTest.trackConversion(testId, goal, value);
  }
};

export const getUserVariant = testId => {
  if (typeof window !== 'undefined' && window.abTest) {
    return window.abTest.getUserVariant(testId);
  }
  return null;
};
