/**
 * BAKASANA UNIFIED DESIGN SYSTEM
 * Elegancja <PERSON> + Ciepły minimalizm + Organiczne elementy
 */

/* =============================================
   GLOBAL WARM VARIABLES
   ============================================= */
:root {
  /* Warm Color Palette */
  --warm-sanctuary: #FDFCF8;
  --warm-charcoal: #2A2724;
  --warm-enterprise: #8B7355;
  --warm-terra: #B8935C;
  --warm-sage: #8B8680;
  --warm-stone: #A8A39E;
  
  /* Organic Spacing */
  --organic-xs: 0.5rem;
  --organic-sm: 1rem;
  --organic-md: 1.5rem;
  --organic-lg: 2rem;
  --organic-xl: 3rem;
  --organic-2xl: 4rem;
  
  /* Warm Shadows */
  --shadow-warm-subtle: 0 2px 8px rgba(26, 24, 22, 0.06);
  --shadow-warm-elegant: 0 4px 16px rgba(139, 115, 85, 0.08);
  --shadow-warm-premium: 0 8px 32px rgba(139, 115, 85, 0.12);
  
  /* Organic Transitions */
  --transition-organic: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-warm: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* =============================================
   UNIFIED BASE STYLES
   ============================================= */

/* Smooth scrolling with organic feel */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

/* Body with warm background */
body {
  background-color: var(--warm-sanctuary);
  color: var(--warm-charcoal);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.7;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Selection with warm colors */
::selection {
  background-color: var(--warm-enterprise);
  color: var(--warm-sanctuary);
}::-moz-selection {
  background-color: var(--warm-enterprise);
  color: var(--warm-sanctuary);
}

/* =============================================
   UNIFIED TYPOGRAPHY SYSTEM
   ============================================= */

/* Headings with organic spacing */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cormorant Garamond', Georgia, serif;
  font-weight: 300;
  color: var(--warm-charcoal);
  line-height: 1.2;
  margin-bottom: var(--organic-md);
}

/* Paragraphs with warm spacing */
p {
  margin-bottom: var(--organic-md);
  color: var(--warm-charcoal);
  opacity: 0.9;
}

/* Links with warm hover */
a {
  color: var(--warm-enterprise);
  text-decoration: none;
  transition: var(--transition-organic);
}a:hover {
  color: var(--warm-terra);
  transform: translateY(-1px);
}

/* =============================================
   UNIFIED COMPONENT STYLES
   ============================================= */

/* Buttons with organic feel */
.btn-unified {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: var(--transition-warm);
  cursor: pointer;
  border: none;
  outline: none;
}.btn-unified:hover {
  box-shadow: var(--shadow-warm-elegant);
  transform: translateY(-2px);
}.btn-unified:active {
  transform: translateY(0);
}

/* Cards with warm shadows */
.card-unified {
  background: var(--warm-sanctuary);
  
  box-shadow: var(--shadow-warm-subtle);
  transition: var(--transition-warm);
  overflow: hidden;
}.card-unified:hover {
  box-shadow: var(--shadow-warm-elegant);
  transform: translateY(-4px);
}

/* Inputs with organic styling */
.input-unified {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  background: var(--warm-sanctuary);
  border: 1px solid rgba(139, 115, 85, 0.2);
  color: var(--warm-charcoal);
  transition: var(--transition-organic);
  outline: none;
}.input-unified:focus {
  border-color: var(--warm-enterprise);
  box-shadow: 0 0 0 3px rgba(139, 115, 85, 0.1);
}.input-unified::placeholder {
  color: var(--warm-sage);
  font-weight: 300;
}

/* =============================================
   ORGANIC ANIMATIONS
   ============================================= */

/* Fade in with organic timing */
@keyframes fadeInOrganic {from {
  transform: translateY(20px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}.animate-fade-in-organic {
  animation: fadeInOrganic 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Staggered animations */
.animate-stagger-1 { animation-delay: 0.1s; }.animate-stagger-2 {
  animation-delay: 0.2s;
}.animate-stagger-3 {
  animation-delay: 0.3s;
}.animate-stagger-4 {
  animation-delay: 0.4s;
}

/* Warm pulse animation */
@keyframes warmPulse {0%, 100% {
  transform: scale(1);
  opacity: 0.8;
}50% {
  transform: scale(1.02);
  opacity: 1;
}
}.animate-warm-pulse {
  animation: warmPulse 3s ease-in-out infinite;
}

/* =============================================
   RESPONSIVE ORGANIC SPACING
   ============================================= */

/* Mobile-first organic spacing */
.section-organic {
  padding: var(--organic-2xl) var(--organic-md);
}

@media (min-width: 768px) {.section-organic {
  padding: calc(var(--organic-2xl) * 1.5) var(--organic-lg);
}
}

@media (min-width: 1024px) {
}

/* Container with organic max-width */
.container-organic {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--organic-md);
}

@media (min-width: 768px) {.container-organic {
  padding: 0 var(--organic-lg);
}
}

/* =============================================
   ACCESSIBILITY IMPROVEMENTS
   ============================================= */

/* Focus styles with warm colors */
*:focus-visible {
  outline: 2px solid var(--warm-enterprise);
  outline-offset: 2px;
  
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {*,
  *::before,
  *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}
}

/* High contrast support */
@media (prefers-contrast: high) {:root {
  --warm-charcoal: #000000;
  --warm-enterprise: #8B7355;
  --warm-sanctuary: #FFFFFF;
}
}

/* =============================================
   UTILITY CLASSES
   ============================================= */

/* Warm text colors */
.text-warm-primary { color: var(--warm-charcoal); }.text-warm-secondary {
  color: var(--warm-sage);
}.text-warm-accent {
  color: var(--warm-enterprise);
}.text-warm-muted {
  color: var(--warm-stone);
}

/* Warm backgrounds */
.bg-warm-primary { background-color: var(--warm-sanctuary); }.bg-warm-accent {
  background-color: var(--warm-enterprise);
}.bg-warm-muted {
  background-color: rgba(139, 115, 85, 0.05);
}

/* Organic spacing utilities */
.space-organic-xs { margin: var(--organic-xs); }.space-organic-sm {
  margin: var(--organic-sm);
}.space-organic-md {
  margin: var(--organic-md);
}.space-organic-lg {
  margin: var(--organic-lg);
}.space-organic-xl {
  margin: var(--organic-xl);
}

/* Warm shadows */
.shadow-warm-subtle { box-shadow: var(--shadow-warm-subtle); }.shadow-warm-elegant {
  box-shadow: var(--shadow-warm-elegant);
}.shadow-warm-premium {
  box-shadow: var(--shadow-warm-premium);
}

/* Organic borders */
.border-warm { border-color: rgba(139, 115, 85, 0.2); }.border-warm-strong {
  border-color: var(--warm-enterprise);
}

/* Smooth transitions */
.transition-organic { transition: var(--transition-organic); }.transition-warm {
  transition: var(--transition-warm);
}