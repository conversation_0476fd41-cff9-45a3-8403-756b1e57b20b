/* =============================================
   📝 BAKASANA ADVANCED TYPOGRAPHY SYSTEM
   Perfect 10/10 Responsive Typography
   ============================================= */

/* ===== ADVANCED FLUID TYPOGRAPHY ===== */
:root {
  /* Perfect fluid typography scale using modern CSS */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.375rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.625rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3.25rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4.5rem);
  --text-6xl: clamp(4rem, 3rem + 5vw, 7rem);
  --text-7xl: clamp(5rem, 4rem + 8vw, 10rem);
  
  /* Advanced line height system */
  --leading-none: 1;
  --leading-tight: 1.1;
  --leading-snug: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Responsive letter spacing */
  --tracking-tighter: clamp(-0.05em, -0.02vw, -0.025em);
  --tracking-tight: clamp(-0.025em, -0.01vw, -0.0125em);
  --tracking-normal: 0;
  --tracking-wide: clamp(0.025em, 0.01vw, 0.05em);
  --tracking-wider: clamp(0.05em, 0.02vw, 0.1em);
  --tracking-widest: clamp(0.1em, 0.05vw, 0.25em);
}

/* ===== RESPONSIVE TYPOGRAPHY CLASSES ===== */

/* Fluid headings */
.heading-display {
  font-size: var(--text-7xl);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-tighter);
  font-weight: 200;
}.heading-hero {
  font-size: var(--text-6xl);
  font-weight: 300;
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
}.heading-section {
  font-size: var(--text-5xl);
  font-weight: 300;
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-normal);
}.heading-subsection {
  font-size: var(--text-4xl);
  font-weight: 400;
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-normal);
}.heading-card {
  font-size: var(--text-3xl);
  font-weight: 400;
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-normal);
}.heading-small {
  font-size: var(--text-2xl);
  font-weight: 500;
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

/* Fluid body text */
.text-lead {
  font-size: var(--text-xl);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  font-weight: 300;
}.text-body {
  font-size: var(--text-base);
  font-weight: 400;
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
}.text-small {
  font-size: var(--text-sm);
  font-weight: 400;
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}.text-caption {
  font-size: var(--text-xs);
  font-weight: 500;
  line-height: var(--leading-normal);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
}

/* ===== RESPONSIVE TYPOGRAPHY UTILITIES ===== */

/* Responsive margins */
.text-spacing-tight {
  margin-bottom: clamp(0.5rem, 2vw, 1rem);
}.text-spacing-normal {
  margin-bottom: clamp(1rem, 3vw, 1.5rem);
}.text-spacing-relaxed {
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
}.text-spacing-loose {
  margin-bottom: clamp(2rem, 5vw, 3.5rem);
}

/* Responsive text alignment */
.text-responsive-center {
  text-align: center;
}

@media (min-width: 768px) {.text-responsive-left {
  text-align: left;
}.text-responsive-right {
  text-align: right;
}
}

/* ===== ADVANCED RESPONSIVE RULES ===== */

/* Container-based typography */
@supports (container-type: inline-size) {.typography-container {
  container-type: inline-size;
}
  
  @container (max-width: 400px) {.typography-container .heading-hero {
  font-size: clamp(2rem, 8vw, 3rem);
  line-height: 1.1;
}.typography-container .text-lead {
  font-size: clamp(1rem, 4vw, 1.125rem);
}
  }
  
  @container (min-width: 401px) and (max-width: 800px) {
  }
  
  @container (min-width: 801px) {
  }
}

/* ===== DEVICE-SPECIFIC OPTIMIZATIONS ===== */

/* Mobile typography optimizations */
@media (max-width: 480px) {:root {
  --text-3xl: clamp(1.5rem, 5vw, 2rem);
  --text-4xl: clamp(1.75rem, 6vw, 2.5rem);
  --text-5xl: clamp(2rem, 8vw, 3rem);
  --text-6xl: clamp(2.5rem, 10vw, 4rem);
}
  
  /* Tighter line heights on mobile */
  .heading-display,
  .heading-hero,
  .heading-section {
    line-height: var(--leading-tight);
  }
  
  /* Reduced letter spacing on mobile */
  .heading-display,
  .heading-hero {
    letter-spacing: var(--tracking-normal);
  }
}

/* Tablet typography optimizations */
@media (min-width: 481px) and (max-width: 1024px) {
}

/* Large screen optimizations */
@media (min-width: 1440px) {
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Respect user font size preferences */
@media (prefers-reduced-motion: reduce) {* {
  transition: none !important;
  animation: none !important;
}
}

/* High contrast mode */
@media (prefers-contrast: high) {.heading-display,
  .heading-hero,
  .heading-section,
  .heading-subsection,
  .heading-card,
  .heading-small {
  font-weight: 600;
  text-shadow: none;
}
}

/* Forced colors mode */
@media (forced-colors: active) {.heading-display,
  .heading-hero,
  .heading-section {
  color: CanvasText;
}
}

/* ===== READING EXPERIENCE OPTIMIZATIONS ===== */

/* Optimal reading width */
.reading-width {
  max-width: clamp(45ch, 75vw, 75ch);
  margin-left: auto;
  margin-right: auto;
}

/* Improved readability */
.reading-optimized {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== PRINT TYPOGRAPHY ===== */
@media print {.text-lead,
  .text-body {
  orphans: 2;
  widows: 2;
}
}

/* ===== FUTURE-PROOF FEATURES ===== */

/* CSS Fonts Level 4 - Variable fonts support */
@supports (font-variation-settings: normal) {.variable-font {
  font-variation-settings: "wght" 400,
      "slnt" 0,
      "ital" 0;
}.variable-font-light {
  font-variation-settings: "wght" 300,
      "slnt" 0,
      "ital" 0;
}.variable-font-bold {
  font-variation-settings: "wght" 600,
      "slnt" 0,
      "ital" 0;
}
}

/* CSS Text Level 4 - Text spacing */
@supports (text-spacing: trim-start) {.text-spacing-optimized {
  text-spacing: trim-start;
}
}