/* =============================================
   🎯 BAKASANA - AWARD-WINNING MICROINTERACTIONS
   Inspired by Apple.com, Stripe.com, Linear.app
   ============================================= */

/* ===== PREMIUM ANIMATION SYSTEM ===== */
:root {
  /* 🎯 UNIFIED TIMING SCALE - Optimized for 9/10 UX */
  --duration-instant: 150ms;    /* Micro-interactions: hover, focus, ripples */
  --duration-quick: 250ms;      /* Small UI changes: button states, tooltips */
  --duration-medium: 350ms;     /* Medium transitions: modal open/close, cards */
  --duration-slow: 500ms;       /* Large transitions: page changes, complex animations */
  --duration-extended: 750ms;   /* Special effects: parallax, complex reveals */

  /* 🌊 PREMIUM EASING FUNCTIONS - Natural motion curves */
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);      /* Smooth, natural */
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);    /* Playful bounce */
  --ease-swift: cubic-bezier(0.4, 0, 0.2, 1);               /* Quick, decisive */
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);  /* Elastic feel */
  --ease-premium: cubic-bezier(0.16, 1, 0.3, 1);            /* Premium feel */

  /* 🎭 TRANSFORM ORIGINS */
  --origin-center: center center;
  --origin-top: center top;
  --origin-bottom: center bottom;
  --origin-left: left center;
  --origin-right: right center;

  /* 🎨 ANIMATION STATES */
  --scale-hover: 1.02;
  --scale-active: 0.98;
  --scale-focus: 1.01;
}

/* ===== BUTTON MICROINTERACTIONS ===== */
.btn-primary {position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all var(--duration-quick) var(--ease-premium);
  will-change: transform, box-shadow, background-color;
  
  /* 3D hover effect */
  &:hover {
  background: linear-gradient(135deg, #C4996B 0%, #B8935C 100%);
  box-shadow: 0 10px 25px rgba(184, 147, 92, 0.25),
      0 6px 12px rgba(184, 147, 92, 0.15);
  transform: translateY(-2px) scale(var(--scale-hover));
}
  
  /* Active state with spring back */
  &:active {
    transform: translateY(0px) scale(var(--scale-active));
    transition-duration: var(--duration-instant);
  }
  
  /* Premium ripple effect */
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                               rgba(255, 255, 255, 0.15) 0%,
                               transparent 70%);
    opacity: 0;
    transition: opacity var(--duration-quick) var(--ease-premium);
    pointer-events: none;
  }

  /* Focus state with premium ring */
  &:focus-visible {
    outline: none;
    box-shadow:
      0 0 0 3px rgba(196, 153, 107, 0.3),
      0 0 0 6px rgba(196, 153, 107, 0.1);
    transform: scale(var(--scale-focus));
  }&:hover::before {
  opacity: 1;
}
  
  /* Ripple effect */
  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    
    transform: scale(0);
    pointer-events: none;
    transition: transform var(--entrance-duration) var(--spring-elastic);
  }&:focus::after {
  width: 100%;
  height: 100%;
  transform: scale(1);
}
}

/* ===== CARD MICROINTERACTIONS ===== */
.card-interactive {position: relative;
  transform: translateZ(0);
  transition: all var(--duration-medium) var(--ease-premium);
  will-change: transform, box-shadow;
  
  /* Subtle lift on hover */
  &:hover {
  box-shadow: 0 20px 40px rgba(42, 39, 36, 0.08),
      0 10px 20px rgba(42, 39, 36, 0.06);
  transform: translateY(-8px) scale(1.01);
}
  
  /* Simplified 2D tilt effect - no 3D transforms */
  &.tilt-active {
    transition: transform var(--micro-duration) var(--spring-smooth);
  }
  
  /* Glowing border on focus */
  &:focus-within {
    outline: none;
    box-shadow: 
      0 0 0 2px rgba(184, 147, 92, 0.2),
      0 0 0 4px rgba(184, 147, 92, 0.1),
      0 20px 40px rgba(42, 39, 36, 0.08);
  }
}

/* ===== LINK MICROINTERACTIONS ===== */
.link-enhanced {position: relative;
  color: var(--temple-gold);
  text-decoration: none;
  overflow: hidden;
  transition: color var(--standard-duration) var(--spring-smooth);
  
  /* Animated underline */
  &::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--temple-gold), var(--golden-amber));
  transition: width var(--standard-duration) var(--spring-smooth);
  content: '';
}&:hover::after {
  width: 100%;
}
  
  /* Shine effect */
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 70%
    );
    transform: translateX(-100%);
    transition: transform var(--entrance-duration) var(--spring-smooth);
  }
}

/* ===== FORM MICROINTERACTIONS ===== */
.form-field {
  position: relative;
  
  input, textarea {transition: all var(--standard-duration) var(--spring-smooth);
    will-change: border-color, box-shadow, background-color;
    
    &:focus {
  border-color: var(--temple-gold);
  background-color: rgba(253, 252, 248, 0.8);
  box-shadow: 0 0 0 3px rgba(184, 147, 92, 0.1),
        0 4px 12px rgba(184, 147, 92, 0.08);
  outline: none;
}&:invalid {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}
  }
  
  /* Floating labels */
  label {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--stone);
    pointer-events: none;
    transition: all var(--standard-duration) var(--spring-smooth);
    background: linear-gradient(to bottom, 
                               transparent 0%, 
                               transparent 40%, 
                               var(--sanctuary) 50%, 
                               var(--sanctuary) 100%);
    padding: 0 4px;
  }input:focus + label,
  input:not(:placeholder-shown) + label {
  top: 0;
  color: var(--temple-gold);
  font-size: 12px;
  transform: translateY(-50%);
}
}

/* ===== SCROLL REVEAL ANIMATIONS ===== */
.reveal-on-scroll {opacity: 0;
  transform: translateY(30px);
  transition: all var(--entrance-duration) var(--spring-smooth);
  will-change: opacity, transform;
  
  &.revealed {
  transform: translateY(0);
  opacity: 1;
}
}

.reveal-stagger {opacity: 0;
  transform: translateY(20px);
  transition: all var(--entrance-duration) var(--spring-smooth);
  will-change: opacity, transform;
  
  &.revealed {
  transform: translateY(0);
  opacity: 1;
}
}

/* Staggered delays for multiple elements */
.reveal-stagger:nth-child(1) { transition-delay: 0s; }.reveal-stagger:nth-child(2) {
  transition-delay: 0.1s;
}.reveal-stagger:nth-child(3) {
  transition-delay: 0.2s;
}.reveal-stagger:nth-child(4) {
  transition-delay: 0.3s;
}.reveal-stagger:nth-child(5) {
  transition-delay: 0.4s;
}

/* ===== MAGNETIC CURSOR EFFECTS ===== */
.magnetic-element {
  position: relative;
  cursor: none;
  transition: transform var(--micro-duration) var(--spring-smooth);
  will-change: transform;
}

.cursor-follower {position: fixed;
  width: 20px;
  height: 20px;
  background: var(--temple-gold);
  
  pointer-events: none;
  z-index: 9999;
  transition: transform var(--micro-duration) var(--spring-smooth);
  will-change: transform;
  
  &.expanded {
  background: rgba(184, 147, 92, 0.3);
  transform: scale(2);
}
}

/* ===== GLASSMORPHISM EFFECTS ===== */
.glass-card {background: rgba(253, 252, 248, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all var(--standard-duration) var(--spring-smooth);
  will-change: background, backdrop-filter;
  
  &:hover {
  background: rgba(253, 252, 248, 0.9);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
}
}

/* ===== LOADING ANIMATIONS ===== */
.loading-shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    90deg,
    var(--whisper) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    var(--whisper) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {0% {
  background-position: -200% 0;
}100% {
  background-position: 200% 0;
}
}

/* ===== PARALLAX SCROLL EFFECTS ===== */
.parallax-container {
  overflow: hidden;
  position: relative;
}.parallax-layer {
  position: absolute;
  transition: transform var(--micro-duration) linear;
  inset: 0;
  will-change: transform;
}.parallax-slow {
  transform: translateY(var(--parallax-slow, 0));
}.parallax-medium {
  transform: translateY(var(--parallax-medium, 0));
}.parallax-fast {
  transform: translateY(var(--parallax-fast, 0));
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {*,
  *::before,
  *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  scroll-behavior: auto !important;
  transition-duration: 0.01ms !important;
}
}

/* ===== FOCUS INDICATORS ===== */
.focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  
}

/* ===== SMOOTH SCROLLING ===== */
html {
  scroll-behavior: smooth;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}.will-change-opacity {
  will-change: opacity;
}.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ADVANCED LOADING ANIMATIONS ===== */
@keyframes fadeInUp {from {
  transform: translateY(30px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}

@keyframes fadeInScale {
}

@keyframes slideInRight {
}

@keyframes bounceIn {50% {
  transform: scale(1.05);
  opacity: 1;
}70% {
  transform: scale(0.9);
}
}

@keyframes float {0%, 100% {
  transform: translateY(0px);
}
}

/* ===== UTILITY ANIMATION CLASSES ===== */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}.animate-bounce-in {
  animation: bounceIn 0.6s ease-out;
}.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* ===== ENHANCED TOUCH INTERACTIONS ===== */
.touch-feedback {position: relative;
  overflow: hidden;

  &::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  content: '';
  pointer-events: none;
}&:active::after {
  width: 200px;
  height: 200px;
}
}

/* ===== PROGRESSIVE ENHANCEMENT ===== */
@supports (backdrop-filter: blur(10px)) {.enhanced-glass {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}
}

/* ===== PREMIUM INTERACTIVE ELEMENTS ===== */

/* Enhanced unified card with premium interactions */
.unified-card-premium {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-medium) var(--ease-premium);
  will-change: transform, box-shadow;
  cursor: pointer;
}.unified-card-premium:hover {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12),
    0 12px 24px rgba(0, 0, 0, 0.08);
  transform: translateY(-6px) scale(var(--scale-hover));
}.unified-card-premium:active {
  transform: translateY(-3px) scale(var(--scale-active));
  transition-duration: var(--duration-instant);
}.unified-card-premium:focus-visible {
  box-shadow: 0 0 0 3px rgba(196, 153, 107, 0.3),
    0 25px 50px rgba(0, 0, 0, 0.12);
  outline: none;
}

/* Premium link with animated underline */
.link-premium {
  position: relative;
  transition: all var(--duration-quick) var(--ease-premium);
  text-decoration: none;
  display: inline-block;
}.link-premium::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--golden), var(--sunset));
  transition: width var(--duration-medium) var(--ease-elastic);
  content: '';
}.link-premium:hover {
  color: var(--golden);
  transform: translateY(-1px);
}.link-premium:hover::after {
  width: 100%;
}.link-premium:focus-visible {
  color: var(--golden);
  transform: scale(var(--scale-focus));
  outline: none;
}

/* Premium button ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}.btn-ripple::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--duration-medium) var(--ease-elastic),
              height var(--duration-medium) var(--ease-elastic);
  content: '';
}.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

@supports (container-type: inline-size) {.container-animations {
  container-type: inline-size;
}

  @container (max-width: 400px) {.container-animations .animate-fade-in-up {
  animation-duration: var(--duration-medium);
}
  }
}