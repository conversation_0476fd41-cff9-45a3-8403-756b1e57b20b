/* Enhanced BAKASANA Design System - World-Class Visual Identity */
/* Fonts loaded via Next.js font optimization system */
/* @import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,200;0,300;0,400;0,500;0,600;1,200;1,300;1,400;1,500&family=Inter:wght@100;200;300;400;500;600;700&display=swap'); */

:root {
  /* Enhanced Brand Colors - Spiritual & Sophisticated */
  --golden-lotus: #C9A575;
  --deep-golden: #B8956A;
  --light-golden: #D4B685;
  --sanctuary: #FDFCF8;
  --soft-sanctuary: #F9F7F2;
  --warm-sanctuary: #F5F3EF;
  --charcoal: #3A3A3A;
  --soft-charcoal: #5A5A5A;
  --light-charcoal: #8A8A8A;
  --mist: #E8E6E2;
  --soft-mist: #F0EFEB;
  --sage: #8B9A8C;
  --soft-sage: #A8B4A9;
  --temple: #D4AF37;
  --soft-temple: #E6C65B;
  
  /* Enhanced Typography */
  --font-primary: 'Cormorant Garamond', serif;
  --font-secondary: 'Inter', sans-serif;
  --font-accent: 'Playfair Display', serif;
  
  /* Enhanced Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;
  
  /* Enhanced Shadows */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-soft: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-medium: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-golden: 0 8px 25px rgba(201, 165, 117, 0.3);
  
  /* Enhanced Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Enhanced Borders */
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;
}

/* Enhanced Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}html {
  font-size: 16px;
  scroll-behavior: smooth;
}body {
  overflow-x: hidden;
  background: var(--sanctuary);
  color: var(--charcoal);
  font-family: var(--font-secondary);
  line-height: 1.6;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

/* Enhanced Typography System */
.text-brand-primary {
  font-family: var(--font-primary);
  font-weight: 400;
  letter-spacing: 0.025em;
}.text-brand-secondary {
  font-family: var(--font-secondary);
  font-weight: 400;
}.text-brand-accent {
  font-family: var(--font-accent);
  font-weight: 400;
}

/* Enhanced Heading System */
.heading-hero {
  font-family: var(--font-primary);
  font-size: clamp(3rem, 8vw, 7.5rem);
  font-weight: 200;
  letter-spacing: 0.25em;
  line-height: 0.9;
  color: var(--sanctuary);
  text-transform: uppercase;
  margin-bottom: var(--space-lg);
}.heading-section {
  margin-bottom: var(--space-xl);
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: 0.1em;
}.heading-subsection {
  margin-bottom: var(--space-lg);
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 400;
  line-height: 1.3;
  letter-spacing: 0.05em;
}.heading-card {
  margin-bottom: var(--space-md);
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-size: clamp(1.25rem, 2vw, 1.75rem);
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0.025em;
}

/* Enhanced Button System */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-2xl);
  background: var(--golden-lotus);
  color: var(--sanctuary);
  border: none;
  
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.025em;
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  box-shadow: var(--shadow-soft);
  position: relative;
  overflow: hidden;
}.btn-primary:hover {
  background: var(--deep-golden);
  box-shadow: var(--shadow-golden);
  transform: translateY(-2px);
}.btn-primary::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
  content: '';
}.btn-primary:hover::before {
  left: 100%;
}.btn-secondary {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: var(--space-md) var(--space-2xl);
  border: 2px solid var(--golden-lotus);
  background: transparent;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  letter-spacing: 0.025em;
}.btn-secondary:hover {
  border-color: var(--deep-golden);
  box-shadow: var(--shadow-soft);
  color: var(--sanctuary);
  transform: translateY(-2px);
}.btn-secondary::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 0;
  height: 100%;
  background: var(--golden-lotus);
  transition: width var(--transition-normal);
  content: '';
}.btn-secondary:hover::before {
  width: 100%;
}.btn-ghost {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-sm) var(--space-lg);
  border: 1px solid transparent;
  background: transparent;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-size: 0.875rem;
  font-weight: 400;
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  letter-spacing: 0.025em;
}.btn-ghost:hover {
  border-color: var(--golden-lotus);
  color: var(--golden-lotus);
}.btn-ghost::after {
  margin-left: var(--space-sm);
  transition: transform var(--transition-normal);
  content: '→';
}.btn-ghost:hover::after {
  transform: translateX(4px);
}

/* Enhanced Card System */
.card-elevated {
  background: var(--sanctuary);
  
  padding: var(--space-2xl);
  box-shadow: var(--shadow-medium);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}.card-elevated:hover {
  box-shadow: var(--shadow-large);
  transform: translateY(-8px);
}.card-elevated::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--golden-lotus), var(--temple));
  transition: opacity var(--transition-normal);
  opacity: 0;
  content: '';
}.card-elevated:hover::before {
  opacity: 1;
}.card-minimal {
  padding: var(--space-xl);
  border: 1px solid var(--mist);
  background: var(--soft-sanctuary);
  transition: all var(--transition-normal);
}.card-minimal:hover {
  border-color: var(--golden-lotus);
  background: var(--sanctuary);
  box-shadow: var(--shadow-soft);
  transform: translateY(-2px);
}

/* Enhanced Section Dividers */
.section-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--space-5xl) 0;
  position: relative;
}.section-divider::before {
  position: absolute;
  right: 0;
  left: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--mist), transparent);
  content: '';
}.section-divider-diamond {
  position: relative;
  z-index: 1;
  width: 20px;
  height: 20px;
  background: var(--golden-lotus);
  transform: rotate(45deg);
}.section-divider-diamond::before {
  position: absolute;
  top: 4px;
  right: 4px;
  bottom: 4px;
  left: 4px;
  background: var(--sanctuary);
  content: '';
}

/* Enhanced Animations */
@keyframes fadeInUp {from {
  transform: translateY(30px);
  opacity: 0;
}to {
  transform: translateY(0);
  opacity: 1;
}
}

@keyframes fadeInScale {
}

@keyframes float {0%, 100% {
  transform: translateY(0px);
}50% {
  transform: translateY(-10px);
}
}

@keyframes shimmer {0% {
  background-position: -1000px 0;
}100% {
  background-position: 1000px 0;
}
}.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}.animate-float {
  animation: float 3s ease-in-out infinite;
}.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {.heading-hero {
  font-size: clamp(2rem, 12vw, 4rem);
  letter-spacing: 0.15em;
}.btn-primary,
  .btn-secondary {
  padding: var(--space-sm) var(--space-lg);
  font-size: 0.875rem;
}.card-elevated {
  padding: var(--space-lg);
}.section-divider {
  margin: var(--space-3xl) 0;
}
}

/* Enhanced Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: 9999;
  padding: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  text-decoration: none;
  transition: top var(--transition-normal);
}.skip-link:focus {
  top: 6px;
}

/* Enhanced Focus States */
*:focus {
  outline: 2px solid var(--golden-lotus);
  outline-offset: 2px;
}button:focus,
a:focus {
  outline: 2px solid var(--golden-lotus);
  outline-offset: 2px;
}

/* Enhanced Print Styles */
@media print {* {
  background: transparent !important;
  box-shadow: none !important;
  color: black !important;
  text-shadow: none !important;
}.btn-primary,
  .btn-secondary,
  .btn-ghost {
  border: 1px solid black;
  background: transparent;
  color: black;
}
}

/* Enhanced Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}.will-change-transform {
  will-change: transform;
}.will-change-opacity {
  will-change: opacity;
}

/* Enhanced Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--mist) 25%, var(--soft-mist) 50%, var(--mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  
}.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
}

/* Enhanced Scroll Behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}.scroll-padding {
  scroll-padding-top: 80px;
}