import Link from 'next/link';
import React from 'react';

import { Home } from '@/components/ui/UnifiedButton';
import { Icon } from '@/components/ui/IconSystem';
import { Icon } from '@/components/ui/IconSystem';

import { footerLinks } from '@/data/navigationLinks';
import { socialLinks, studioInfo } from '@/data/contactData';

export default function ServerFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      id='footer'
      className='pt-48 pb-12 bg-whisper' // Bardzo jasny beż #F9F7F2
    >
      <div className='max-w-7xl mx-auto px-hero-padding sm:px-12 lg:px-16'>
        {/* Duchowe pozdrowienie */}
        <div className='text-center mb-2xl'>
          <div className='text-stone/60 text-sm font-light tracking-wide'>
            <span className='text-lg opacity-60'>ॐ</span>
            <span className='mx-3'>Om <PERSON>wastiastu</span>
            <span className='text-lg opacity-60'>ॐ</span>
          </div>
          <p className='text-stone/50 text-xs font-light mt-2 tracking-wide'>
            Niech pokój będzie z Tobą
          </p>
        </div>

        {/* Minimalistyczny layout - maksymalnie 4 linki 11px #8A8A8A */}
        <div className='text-center mb-xl'>
          <nav aria-label='Nawigacja w stopce'>
            <ul className='flex justify-center items-center gap-lg'>
              {footerLinks.slice(0, 4).map(
                (
                  link // Maksymalnie 4 linki
                ) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className='text-stone hover:opacity-70 transition-opacity text-xs font-light tracking-wide' // 11px #8A8A8A
                      style={{ fontSize: '11px' }}
                    >
                      {link.label}
                    </Link>
                  </li>
                )
              )}
            </ul>
          </nav>
        </div>

        {/* Małe delikatne ikony social */}
        <div className='flex justify-center items-center gap-md mb-xl'>
          <a
            href='https://www.instagram.com/fly_with_bakasana'
            target='_blank'
            rel='noopener noreferrer'
            className='text-stone/60 hover:opacity-70 transition-opacity'
          >
            <span className='text-xs'>Instagram</span>
          </a>
          <span className='w-px h-3 bg-stone/30'></span>
          <a
            href='mailto:<EMAIL>'
            className='text-stone/60 hover:opacity-70 transition-opacity'
          >
            <span className='text-xs'>Email</span>
          </a>
        </div>

        {/* Prawie niewidoczny copyright 10px */}
        <div className='text-center'>
          <p
            className='text-stone/40 font-light tracking-wide'
            style={{ fontSize: '10px' }}
          >
            © {currentYear} Bakasana
          </p>
        </div>
      </div>
    </footer>
  );
}
