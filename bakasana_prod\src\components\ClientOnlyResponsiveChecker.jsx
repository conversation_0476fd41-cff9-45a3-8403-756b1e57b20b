'use client';

import { useState, useEffect } from 'react';
const ClientOnlyResponsiveChecker = () => {
  const [mounted, setMounted] = useState(false);
  const [screenInfo, setScreenInfo] = useState({
    width: 0,
    height: 0,
    breakpoint: '',
    orientation: '',
    pixelRatio: 1,
  });
  const [isVisible, setIsVisible] = useState(false);

  // Ensure component only renders on client
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    const updateScreenInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      let breakpoint = '';
      if (width < 640) breakpoint = 'Mobile (< 640px)';
      else if (width < 768) breakpoint = 'SM (640px - 767px)';
      else if (width < 1024) breakpoint = 'MD (768px - 1023px)';
      else if (width < 1280) breakpoint = 'LG (1024px - 1279px)';
      else if (width < 1536) breakpoint = 'XL (1280px - 1535px)';
      else breakpoint = '2XL (≥ 1536px)';

      const orientation = width > height ? 'Landscape' : 'Portrait';
      const pixelRatio = window.devicePixelRatio || 1;

      setScreenInfo({
        width,
        height,
        breakpoint,
        orientation,
        pixelRatio,
      });
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);

    return () => window.removeEventListener('resize', updateScreenInfo);
  }, [mounted]);

  // Keyboard shortcut
  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    const handleKeyPress = e => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'r' && e.shiftKey) {
        e.preventDefault();
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [mounted, isVisible]);

  // Don't render on server or in production
  if (!mounted || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className='fixed bottom-4 right-4 z-50 bg-charcoal text-white p-2 rounded-full shadow-lg hover:bg-charcoal/80 transition-colors'
        title='Toggle Responsive Checker (Ctrl+Shift+R)'
      >
        📱
      </button>

      {/* Responsive Info Panel */}
      {isVisible && (
        <div className='fixed top-4 right-4 z-50 bg-white border border-stone/20 rounded-lg shadow-xl p-4 min-w-[280px] font-mono text-sm'>
          <div className='flex justify-between items-center mb-3'>
            <h3 className='font-bold text-charcoal'>Responsive Debug</h3>
            <button
              onClick={() => setIsVisible(false)}
              className='text-stone hover:text-charcoal'
            >
              ✕
            </button>
          </div>

          <div className='space-y-2 text-xs'>
            <div className='flex justify-between'>
              <span className='text-stone'>Screen:</span>
              <span className='font-bold'>
                {screenInfo.width} × {screenInfo.height}
              </span>
            </div>

            <div className='flex justify-between'>
              <span className='text-stone'>Breakpoint:</span>
              <span className='font-bold text-charcoal-gold'>
                {screenInfo.breakpoint}
              </span>
            </div>

            <div className='flex justify-between'>
              <span className='text-stone'>Orientation:</span>
              <span className='font-bold'>{screenInfo.orientation}</span>
            </div>

            <div className='flex justify-between'>
              <span className='text-stone'>Pixel Ratio:</span>
              <span className='font-bold'>{screenInfo.pixelRatio}x</span>
            </div>

            <div className='border-t border-stone/20 pt-2 mt-3'>
              <div className='text-stone text-xs'>
                <div>
                  Touch Target:{' '}
                  {screenInfo.width < 768 ? '44px min' : '40px min'}
                </div>
                <div>
                  Font Scale: {screenInfo.width < 768 ? 'Mobile' : 'Desktop'}
                </div>
              </div>
            </div>
          </div>

          {/* Breakpoint Indicators */}
          <div className='mt-4 pt-3 border-t border-stone/20'>
            <div className='text-xs text-stone mb-2'>Breakpoints:</div>
            <div className='grid grid-cols-2 gap-1 text-xs'>
              <div
                className={`p-1 rounded text-center ${screenInfo.width < 640 ? 'bg-red-100 text-red-800' : 'bg-gray-100'}`}
              >
                SM
              </div>
              <div
                className={`p-1 rounded text-center ${screenInfo.width >= 640 && screenInfo.width < 768 ? 'bg-orange-100 text-orange-800' : 'bg-gray-100'}`}
              >
                MD
              </div>
              <div
                className={`p-1 rounded text-center ${screenInfo.width >= 768 && screenInfo.width < 1024 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100'}`}
              >
                LG
              </div>
              <div
                className={`p-1 rounded text-center ${screenInfo.width >= 1024 ? 'bg-green-100 text-green-800' : 'bg-gray-100'}`}
              >
                XL
              </div>
            </div>
          </div>

          {/* Quick Tests */}
          <div className='mt-4 pt-3 border-t border-stone/20'>
            <div className='text-xs text-stone mb-2'>Quick Checks:</div>
            <div className='space-y-1 text-xs'>
              <div
                className={`flex justify-between ${screenInfo.width < 768 ? 'text-red-600' : 'text-green-600'}`}
              >
                <span>Mobile Menu:</span>
                <span>{screenInfo.width < 768 ? '✓ Active' : '✗ Hidden'}</span>
              </div>
              <div
                className={`flex justify-between ${screenInfo.width >= 1024 ? 'text-green-600' : 'text-orange-600'}`}
              >
                <span>Desktop Layout:</span>
                <span>
                  {screenInfo.width >= 1024 ? '✓ Active' : '✗ Stacked'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ClientOnlyResponsiveChecker;
